package csv

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/berrijam/mulberri/internal/types"
)

func TestWriteSimpleCSV(t *testing.T) {
	tests := []struct {
		name           string
		predictions    []string
		includeHeaders bool
		expectedLines  []string
	}{
		{
			name:           "simple predictions with headers",
			predictions:    []string{"yes", "no", "yes"},
			includeHeaders: true,
			expectedLines:  []string{"prediction", "yes", "no", "yes"},
		},
		{
			name:           "simple predictions without headers",
			predictions:    []string{"yes", "no", "yes"},
			includeHeaders: false,
			expectedLines:  []string{"yes", "no", "yes"},
		},
		{
			name:           "empty predictions with headers",
			predictions:    []string{},
			includeHeaders: true,
			expectedLines:  []string{"prediction"},
		},
		{
			name:           "empty predictions without headers",
			predictions:    []string{},
			includeHeaders: false,
			expectedLines:  []string{},
		},
		{
			name:           "single prediction with headers",
			predictions:    []string{"maybe"},
			includeHeaders: true,
			expectedLines:  []string{"prediction", "maybe"},
		},
		{
			name:           "predictions with commas",
			predictions:    []string{"class,with,commas", "class\"with\"quotes"},
			includeHeaders: true,
			expectedLines:  []string{"prediction", "\"class,with,commas\"", "\"class\"\"with\"\"quotes\""},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create temporary file
			tempDir := t.TempDir()
			outputPath := filepath.Join(tempDir, "test_simple.csv")

			// Write predictions
			err := WriteSimpleCSV(tt.predictions, outputPath, tt.includeHeaders)
			if err != nil {
				t.Fatalf("WriteSimpleCSV failed: %v", err)
			}

			// Verify file exists
			if _, err := os.Stat(outputPath); os.IsNotExist(err) {
				t.Fatalf("Output file was not created: %s", outputPath)
			}

			// Read and verify content
			content, err := os.ReadFile(outputPath)
			if err != nil {
				t.Fatalf("Failed to read output file: %v", err)
			}

			lines := strings.Split(strings.TrimSpace(string(content)), "\n")

			// Handle empty case
			if len(tt.expectedLines) == 0 {
				if len(lines) == 1 && lines[0] == "" {
					return // Empty file is correct
				}
				t.Fatalf("Expected empty file, got %d lines: %v", len(lines), lines)
			}

			if len(lines) != len(tt.expectedLines) {
				t.Fatalf("Expected %d lines, got %d lines. Content: %s", len(tt.expectedLines), len(lines), string(content))
			}

			for i, expectedLine := range tt.expectedLines {
				if lines[i] != expectedLine {
					t.Errorf("Line %d: expected %q, got %q", i, expectedLine, lines[i])
				}
			}
		})
	}
}

func TestWriteSimpleCSV_ErrorCases(t *testing.T) {
	tests := []struct {
		name        string
		outputPath  string
		predictions []string
		wantErr     bool
	}{
		{
			name:        "invalid output directory",
			outputPath:  "/nonexistent/directory/output.csv",
			predictions: []string{"yes", "no"},
			wantErr:     true,
		},
		{
			name:        "empty output path",
			outputPath:  "",
			predictions: []string{"yes", "no"},
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := WriteSimpleCSV(tt.predictions, tt.outputPath, true)
			if (err != nil) != tt.wantErr {
				t.Errorf("WriteSimpleCSV() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestWriteDetailedCSV(t *testing.T) {
	tests := []struct {
		name           string
		predictions    []*types.DetailedPrediction
		includeHeaders bool
		expectedLines  int // Just check line count for complex data
	}{
		{
			name: "detailed predictions with headers",
			predictions: []*types.DetailedPrediction{
				{
					RowIndex:          0,
					InputFeatures:     map[string]interface{}{"age": 25, "income": 30000},
					Prediction:        "no",
					Confidence:        0.85,
					DecisionPath:      []string{"age <= 30", "income <= 40000", "LEAF[no]"},
					DecisionRule:      "IF age <= 30 AND income <= 40000 THEN no",
					ClassDistribution: map[string]int{"yes": 2, "no": 8},
					Error:             "",
				},
				{
					RowIndex:          1,
					InputFeatures:     map[string]interface{}{"age": 35, "income": 60000},
					Prediction:        "yes",
					Confidence:        0.92,
					DecisionPath:      []string{"age > 30", "income > 50000", "LEAF[yes]"},
					DecisionRule:      "IF age > 30 AND income > 50000 THEN yes",
					ClassDistribution: map[string]int{"yes": 9, "no": 1},
					Error:             "",
				},
			},
			includeHeaders: true,
			expectedLines:  3, // header + 2 data rows
		},
		{
			name: "detailed predictions without headers",
			predictions: []*types.DetailedPrediction{
				{
					RowIndex:          0,
					InputFeatures:     map[string]interface{}{"feature": "value"},
					Prediction:        "class1",
					Confidence:        0.75,
					DecisionPath:      []string{"decision1", "decision2"},
					DecisionRule:      "IF condition THEN class1",
					ClassDistribution: map[string]int{"class1": 5, "class2": 3},
					Error:             "",
				},
			},
			includeHeaders: false,
			expectedLines:  1, // 1 data row only
		},
		{
			name: "prediction with error",
			predictions: []*types.DetailedPrediction{
				{
					RowIndex:          0,
					InputFeatures:     map[string]interface{}{"feature": "value"},
					Prediction:        "class1",
					Confidence:        0.0,
					DecisionPath:      []string{},
					DecisionRule:      "",
					ClassDistribution: map[string]int{},
					Error:             "missing feature value",
				},
			},
			includeHeaders: true,
			expectedLines:  2, // header + 1 data row
		},
		{
			name:           "empty predictions with headers",
			predictions:    []*types.DetailedPrediction{},
			includeHeaders: true,
			expectedLines:  1, // header only
		},
		{
			name:           "empty predictions without headers",
			predictions:    []*types.DetailedPrediction{},
			includeHeaders: false,
			expectedLines:  0, // no lines
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create temporary file
			tempDir := t.TempDir()
			outputPath := filepath.Join(tempDir, "test_detailed.csv")

			// Write predictions
			err := WriteDetailedCSV(tt.predictions, outputPath, tt.includeHeaders)
			if err != nil {
				t.Fatalf("WriteDetailedCSV failed: %v", err)
			}

			// Verify file exists
			if _, err := os.Stat(outputPath); os.IsNotExist(err) {
				t.Fatalf("Output file was not created: %s", outputPath)
			}

			// Read and verify content
			content, err := os.ReadFile(outputPath)
			if err != nil {
				t.Fatalf("Failed to read output file: %v", err)
			}

			lines := strings.Split(strings.TrimSpace(string(content)), "\n")

			// Handle empty case
			if tt.expectedLines == 0 {
				if len(lines) == 1 && lines[0] == "" {
					return // Empty file is correct
				}
				t.Fatalf("Expected empty file, got %d lines", len(lines))
			}

			if len(lines) != tt.expectedLines {
				t.Errorf("Expected %d lines, got %d lines. Content: %s", tt.expectedLines, len(lines), string(content))
			}

			// Verify headers if included
			if tt.includeHeaders && len(lines) > 0 {
				expectedHeaders := []string{"row_index", "prediction", "confidence", "decision_rule", "decision_path", "input_features", "class_distribution", "error"}
				headerLine := lines[0]
				for _, header := range expectedHeaders {
					if !strings.Contains(headerLine, header) {
						t.Errorf("Header line missing expected header %q: %s", header, headerLine)
					}
				}
			}
		})
	}
}

func TestWriteDetailedCSV_ErrorHandling(t *testing.T) {
	prediction := &types.DetailedPrediction{
		RowIndex:          0,
		InputFeatures:     map[string]interface{}{"feature": "value"},
		Prediction:        "class1",
		Confidence:        0.75,
		DecisionPath:      []string{"decision"},
		DecisionRule:      "rule",
		ClassDistribution: map[string]int{"class1": 1},
		Error:             "test error",
	}

	tempDir := t.TempDir()
	outputPath := filepath.Join(tempDir, "test_error.csv")

	err := WriteDetailedCSV([]*types.DetailedPrediction{prediction}, outputPath, true)
	if err != nil {
		t.Fatalf("WriteDetailedCSV failed: %v", err)
	}

	// Read content and verify error handling
	content, err := os.ReadFile(outputPath)
	if err != nil {
		t.Fatalf("Failed to read output file: %v", err)
	}

	lines := strings.Split(strings.TrimSpace(string(content)), "\n")
	if len(lines) != 2 {
		t.Fatalf("Expected 2 lines (header + data), got %d", len(lines))
	}

	// Check that prediction value is "ERROR" when error is present
	dataLine := lines[1]
	if !strings.Contains(dataLine, "ERROR") {
		t.Errorf("Expected prediction to be 'ERROR' when error is present, got: %s", dataLine)
	}

	// Check that error message is included
	if !strings.Contains(dataLine, "test error") {
		t.Errorf("Expected error message to be included, got: %s", dataLine)
	}
}

func TestWriteDetailedCSV_ErrorCases(t *testing.T) {
	prediction := &types.DetailedPrediction{
		RowIndex:          0,
		InputFeatures:     map[string]interface{}{"feature": "value"},
		Prediction:        "class1",
		Confidence:        0.75,
		DecisionPath:      []string{"decision"},
		DecisionRule:      "rule",
		ClassDistribution: map[string]int{"class1": 1},
		Error:             "",
	}

	tests := []struct {
		name        string
		outputPath  string
		predictions []*types.DetailedPrediction
		wantErr     bool
	}{
		{
			name:        "invalid output directory",
			outputPath:  "/nonexistent/directory/output.csv",
			predictions: []*types.DetailedPrediction{prediction},
			wantErr:     true,
		},
		{
			name:        "empty output path",
			outputPath:  "",
			predictions: []*types.DetailedPrediction{prediction},
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := WriteDetailedCSV(tt.predictions, tt.outputPath, true)
			if (err != nil) != tt.wantErr {
				t.Errorf("WriteDetailedCSV() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestCSVRecord_SimpleRecord tests the SimpleRecord implementation of CSVRecord interface
func TestCSVRecord_SimpleRecord(t *testing.T) {
	tests := []struct {
		name            string
		prediction      string
		expectedRecord  []string
		expectedHeaders []string
	}{
		{
			name:            "basic prediction",
			prediction:      "yes",
			expectedRecord:  []string{"yes"},
			expectedHeaders: []string{"prediction"},
		},
		{
			name:            "prediction with spaces",
			prediction:      "class with spaces",
			expectedRecord:  []string{"class with spaces"},
			expectedHeaders: []string{"prediction"},
		},
		{
			name:            "empty prediction",
			prediction:      "",
			expectedRecord:  []string{""},
			expectedHeaders: []string{"prediction"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := SimpleRecord{Prediction: tt.prediction}

			// Test ToCSVRecord
			csvRecord := record.ToCSVRecord()
			if len(csvRecord) != len(tt.expectedRecord) {
				t.Errorf("ToCSVRecord() length = %d, expected %d", len(csvRecord), len(tt.expectedRecord))
			}
			for i, expected := range tt.expectedRecord {
				if csvRecord[i] != expected {
					t.Errorf("ToCSVRecord()[%d] = %q, expected %q", i, csvRecord[i], expected)
				}
			}

			// Test GetHeaders
			headers := record.GetHeaders()
			if len(headers) != len(tt.expectedHeaders) {
				t.Errorf("GetHeaders() length = %d, expected %d", len(headers), len(tt.expectedHeaders))
			}
			for i, expected := range tt.expectedHeaders {
				if headers[i] != expected {
					t.Errorf("GetHeaders()[%d] = %q, expected %q", i, headers[i], expected)
				}
			}
		})
	}
}

// TestCSVRecord_DetailedRecord tests the DetailedRecord implementation of CSVRecord interface
func TestCSVRecord_DetailedRecord(t *testing.T) {
	tests := []struct {
		name               string
		index              int
		prediction         *types.DetailedPrediction
		expectedRecordLen  int
		expectedHeaders    []string
		checkPredictionCol bool
		expectedPredValue  string
	}{
		{
			name:  "normal prediction",
			index: 0,
			prediction: &types.DetailedPrediction{
				Prediction:        "yes",
				Confidence:        0.85,
				DecisionRule:      "IF age > 30 THEN yes",
				DecisionPath:      []string{"age > 30", "LEAF[yes]"},
				InputFeatures:     map[string]interface{}{"age": 35},
				ClassDistribution: map[string]int{"yes": 8, "no": 2},
				Error:             "",
			},
			expectedRecordLen:  8,
			expectedHeaders:    []string{"row_index", "prediction", "confidence", "decision_rule", "decision_path", "input_features", "class_distribution", "error"},
			checkPredictionCol: true,
			expectedPredValue:  "yes",
		},
		{
			name:  "prediction with error",
			index: 1,
			prediction: &types.DetailedPrediction{
				Prediction:        "no",
				Confidence:        0.0,
				DecisionRule:      "",
				DecisionPath:      []string{},
				InputFeatures:     map[string]interface{}{},
				ClassDistribution: map[string]int{},
				Error:             "missing feature",
			},
			expectedRecordLen:  8,
			expectedHeaders:    []string{"row_index", "prediction", "confidence", "decision_rule", "decision_path", "input_features", "class_distribution", "error"},
			checkPredictionCol: true,
			expectedPredValue:  "ERROR",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := DetailedRecord{Index: tt.index, Prediction: tt.prediction}

			// Test ToCSVRecord
			csvRecord := record.ToCSVRecord()
			if len(csvRecord) != tt.expectedRecordLen {
				t.Errorf("ToCSVRecord() length = %d, expected %d", len(csvRecord), tt.expectedRecordLen)
			}

			// Check specific columns
			if tt.checkPredictionCol && len(csvRecord) > 1 {
				if csvRecord[1] != tt.expectedPredValue {
					t.Errorf("ToCSVRecord()[1] (prediction) = %q, expected %q", csvRecord[1], tt.expectedPredValue)
				}
			}

			// Test GetHeaders
			headers := record.GetHeaders()
			if len(headers) != len(tt.expectedHeaders) {
				t.Errorf("GetHeaders() length = %d, expected %d", len(headers), len(tt.expectedHeaders))
			}
			for i, expected := range tt.expectedHeaders {
				if headers[i] != expected {
					t.Errorf("GetHeaders()[%d] = %q, expected %q", i, headers[i], expected)
				}
			}
		})
	}
}

// TestWriteCSV_UnifiedFunction tests the unified writeCSV function directly
func TestWriteCSV_UnifiedFunction(t *testing.T) {
	tests := []struct {
		name              string
		records           []CSVRecord
		config            CSVWriteConfig
		expectedLines     int
		shouldHaveHeaders bool
	}{
		{
			name: "mixed record types with headers",
			records: []CSVRecord{
				SimpleRecord{Prediction: "yes"},
				SimpleRecord{Prediction: "no"},
			},
			config: CSVWriteConfig{
				OutputPath:     "", // will be set in test
				IncludeHeaders: true,
				RecordType:     "simple",
			},
			expectedLines:     3, // header + 2 records
			shouldHaveHeaders: true,
		},
		{
			name:    "empty records with headers",
			records: []CSVRecord{},
			config: CSVWriteConfig{
				OutputPath:     "", // will be set in test
				IncludeHeaders: true,
				RecordType:     "detailed",
			},
			expectedLines:     1, // header only
			shouldHaveHeaders: true,
		},
		{
			name:    "empty records without headers",
			records: []CSVRecord{},
			config: CSVWriteConfig{
				OutputPath:     "", // will be set in test
				IncludeHeaders: false,
				RecordType:     "simple",
			},
			expectedLines:     0, // no lines
			shouldHaveHeaders: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create temporary file
			tempDir := t.TempDir()
			outputPath := filepath.Join(tempDir, "test_unified.csv")
			tt.config.OutputPath = outputPath

			// Call unified function
			err := writeCSV(tt.records, tt.config)
			if err != nil {
				t.Fatalf("writeCSV failed: %v", err)
			}

			// Verify file exists
			if _, err := os.Stat(outputPath); os.IsNotExist(err) {
				t.Fatalf("Output file was not created: %s", outputPath)
			}

			// Read and verify content
			content, err := os.ReadFile(outputPath)
			if err != nil {
				t.Fatalf("Failed to read output file: %v", err)
			}

			lines := strings.Split(strings.TrimSpace(string(content)), "\n")

			// Handle empty case
			if tt.expectedLines == 0 {
				if len(lines) == 1 && lines[0] == "" {
					return // Empty file is correct
				}
				t.Fatalf("Expected empty file, got %d lines", len(lines))
			}

			if len(lines) != tt.expectedLines {
				t.Errorf("Expected %d lines, got %d lines. Content: %s", tt.expectedLines, len(lines), string(content))
			}

			// Check headers if expected
			if tt.shouldHaveHeaders && len(lines) > 0 {
				headerLine := lines[0]
				if tt.config.RecordType == "simple" && !strings.Contains(headerLine, "prediction") {
					t.Errorf("Expected simple headers in: %s", headerLine)
				}
				if tt.config.RecordType == "detailed" && !strings.Contains(headerLine, "row_index") {
					t.Errorf("Expected detailed headers in: %s", headerLine)
				}
			}
		})
	}
}

func TestFormatInputFeatures(t *testing.T) {
	tests := []struct {
		name     string
		features map[string]interface{}
		expected string
	}{
		{
			name:     "empty features",
			features: map[string]interface{}{},
			expected: "{}",
		},
		{
			name:     "single feature",
			features: map[string]interface{}{"age": 25},
			expected: "{age:25}",
		},
		{
			name:     "multiple features",
			features: map[string]interface{}{"age": 25, "income": 50000},
			expected: "{age:25, income:50000}", // Note: order may vary due to map iteration
		},
		{
			name:     "mixed types",
			features: map[string]interface{}{"name": "John", "age": 25, "active": true},
			expected: "name:John", // We'll check if it contains expected parts
		},
		{
			name:     "nil map",
			features: nil,
			expected: "{}",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatInputFeatures(tt.features)

			if tt.name == "empty features" || tt.name == "nil map" {
				if result != tt.expected {
					t.Errorf("formatInputFeatures() = %v, expected %v", result, tt.expected)
				}
				return
			}

			if tt.name == "single feature" {
				if result != tt.expected {
					t.Errorf("formatInputFeatures() = %v, expected %v", result, tt.expected)
				}
				return
			}

			// For multiple features, check that result contains expected parts
			if !strings.HasPrefix(result, "{") || !strings.HasSuffix(result, "}") {
				t.Errorf("formatInputFeatures() = %v, should be wrapped in braces", result)
			}

			// Check that all features are present
			for key, value := range tt.features {
				expectedPart := key + ":" + fmt.Sprintf("%v", value)
				if !strings.Contains(result, expectedPart) {
					t.Errorf("formatInputFeatures() = %v, should contain %v", result, expectedPart)
				}
			}
		})
	}
}

func TestFormatClassDistribution(t *testing.T) {
	tests := []struct {
		name         string
		distribution map[string]int
		expected     string
	}{
		{
			name:         "empty distribution",
			distribution: map[string]int{},
			expected:     "{}",
		},
		{
			name:         "single class",
			distribution: map[string]int{"yes": 5},
			expected:     "{yes:5}",
		},
		{
			name:         "multiple classes",
			distribution: map[string]int{"yes": 5, "no": 3},
			expected:     "yes:5", // We'll check if it contains expected parts
		},
		{
			name:         "nil map",
			distribution: nil,
			expected:     "{}",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatClassDistribution(tt.distribution)

			if tt.name == "empty distribution" || tt.name == "nil map" {
				if result != tt.expected {
					t.Errorf("formatClassDistribution() = %v, expected %v", result, tt.expected)
				}
				return
			}

			if tt.name == "single class" {
				if result != tt.expected {
					t.Errorf("formatClassDistribution() = %v, expected %v", result, tt.expected)
				}
				return
			}

			// For multiple classes, check that result contains expected parts
			if !strings.HasPrefix(result, "{") || !strings.HasSuffix(result, "}") {
				t.Errorf("formatClassDistribution() = %v, should be wrapped in braces", result)
			}

			// Check that all classes are present
			for class, count := range tt.distribution {
				expectedPart := class + ":" + fmt.Sprintf("%d", count)
				if !strings.Contains(result, expectedPart) {
					t.Errorf("formatClassDistribution() = %v, should contain %v", result, expectedPart)
				}
			}
		})
	}
}
