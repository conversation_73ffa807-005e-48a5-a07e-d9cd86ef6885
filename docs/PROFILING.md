# Profiling Guide for Mulberri

This guide explains how to use the built-in profiling capabilities in Mulberri to analyze performance and identify bottlenecks in your decision tree training and prediction workflows.

## Overview

Mulberri includes file-based profiling support using Go's built-in `pprof` package. This allows you to:

- **CPU Profiling**: Identify which functions consume the most CPU time
- **Memory Profiling**: Analyze memory allocation patterns and identify memory leaks
- **Goroutine Profiling**: Debug concurrency issues and goroutine leaks

Profile data is written to files that can be analyzed using Go's `pprof` tool.

## Quick Start

### Enable Profiling

Add profiling flags to your train or predict commands:

```bash
# Enable CPU profiling for training
./mulberri train -i data.csv -t target -o model.dt -f features.yaml --cpu-profile

# Enable memory profiling for prediction
./mulberri predict -i test.csv -m model.dt -o predictions.csv --mem-profile

# Enable both CPU and memory profiling
./mulberri train -i data.csv -t target -o model.dt -f features.yaml --cpu-profile --mem-profile

# Specify custom profile output directory
./mulberri train -i data.csv -t target -o model.dt -f features.yaml --cpu-profile --profile-dir ./my-profiles
```

### Profile Files

Profile files are automatically named with timestamps:
- CPU profiles: `train-cpu-20240917-143052.prof`
- Memory profiles: `predict-mem-20240917-143052.prof`
- Default location: `./profiles/` directory

## Analyzing Profiles

### CPU Profiles

CPU profiles show where your program spends time:

```bash
# Interactive analysis
go tool pprof profiles/train-cpu-20240917-143052.prof

# Generate web interface (requires graphviz)
go tool pprof -http=:8080 profiles/train-cpu-20240917-143052.prof

# Top functions by CPU usage
go tool pprof -top profiles/train-cpu-20240917-143052.prof

# Generate flame graph
go tool pprof -png profiles/train-cpu-20240917-143052.prof > cpu-profile.png
```

### Memory Profiles

Memory profiles show allocation patterns:

```bash
# Interactive analysis
go tool pprof profiles/predict-mem-20240917-143052.prof

# Top functions by memory allocation
go tool pprof -top profiles/predict-mem-20240917-143052.prof

# Show allocations in specific function
go tool pprof -list=FunctionName profiles/predict-mem-20240917-143052.prof

# Generate memory usage graph
go tool pprof -png profiles/predict-mem-20240917-143052.prof > mem-profile.png
```

## Common pprof Commands

Once in the interactive pprof shell:

```
(pprof) top10          # Show top 10 functions
(pprof) list main      # Show source code for main function
(pprof) web            # Open web interface (requires graphviz)
(pprof) png            # Generate PNG graph
(pprof) help           # Show all available commands
```

## Performance Analysis Workflow

### 1. Baseline Measurement

First, run without profiling to establish baseline performance:

```bash
time ./mulberri train -i large-dataset.csv -t target -o model.dt -f features.yaml
```

### 2. CPU Profiling

Identify CPU bottlenecks:

```bash
./mulberri train -i large-dataset.csv -t target -o model.dt -f features.yaml --cpu-profile
go tool pprof -top profiles/train-cpu-*.prof
```

Look for:
- Functions with high `flat%` (time spent in function itself)
- Functions with high `cum%` (time spent in function and callees)
- Unexpected hot spots

### 3. Memory Profiling

Analyze memory usage:

```bash
./mulberri train -i large-dataset.csv -t target -o model.dt -f features.yaml --mem-profile
go tool pprof -top profiles/train-mem-*.prof
```

Look for:
- Large memory allocations
- Functions that allocate frequently
- Potential memory leaks

### 4. Optimization

Based on profiling results:
- Optimize hot code paths identified in CPU profiles
- Reduce allocations in memory-intensive functions
- Consider algorithmic improvements for bottlenecks

## Example Analysis Session

```bash
# 1. Train with profiling enabled
./mulberri train -i examples/loan_approval.csv -t approved -o model.dt -f examples/loan_approval_features.yaml --cpu-profile --mem-profile

# 2. Analyze CPU usage
go tool pprof profiles/train-cpu-*.prof
(pprof) top10
(pprof) list BuildTree
(pprof) web

# 3. Analyze memory usage
go tool pprof profiles/train-mem-*.prof
(pprof) top10
(pprof) list LoadCSVToDataset
```

## Profiling Best Practices

### When to Profile

- **Performance Issues**: When training or prediction is slower than expected
- **Memory Problems**: When experiencing high memory usage or potential leaks
- **Optimization**: Before and after performance improvements to measure impact
- **Large Datasets**: When working with datasets that push system limits

### What to Profile

- **Training**: Focus on tree building algorithms, data loading, and feature processing
- **Prediction**: Focus on tree traversal, batch processing, and output generation
- **Both**: Memory allocation patterns and garbage collection impact

### Profile Interpretation

- **CPU Profiles**: Focus on `flat%` for direct optimization targets
- **Memory Profiles**: Look for allocation patterns, not just total usage
- **Comparative Analysis**: Profile before and after changes to measure improvement

## Troubleshooting

### Common Issues

1. **No profile generated**: Check that profiling flags are enabled and output directory is writable
2. **Empty profiles**: Ensure the operation runs long enough to collect meaningful data
3. **Large profile files**: Consider profiling smaller datasets first for initial analysis

### Performance Impact

- CPU profiling adds ~5% overhead
- Memory profiling adds minimal overhead
- Profile file sizes are typically small (< 10MB)

## Integration with CI/CD

For automated performance monitoring:

```bash
# Run benchmarks with profiling
./scripts/benchmark.sh --profile

# Compare profiles across versions
go tool pprof -base=old.prof new.prof
```

## Advanced Features

The profiling package also supports:
- Goroutine profiling for concurrency analysis
- Custom profile output directories
- Timestamped filenames for historical analysis

See the source code in `internal/utils/profiling/` for additional capabilities.
