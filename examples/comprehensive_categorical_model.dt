{"features": [{"name": "department", "type": "string"}, {"name": "education", "type": "string"}, {"name": "experience", "type": "string"}, {"name": "performance", "type": "string"}], "classes": ["no", "yes"], "metadata": {"created_at": "2025-09-16T00:20:39.369026273+03:00", "algorithm": "C4.5", "max_depth": 10, "min_samples": 2, "criterion": "entropy", "total_nodes": 18, "leaf_nodes": 12, "training_samples": 20, "target_column": "promoted"}, "root": {"type": "decision", "feature": {"name": "experience", "type": "string"}, "split_value": "experience", "children": {"junior": {"type": "decision", "feature": {"name": "performance", "type": "string"}, "split_value": "performance", "children": {"excellent": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1}, "good": {"type": "decision", "feature": {"name": "department", "type": "string"}, "split_value": "department", "children": {"finance": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1}, "hr": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1}, "marketing": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1}, "sales": {"type": "decision", "feature": {"name": "education", "type": "string"}, "split_value": "education", "children": {"bachelors": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1}, "masters": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"no": 1, "yes": 1}, "samples": 2, "confidence": 0.5}}, "class_distribution": {"no": 4, "yes": 1}, "samples": 5, "confidence": 0.8}, "poor": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 4}, "samples": 4, "confidence": 1}}, "class_distribution": {"no": 8, "yes": 2}, "samples": 10, "confidence": 0.8}, "senior": {"type": "decision", "feature": {"name": "performance", "type": "string"}, "split_value": "performance", "children": {"excellent": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 6}, "samples": 6, "confidence": 1}, "good": {"type": "decision", "feature": {"name": "department", "type": "string"}, "split_value": "department", "children": {"engineering": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1}, "finance": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1}, "hr": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1}, "marketing": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"no": 1, "yes": 3}, "samples": 4, "confidence": 0.75}}, "class_distribution": {"no": 1, "yes": 9}, "samples": 10, "confidence": 0.9}}, "class_distribution": {"no": 9, "yes": 11}, "samples": 20, "confidence": 0.55}}