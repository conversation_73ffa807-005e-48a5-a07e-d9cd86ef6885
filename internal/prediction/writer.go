package prediction

import (
	"github.com/berrijam/mulberri/internal/io/formats/csv"
	"github.com/berrijam/mulberri/internal/types"
)

// PredictionWriter handles writing prediction results to various output formats.
//
// This service provides flexible output formatting for prediction results,
// supporting both simple and detailed prediction formats with configurable
// output styles for different use cases.
//
// Architecture: Follows the service pattern with format-specific methods
// and comprehensive error handling for reliable output generation.
type PredictionWriter struct{}

// NewPredictionWriter creates a new prediction writer service.
//
// Returns:
//   - *PredictionWriter: New writer service instance
//
// Performance: O(1) initialization
// Side effects: None
func NewPredictionWriter() *PredictionWriter {
	return &PredictionWriter{}
}

// WriteDetailedCSV writes detailed prediction results to a CSV file.
//
// This method creates a comprehensive CSV output that includes the prediction,
// confidence, decision path, and decision rule for each row, providing full
// transparency into how each prediction was made.
//
// Args:
//   - predictions: Detailed prediction results to write
//   - outputPath: Path where the CSV file should be created
//   - includeHeaders: Whether to include column headers in the output
//
// Returns:
//   - error: Error if writing fails
//
// Performance: O(n) where n is the number of predictions
// Side effects: Creates/overwrites file at outputPath, logs writing progress
func (pw *PredictionWriter) WriteDetailedCSV(predictions []*types.DetailedPrediction, outputPath string, includeHeaders bool) error {
	// Delegate to the CSV package's WriteDetailedCSV function to avoid code duplication
	return csv.WriteDetailedCSV(predictions, outputPath, includeHeaders)
}

// WriteSimpleCSV writes simple prediction results to a CSV file.
//
// This method creates a basic CSV output with just the prediction column,
// compatible with the original simple prediction format.
//
// Args:
//   - predictions: Simple prediction results to write
//   - outputPath: Path where the CSV file should be created
//   - includeHeaders: Whether to include column headers in the output
//
// Returns:
//   - error: Error if writing fails
//
// Performance: O(n) where n is the number of predictions
// Side effects: Creates/overwrites file at outputPath, logs writing progress
func (pw *PredictionWriter) WriteSimpleCSV(predictions []string, outputPath string, includeHeaders bool) error {
	// Delegate to the CSV package's WriteSimpleCSV function to avoid code duplication
	return csv.WriteSimpleCSV(predictions, outputPath, includeHeaders)
}
