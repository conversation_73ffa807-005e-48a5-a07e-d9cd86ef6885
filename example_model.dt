{"features": [{"name": "age", "type": "integer"}, {"name": "education", "type": "string"}, {"name": "income", "type": "integer"}], "classes": ["no", "yes"], "metadata": {"created_at": "2025-09-17T15:16:50.136570445+03:00", "algorithm": "C4.5", "max_depth": 10, "min_samples": 2, "criterion": "entropy", "total_nodes": 17, "leaf_nodes": 9, "training_samples": 30, "target_column": "approved"}, "root": {"type": "decision", "feature": {"name": "income", "type": "float"}, "split_value": 54000, "children": {"gt": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 11}, "samples": 11, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "income", "type": "float"}, "split_value": 45500, "children": {"gt": {"type": "decision", "feature": {"name": "age", "type": "float"}, "split_value": 39, "children": {"gt": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 2}, "samples": 2, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "age", "type": "float"}, "split_value": 32, "children": {"gt": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 3}, "samples": 3, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "age", "type": "float"}, "split_value": 30.5, "children": {"gt": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"no": 1, "yes": 1}, "samples": 2, "confidence": 0.5}}, "class_distribution": {"no": 1, "yes": 4}, "samples": 5, "confidence": 0.8}}, "class_distribution": {"no": 3, "yes": 4}, "samples": 7, "confidence": 0.5714285714285714}, "lte": {"type": "decision", "feature": {"name": "income", "type": "float"}, "split_value": 39000, "children": {"gt": {"type": "decision", "feature": {"name": "income", "type": "float"}, "split_value": 41000, "children": {"gt": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 4}, "samples": 4, "confidence": 1}, "lte": {"type": "decision", "feature": {"name": "age", "type": "float"}, "split_value": 27, "children": {"gt": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1}, "lte": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"no": 1, "yes": 1}, "samples": 2, "confidence": 0.5}}, "class_distribution": {"no": 5, "yes": 1}, "samples": 6, "confidence": 0.8333333333333334}, "lte": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 6}, "samples": 6, "confidence": 1}}, "class_distribution": {"no": 11, "yes": 1}, "samples": 12, "confidence": 0.9166666666666666}}, "class_distribution": {"no": 14, "yes": 5}, "samples": 19, "confidence": 0.7368421052631579}}, "class_distribution": {"no": 14, "yes": 16}, "samples": 30, "confidence": 0.5333333333333333}}