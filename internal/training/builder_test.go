package training

import (
	"testing"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
)

// TestTreeBuilder_BasicConstruction tests basic tree builder functionality.
func TestTreeBuilder_BasicConstruction(t *testing.T) {
	// Create test configuration
	config := BuildConfig{
		MaxDepth:         3,
		MinSamplesSplit:  2,
		MinSamplesLeaf:   1,
		Criterion:        "entropy",
		MinGainThreshold: 0.0,
	}

	// Create tree builder
	builder, err := NewTreeBuilder(config)
	if err != nil {
		t.Fatalf("Failed to create tree builder: %v", err)
	}

	if builder == nil {
		t.Fatal("Tree builder should not be nil")
	}

	// Verify configuration
	if builder.config.MaxDepth != 3 {
		t.<PERSON><PERSON><PERSON>("Expected max depth 3, got %d", builder.config.MaxDepth)
	}
	if builder.config.Criterion != "entropy" {
		t.<PERSON><PERSON><PERSON>("Expected criterion 'entropy', got %s", builder.config.Criterion)
	}
}

// TestTreeBuilder_InvalidConfiguration tests configuration validation.
func TestTreeBuilder_InvalidConfiguration(t *testing.T) {
	tests := []struct {
		name   string
		config BuildConfig
	}{
		{
			name: "negative max depth",
			config: BuildConfig{
				MaxDepth:        -1,
				MinSamplesSplit: 2,
				MinSamplesLeaf:  1,
				Criterion:       "entropy",
			},
		},
		{
			name: "invalid min samples split",
			config: BuildConfig{
				MaxDepth:        10,
				MinSamplesSplit: 1,
				MinSamplesLeaf:  1,
				Criterion:       "entropy",
			},
		},
		{
			name: "invalid criterion",
			config: BuildConfig{
				MaxDepth:        10,
				MinSamplesSplit: 2,
				MinSamplesLeaf:  1,
				Criterion:       "gini",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			builder, err := NewTreeBuilder(tt.config)
			if err == nil {
				t.Errorf("Expected error for invalid configuration, but got none")
			}
			if builder != nil {
				t.Errorf("Expected nil builder for invalid configuration, but got valid builder")
			}
		})
	}
}

// TestTreeBuilder_SimpleTree tests building a simple decision tree.
func TestTreeBuilder_SimpleTree(t *testing.T) {
	// Create test dataset
	dataset := dataset.NewDataset[string](100)

	// Add age feature (numerical)
	ages := make([]int64, 100)
	ageNulls := make([]bool, 100)
	for i := 0; i < 100; i++ {
		if i < 50 {
			ages[i] = int64(20 + i/5) // Ages 20-29
		} else {
			ages[i] = int64(30 + (i-50)/5) // Ages 30-39
		}
		ageNulls[i] = false
	}
	dataset.AddIntColumn("age", ages, ageNulls)

	// Add target values
	for i := 0; i < 100; i++ {
		if i < 50 {
			dataset.AddTarget("young")
		} else {
			dataset.AddTarget("old")
		}
	}

	// Create feature types
	featureTypes := map[string]features.FeatureType{
		"age": features.IntegerFeature,
	}

	// Create all indices view
	allIndices := make([]int, 100)
	for i := range allIndices {
		allIndices[i] = i
	}
	view := dataset.CreateView(allIndices)

	// Create tree builder
	config := BuildConfig{
		MaxDepth:         5,
		MinSamplesSplit:  10,
		MinSamplesLeaf:   5,
		Criterion:        "entropy",
		MinGainThreshold: 0.0,
	}

	builder, err := NewTreeBuilder(config)
	if err != nil {
		t.Fatalf("Failed to create tree builder: %v", err)
	}

	// Build tree
	tree, err := builder.BuildTree(view, featureTypes, "target")
	if err != nil {
		t.Fatalf("Failed to build tree: %v", err)
	}

	if tree == nil {
		t.Fatal("Tree should not be nil")
	}

	// Verify tree properties
	if tree.Root == nil {
		t.Error("Tree root should not be nil")
	}

	if len(tree.Features) != 1 {
		t.Errorf("Expected 1 feature, got %d", len(tree.Features))
	}

	if len(tree.Classes) != 2 {
		t.Errorf("Expected 2 classes, got %d", len(tree.Classes))
	}

	if tree.Metadata == nil {
		t.Error("Tree metadata should not be nil")
	}

	// Verify metadata
	if tree.Metadata.Algorithm != "C4.5" {
		t.Errorf("Expected algorithm 'C4.5', got %s", tree.Metadata.Algorithm)
	}

	if tree.Metadata.TotalNodes <= 0 {
		t.Errorf("Expected positive total nodes, got %d", tree.Metadata.TotalNodes)
	}

	if tree.Metadata.LeafNodes <= 0 {
		t.Errorf("Expected positive leaf nodes, got %d", tree.Metadata.LeafNodes)
	}

	t.Logf("Tree built successfully: %d total nodes, %d leaf nodes, max depth %d",
		tree.Metadata.TotalNodes, tree.Metadata.LeafNodes, tree.Metadata.MaxDepth)
}

// TestTreeBuilder_EmptyDataset tests error handling for empty dataset.
func TestTreeBuilder_EmptyDataset(t *testing.T) {
	config := BuildConfig{
		MaxDepth:         5,
		MinSamplesSplit:  2,
		MinSamplesLeaf:   1,
		Criterion:        "entropy",
		MinGainThreshold: 0.0,
	}

	builder, err := NewTreeBuilder(config)
	if err != nil {
		t.Fatalf("Failed to create tree builder: %v", err)
	}

	// Create empty dataset view
	dataset := dataset.NewDataset[string](0)
	view := dataset.CreateView([]int{})

	featureTypes := map[string]features.FeatureType{
		"age": features.IntegerFeature,
	}

	// Should fail with empty dataset
	tree, err := builder.BuildTree(view, featureTypes, "target")
	if err == nil {
		t.Error("Expected error for empty dataset, but got none")
	}
	if tree != nil {
		t.Error("Expected nil tree for empty dataset, but got valid tree")
	}
}
