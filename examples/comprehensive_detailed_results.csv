row_index,prediction,confidence,decision_rule,decision_path,input_features,class_distribution,error
0,yes,1.0000,IF experience = senior AND performance = excellent THEN yes,experience = senior → performance = excellent → LEAF[yes] (confidence: 1.000),"{education:masters, performance:excellent, department:engineering, experience:senior}",{yes:6},
1,no,1.0000,IF experience = junior AND performance = poor THEN no,experience = junior → performance = poor → LEAF[no] (confidence: 1.000),"{education:bachelors, performance:poor, department:marketing, experience:junior}",{no:4},
2,yes,1.0000,IF experience = junior AND performance = good AND department = sales AND education = masters THEN yes,experience = junior → performance = good → department = sales → education = masters → LEAF[yes] (confidence: 1.000),"{education:masters, performance:good, department:sales, experience:junior}",{yes:1},
3,yes,1.0000,IF experience = senior AND performance = good AND department = hr THEN yes,experience = senior → performance = good → department = hr → LEAF[yes] (confidence: 1.000),"{experience:senior, education:bachelors, performance:good, department:hr}",{yes:1},
4,no,1.0000,IF experience = junior AND performance = good AND department = finance THEN no,experience = junior → performance = good → department = finance → LEAF[no] (confidence: 1.000),"{department:finance, experience:junior, education:bachelors, performance:good}",{no:1},
5,yes,1.0000,IF experience = senior AND performance = excellent THEN yes,experience = senior → performance = excellent → LEAF[yes] (confidence: 1.000),"{department:unknown_dept, experience:senior, education:masters, performance:excellent}",{yes:6},
6,yes,0.5500,IF experience = unknown_exp THEN yes,experience = unknown_exp → NO_CHILD -> yes,"{performance:excellent, department:engineering, experience:unknown_exp, education:masters}","{no:9, yes:11}",
7,no,0.5000,IF experience = junior AND performance = good AND department = sales AND education = unknown_edu THEN no,experience = junior → performance = good → department = sales → education = unknown_edu → NO_CHILD -> no,"{department:sales, experience:junior, education:unknown_edu, performance:good}","{no:1, yes:1}",
8,no,0.8000,IF experience = junior AND performance = unknown_perf THEN no,experience = junior → performance = unknown_perf → NO_CHILD -> no,"{performance:unknown_perf, department:marketing, experience:junior, education:bachelors}","{no:8, yes:2}",
