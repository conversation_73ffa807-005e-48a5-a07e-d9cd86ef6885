package prediction

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/types"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// BatchProcessor handles batch prediction operations and output formatting.
//
// This component is responsible for processing prediction results in batches,
// formatting output data, and writing results to various output formats.
// It separates the concerns of prediction logic from output formatting.
//
// Architecture: Follows the processor pattern for handling batch operations
// with configurable output formats and validation using global logger functions.
type BatchProcessor struct{}

// NewBatchProcessor creates a new batch processor instance.
//
// Returns configured batch processor ready for use.
// Uses global logger functions for logging operations.
func NewBatchProcessor() *BatchProcessor {
	return &BatchProcessor{}
}

// BatchRequest contains parameters for batch processing operations.
//
// This structure encapsulates the batch processing request including
// prediction results and output configuration options.
type BatchRequest struct {
	// Predictions contains the simple prediction results to process
	Predictions []string

	// DetailedPredictions contains the detailed prediction results to process
	DetailedPredictions []*types.DetailedPrediction

	// OutputPath specifies where to write the batch results
	OutputPath string

	// Format specifies the output format (currently supports CSV)
	Format string

	// IncludeHeaders indicates whether to include column headers
	IncludeHeaders bool

	// UseDetailedFormat indicates whether to use detailed output format
	UseDetailedFormat bool
}

// BatchResult contains the output of a batch processing operation.
//
// This structure holds metadata about the batch processing results
// for validation and logging purposes.
type BatchResult struct {
	// OutputPath indicates where results were written
	OutputPath string

	// RecordsWritten indicates the number of records processed
	RecordsWritten int

	// Format indicates the output format used
	Format string
}

// ProcessToCSV processes prediction results and writes them to a CSV file.
//
// This method handles the complete CSV output workflow including file creation,
// header writing, data formatting, and file closure with proper error handling.
//
// Args:
//   - request: Batch processing request with predictions and output configuration
//
// Returns:
//   - *BatchResult: Result metadata including output path and record count
//   - error: Error if batch processing fails
//
// Side effects: Creates output file, writes CSV data, logs processing progress
// Security: Validates output path and handles file permissions appropriately
func (bp *BatchProcessor) ProcessToCSV(request *BatchRequest) (*BatchResult, error) {
	if request == nil {
		return nil, fmt.Errorf("batch request cannot be nil")
	}

	if request.OutputPath == "" {
		return nil, fmt.Errorf("output path is required")
	}

	// Create prediction writer
	writer := NewPredictionWriter()

	// Determine output style and use appropriate writer method
	if request.UseDetailedFormat && len(request.DetailedPredictions) > 0 {
		// Use detailed predictions
		logger.Debug(fmt.Sprintf("Processing %d detailed predictions to CSV file: %s",
			len(request.DetailedPredictions), request.OutputPath))

		// Write detailed CSV format
		if err := writer.WriteDetailedCSV(request.DetailedPredictions, request.OutputPath, request.IncludeHeaders); err != nil {
			return nil, err
		}
		return &BatchResult{
			OutputPath:     request.OutputPath,
			RecordsWritten: len(request.DetailedPredictions),
			Format:         "DETAILED_CSV",
		}, nil
	} else {
		// Use simple predictions
		if request.Predictions == nil {
			return nil, fmt.Errorf("predictions cannot be nil")
		}

		logger.Debug(fmt.Sprintf("Processing %d simple predictions to CSV file: %s",
			len(request.Predictions), request.OutputPath))

		if err := writer.WriteSimpleCSV(request.Predictions, request.OutputPath, request.IncludeHeaders); err != nil {
			return nil, err
		}

		return &BatchResult{
			OutputPath:     request.OutputPath,
			RecordsWritten: len(request.Predictions),
			Format:         "SIMPLE_CSV",
		}, nil
	}
}

// ValidateBatchRequest performs comprehensive validation of batch processing requests.
//
// This method validates all aspects of the batch request including output path,
// prediction data, and format specifications to ensure successful processing.
//
// Args:
//   - request: The batch request to validate
//
// Returns error if validation fails, nil if request is valid.
// Side effects: Logs validation errors for debugging.
func (bp *BatchProcessor) ValidateBatchRequest(request *BatchRequest) error {
	if request == nil {
		return fmt.Errorf("batch request cannot be nil")
	}

	if request.OutputPath == "" {
		return fmt.Errorf("output path is required")
	}

	// Check that we have either simple predictions or detailed predictions
	if request.Predictions == nil && request.DetailedPredictions == nil {
		return fmt.Errorf("predictions cannot be nil")
	}

	// Validate output format
	if request.Format != "" && request.Format != "CSV" {
		return fmt.Errorf("unsupported output format: %s (supported: CSV)", request.Format)
	}

	// Set default format if not specified
	if request.Format == "" {
		request.Format = "CSV"
	}

	// Log validation success with appropriate count
	predictionCount := len(request.Predictions)
	if request.DetailedPredictions != nil {
		predictionCount = len(request.DetailedPredictions)
	}
	logger.Debug(fmt.Sprintf("Batch request validation passed: %d predictions, format: %s, output: %s",
		predictionCount, request.Format, request.OutputPath))

	return nil
}

// ProcessBatch processes prediction results according to the specified format.
//
// This is the main batch processing method that routes requests to appropriate
// format-specific processors based on the requested output format.
//
// Args:
//   - request: Complete batch processing request
//
// Returns:
//   - *BatchResult: Processing results and metadata
//   - error: Error if batch processing fails
//
// Side effects: Creates output files, logs processing progress
func (bp *BatchProcessor) ProcessBatch(request *BatchRequest) (*BatchResult, error) {
	// Validate the request
	if err := bp.ValidateBatchRequest(request); err != nil {
		logger.Error(fmt.Sprintf("Batch request validation failed: %v", err))
		return nil, err
	}

	// Determine the number of predictions to process
	predictionCount := len(request.Predictions)
	if request.UseDetailedFormat && len(request.DetailedPredictions) > 0 {
		predictionCount = len(request.DetailedPredictions)
	}

	logger.Info(fmt.Sprintf("Starting batch processing: %d predictions to %s format",
		predictionCount, request.Format))

	// Route to appropriate processor based on format and style
	switch request.Format {
	case "CSV":
		return bp.ProcessToCSV(request)
	default:
		return nil, fmt.Errorf("unsupported output format: %s", request.Format)
	}
}

// CreateBatchRequest creates a properly configured batch request.
//
// This convenience method helps create batch requests with appropriate defaults
// and validation to simplify the batch processing workflow.
//
// Args:
//   - predictions: Prediction results to process
//   - outputPath: Path for output file
//   - format: Output format (optional, defaults to CSV)
//
// Returns configured batch request ready for processing.
func CreateBatchRequest(predictions []string, outputPath string, format string) *BatchRequest {
	if format == "" {
		format = "CSV"
	}

	return &BatchRequest{
		Predictions:       predictions,
		OutputPath:        outputPath,
		Format:            format,
		IncludeHeaders:    true, // Default to including headers
		UseDetailedFormat: false,
	}
}

// CreateDetailedBatchRequest creates a batch request configured for detailed predictions.
//
// This convenience method helps create batch requests for detailed prediction output
// with decision paths, confidence scores, and reasoning information.
//
// Args:
//   - predictions: Simple prediction results to process
//   - detailedPredictions: Detailed prediction results to process
//   - outputPath: Path for output file
//   - format: Output format (optional, defaults to CSV)
//
// Returns configured batch request ready for detailed processing.
func CreateDetailedBatchRequest(predictions []string, detailedPredictions []*types.DetailedPrediction, outputPath string, format string) *BatchRequest {
	if format == "" {
		format = "CSV"
	}

	return &BatchRequest{
		Predictions:         predictions,
		DetailedPredictions: detailedPredictions,
		OutputPath:          outputPath,
		Format:              format,
		IncludeHeaders:      true,
		UseDetailedFormat:   len(detailedPredictions) > 0,
	}
}
