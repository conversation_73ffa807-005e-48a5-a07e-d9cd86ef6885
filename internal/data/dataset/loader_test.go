// Package dataset provides comprehensive tests for dataset loading functionality.
//
// This test suite covers both basic CSV loading and the enhanced LoadCSVToDataset function
// that supports different operation modes:
//
//   - "train" mode: Requires target column to exist, includes target values in dataset
//   - "predict" mode: Target column optional, excludes target from features, only includes
//     columns specified in feature metadata
//   - Invalid operations: Default to training mode behavior
//
// Key test categories:
// 1. Basic CSV loading and parsing
// 2. Type conversion and feature metadata handling
// 3. Null value handling and edge cases
// 4. Training vs prediction mode differences
// 5. Feature filtering and column exclusion
// 6. Error handling for missing files, columns, and invalid data
package dataset

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/berrijam/mulberri/internal/data/features"
)

func TestLoaderBasic(t *testing.T) {
	// Create a temporary CSV file
	csvContent := `name,age,salary,active
John,25,50000.5,true
Jane,30,75000.0,false
Bob,35,60000.25,true`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Load CSV data
	loader := NewLoader()
	data := loader.LoadCSV(csvFile)
	if data == nil {
		t.Fatal("LoadCSV returned nil data")
	}

	// Verify basic structure
	if data.NumRows != 3 {
		t.Errorf("Expected 3 rows, got %d", data.NumRows)
	}

	if data.NumColumns != 4 {
		t.Errorf("Expected 4 columns, got %d", data.NumColumns)
	}

	expectedHeaders := []string{"name", "age", "salary", "active"}
	for i, expected := range expectedHeaders {
		if data.Headers[i] != expected {
			t.Errorf("Header %d: expected %s, got %s", i, expected, data.Headers[i])
		}
	}

	// Verify some data content
	if len(data.Records) != 3 {
		t.Errorf("Expected 3 records, got %d", len(data.Records))
	}
	if data.Records[0][0] != "John" {
		t.Errorf("Expected first record name to be 'John', got %s", data.Records[0][0])
	}
}

func TestLoaderFileNotFound(t *testing.T) {
	loader := NewLoader()
	data := loader.LoadCSV("nonexistent.csv")

	// Should return nil for nonexistent file
	if data != nil {
		t.Fatal("Expected nil data for nonexistent file, got valid data")
	}
}

func TestLoaderEmptyFile(t *testing.T) {
	// Create an empty CSV file
	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "empty.csv")

	err := os.WriteFile(csvFile, []byte(""), 0644)
	if err != nil {
		t.Fatalf("Failed to create empty CSV file: %v", err)
	}

	loader := NewLoader()
	data := loader.LoadCSV(csvFile)

	// Should return data with zero values for empty CSV
	if data == nil {
		t.Fatal("Expected data to be returned for empty CSV")
	}
	if data.NumColumns != 0 {
		t.Errorf("Expected 0 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 0 {
		t.Errorf("Expected 0 rows, got %d", data.NumRows)
	}
}

func TestLoaderHeaderOnly(t *testing.T) {
	// Create a CSV file with only headers
	csvContent := `name,age,salary,active`
	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "header_only.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create header-only CSV file: %v", err)
	}

	loader := NewLoader()
	data := loader.LoadCSV(csvFile)
	if data == nil {
		t.Fatal("LoadCSV returned nil data")
	}

	// Should have headers but no data rows
	if data.NumColumns != 4 {
		t.Errorf("Expected 4 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 0 {
		t.Errorf("Expected 0 data rows, got %d", data.NumRows)
	}
	if len(data.Headers) != 4 {
		t.Errorf("Expected 4 headers, got %d", len(data.Headers))
	}
	if len(data.Records) != 0 {
		t.Errorf("Expected 0 records, got %d", len(data.Records))
	}
}

// TestLoadCSVToDataset tests the integrated CSV loading with type conversion
func TestLoadCSVToDataset(t *testing.T) {
	// Create test CSV content with mixed types
	csvContent := `name,age,salary,active,department
John,25,50000.5,true,Engineering
Jane,30,75000.0,false,Marketing
Bob,35,60000.25,true,Engineering`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Define feature types for conversion
	featureTypes := map[string]features.FeatureType{
		"name":       features.StringFeature,
		"age":        features.IntegerFeature,
		"salary":     features.FloatFeature,
		"department": features.StringFeature,
	}

	// Load CSV to dataset with string target for training
	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "active", "train")
	if dataset == nil {
		t.Fatalf("LoadCSVToDataset failed")
	}

	// Verify dataset structure
	if dataset.totalSize != 3 {
		t.Errorf("Expected dataset size 3, got %d", dataset.totalSize)
	}

	// Verify feature order
	expectedFeatures := []string{"name", "age", "salary", "department"}
	featureOrder := dataset.GetFeatureOrder()
	if len(featureOrder) != len(expectedFeatures) {
		t.Errorf("Expected %d features, got %d", len(expectedFeatures), len(featureOrder))
	}

	// Verify columns were created correctly
	nameCol := dataset.GetColumn("name")
	if nameCol == nil {
		t.Errorf("Failed to get name column")
	}
	if nameCol.GetType() != features.StringFeature {
		t.Errorf("Expected StringFeature for name, got %v", nameCol.GetType())
	}

	ageCol := dataset.GetColumn("age")
	if ageCol == nil {
		t.Errorf("Failed to get age column")
	}
	if ageCol.GetType() != features.IntegerFeature {
		t.Errorf("Expected IntegerFeature for age, got %v", ageCol.GetType())
	}

	salaryCol := dataset.GetColumn("salary")
	if salaryCol == nil {
		t.Errorf("Failed to get salary column")
	}
	if salaryCol.GetType() != features.FloatFeature {
		t.Errorf("Expected FloatFeature for salary, got %v", salaryCol.GetType())
	}

	// Verify data values
	nameValue := nameCol.GetValue(0)
	if nameValue == nil {
		t.Errorf("Failed to get name value")
	}
	// Dereference the pointer to get the actual value
	if ptr, ok := nameValue.(*string); ok {
		if *ptr != "John" {
			t.Errorf("Expected name 'John', got %v", *ptr)
		}
	} else {
		t.Errorf("Expected *string, got %T", nameValue)
	}

	ageValue := ageCol.GetValue(0)
	if ageValue == nil {
		t.Errorf("Failed to get age value")
	}
	// Dereference the pointer to get the actual value
	if ptr, ok := ageValue.(*int64); ok {
		if *ptr != int64(25) {
			t.Errorf("Expected age 25, got %v", *ptr)
		}
	} else {
		t.Errorf("Expected *int64, got %T", ageValue)
	}

	salaryValue := salaryCol.GetValue(0)
	if salaryValue == nil {
		t.Errorf("Failed to get salary value")
	}
	// Dereference the pointer to get the actual value
	if ptr, ok := salaryValue.(*float64); ok {
		if *ptr != 50000.5 {
			t.Errorf("Expected salary 50000.5, got %v", *ptr)
		}
	} else {
		t.Errorf("Expected *float64, got %T", salaryValue)
	}

	// Verify target values
	target0 := dataset.GetTarget(0)
	if target0 == "" {
		t.Errorf("Failed to get target 0")
	}
	if target0 != "true" {
		t.Errorf("Expected target 'true', got %v", target0)
	}

	target1 := dataset.GetTarget(1)
	if target1 == "" {
		t.Errorf("Failed to get target 1")
	}
	if target1 != "false" {
		t.Errorf("Expected target 'false', got %v", target1)
	}

	// Verify feature metadata
	nameInfo := dataset.GetFeatureInfo("name")
	if nameInfo == nil {
		t.Errorf("Failed to get name feature info")
		return
	}
	if nameInfo.Type != features.StringFeature {
		t.Errorf("Expected StringFeature in metadata, got %v", nameInfo.Type)
	}
}

// TestLoadCSVToDatasetTargetNotFound tests error handling for missing target column
func TestLoadCSVToDatasetTargetNotFound(t *testing.T) {
	csvContent := `name,age,salary
John,25,50000.5
Jane,30,75000.0`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file")
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	// Try to load with non-existent target column for training (should fail)
	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "nonexistent", "train")
	if dataset != nil {
		t.Error("Expected nil dataset for missing target column in training mode")
	}
}

// TestLoadCSVToDatasetFileNotFound tests error handling for missing CSV file
func TestLoadCSVToDatasetFileNotFound(t *testing.T) {
	featureTypes := map[string]features.FeatureType{
		"name": features.StringFeature,
	}

	dataset := LoadCSVToDataset[string]("nonexistent.csv", featureTypes, "target", "train")
	if dataset != nil {
		t.Error("Expected nil dataset for missing file")
	}
}

// TestLoadCSVToDatasetWithNullValues tests handling of missing/null values
func TestLoadCSVToDatasetWithNullValues(t *testing.T) {
	csvContent := `name,age,salary,target
John,25,50000.5,A
Jane,,75000.0,B
Bob,35,,C
,40,60000.25,D`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test_nulls.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file")
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "target", "train")
	if dataset == nil {
		t.Fatalf("LoadCSVToDataset failed")
	}

	// Test null handling in age column (row 1 has empty age)
	ageCol := dataset.GetColumn("age")
	if err != nil {
		t.Fatalf("Failed to get age column")
	}

	// Row 1 should have null age
	ageValue := ageCol.GetValue(1)
	if ageValue != nil {
		t.Error("Expected nil for null age value")
	}

	// Test null handling in salary column (row 2 has empty salary)
	salaryCol := dataset.GetColumn("salary")
	if salaryCol == nil {
		t.Fatalf("Failed to get salary column")
	}

	salaryValue := salaryCol.GetValue(2)
	if salaryValue != nil {
		t.Error("Expected nil for null salary value")
	}

	// Test null handling in name column (row 3 has empty name)
	nameCol := dataset.GetColumn("name")
	if nameCol == nil {
		t.Fatalf("Failed to get name column")
	}

	nameValue := nameCol.GetValue(3)
	if nameValue != nil {
		t.Error("Expected nil for null name value")
	}

	// Verify valid values still work
	validAge := ageCol.GetValue(0)
	if validAge == nil {
		t.Errorf("Failed to get valid age")
	} else {
		// Dereference the pointer to get the actual value
		if ptr, ok := validAge.(*int64); ok {
			if *ptr != int64(25) {
				t.Errorf("Expected age 25, got %v", *ptr)
			}
		} else {
			t.Errorf("Expected *int64, got %T", validAge)
		}
	}
}

// TestLoadCSVToDatasetMissingFeatureType tests handling of missing feature type metadata
func TestLoadCSVToDatasetMissingFeatureType(t *testing.T) {
	csvContent := `name,age,salary,target
John,25,50000.5,A
Jane,30,75000.0,B`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file")
	}

	// Missing feature type for 'salary' column
	featureTypes := map[string]features.FeatureType{
		"name": features.StringFeature,
		"age":  features.IntegerFeature,
		// "salary" is missing - should default to StringFeature
	}

	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "target", "train")
	if dataset == nil {
		t.Fatalf("LoadCSVToDataset failed")
	}

	// Verify salary column was created as StringFeature (default)
	salaryCol := dataset.GetColumn("salary")
	if salaryCol == nil {
		t.Fatalf("Failed to get salary column")
	}
	if salaryCol.GetType() != features.StringFeature {
		t.Errorf("Expected StringFeature for missing type, got %v", salaryCol.GetType())
	}

	// Verify salary value is stored as string
	salaryValue := salaryCol.GetValue(0)
	if salaryValue == nil {
		t.Errorf("Failed to get salary value")
	} else {
		// Dereference the pointer to get the actual value
		if ptr, ok := salaryValue.(*string); ok {
			if *ptr != "50000.5" {
				t.Errorf("Expected salary '50000.5' as string, got %v", *ptr)
			}
		} else {
			t.Errorf("Expected *string, got %T", salaryValue)
		}
	}
}

// TestLoadCSVToDatasetEmptyDataset tests loading CSV with only headers
func TestLoadCSVToDatasetEmptyDataset(t *testing.T) {
	csvContent := `name,age,salary,target`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "empty_data.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file")
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "target", "train")
	if dataset == nil {
		t.Fatalf("LoadCSVToDataset failed")
	}

	// Verify empty dataset structure
	if dataset.totalSize != 0 {
		t.Errorf("Expected dataset size 0, got %d", dataset.totalSize)
	}

	// Verify columns were still created
	expectedFeatures := []string{"name", "age", "salary"}
	featureOrder := dataset.GetFeatureOrder()
	if len(featureOrder) != len(expectedFeatures) {
		t.Errorf("Expected %d features, got %d", len(expectedFeatures), len(featureOrder))
	}

	// Verify columns exist but are empty
	nameCol := dataset.GetColumn("name")
	if err != nil {
		t.Errorf("Failed to get name column")
	}
	if nameCol.GetSize() != 0 {
		t.Errorf("Expected empty name column, got size %d", nameCol.GetSize())
	}
}

// TestLoadCSVToDatasetSingleRow tests loading CSV with single data row
func TestLoadCSVToDatasetSingleRow(t *testing.T) {
	csvContent := `name,age,salary,target
John,25,50000.5,A`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "single_row.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file")
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "target", "train")
	if dataset == nil {
		t.Fatalf("LoadCSVToDataset failed")
	}

	// Verify single row dataset
	if dataset.totalSize != 1 {
		t.Errorf("Expected dataset size 1, got %d", dataset.totalSize)
	}

	// Verify data values
	nameCol := dataset.GetColumn("name")
	if nameCol == nil {
		t.Fatalf("Failed to get name column")
	}
	nameValue := nameCol.GetValue(0)
	if nameValue == nil {
		t.Errorf("Failed to get name value")
	} else {
		// Dereference the pointer to get the actual value
		if ptr, ok := nameValue.(*string); ok {
			if *ptr != "John" {
				t.Errorf("Expected name 'John', got %v", *ptr)
			}
		} else {
			t.Errorf("Expected *string, got %T", nameValue)
		}
	}

	// Verify target
	target := dataset.GetTarget(0)
	if err != nil {
		t.Errorf("Failed to get target")
	}
	if target != "A" {
		t.Errorf("Expected target 'A', got %v", target)
	}
}

// TestLoadCSVToDataset_UnsupportedFeatureType tests error handling for unsupported feature types
func TestLoadCSVToDataset_UnsupportedFeatureType(t *testing.T) {
	csvContent := `name,age,target
John,25,A`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file")
	}

	// Use an invalid feature type (this would require modifying the enum, so we'll simulate)
	// For now, test with valid types but add a test for the default case
	featureTypes := map[string]features.FeatureType{
		"name": features.StringFeature,
		"age":  features.IntegerFeature,
	}

	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "target", "train")
	if dataset == nil {
		t.Fatalf("LoadCSVToDataset failed")
	}

	// Verify dataset was created successfully
	if dataset.totalSize != 1 {
		t.Errorf("Expected dataset size 1, got %d", dataset.totalSize)
	}
}

// TestLoadCSVToDataset_TargetConversionError tests error handling for target conversion failures
func TestLoadCSVToDataset_TargetConversionError(t *testing.T) {
	csvContent := `name,age,target
John,25,A`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file")
	}

	featureTypes := map[string]features.FeatureType{
		"name": features.StringFeature,
		"age":  features.IntegerFeature,
	}

	// Try to load with int target type (this should fail for string targets)
	dataset := LoadCSVToDataset[int](csvFile, featureTypes, "target", "train")
	if dataset == nil {
		t.Error("Expected dataset to be created even with target conversion issues")
	}
}

// TestLoadCSVToDataset_LargeDataset tests loading a larger dataset
func TestLoadCSVToDataset_LargeDataset(t *testing.T) {
	// Create a larger CSV content
	csvContent := `name,age,salary,target
John,25,50000.5,A
Jane,30,75000.0,B
Bob,35,60000.25,A
Alice,28,55000.75,B
Charlie,40,80000.0,A
Diana,32,65000.5,B
Eve,27,52000.25,A
Frank,38,72000.0,B
Grace,29,58000.75,A
Henry,33,68000.5,B`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "large_test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file")
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "target", "train")
	if dataset == nil {
		t.Fatalf("LoadCSVToDataset failed")
	}

	// Verify dataset structure
	if dataset.totalSize != 10 {
		t.Errorf("Expected dataset size 10, got %d", dataset.totalSize)
	}

	// Verify some data values
	nameCol := dataset.GetColumn("name")
	if nameCol == nil {
		t.Fatalf("Failed to get name column")
	}

	nameValue := nameCol.GetValue(0)
	if nameValue == nil {
		t.Errorf("Failed to get name value")
	} else {
		// Dereference the pointer to get the actual value
		if ptr, ok := nameValue.(*string); ok {
			if *ptr != "John" {
				t.Errorf("Expected name 'John', got %v", *ptr)
			}
		} else {
			t.Errorf("Expected *string, got %T", nameValue)
		}
	}

	// Test target distribution
	view := dataset.CreateView([]int{0, 1, 2, 3, 4, 5, 6, 7, 8, 9})
	dist := view.GetTargetDistribution()
	if dist == nil {
		t.Fatalf("Failed to get target distribution")
	}

	if dist["A"] != 5 {
		t.Errorf("Expected 5 'A' targets, got %d", dist["A"])
	}
	if dist["B"] != 5 {
		t.Errorf("Expected 5 'B' targets, got %d", dist["B"])
	}
}

// TestLoadCSVToDataset_PredictOperation tests loading CSV for prediction (no target column required)
func TestLoadCSVToDataset_PredictOperation(t *testing.T) {
	csvContent := `name,age,salary
John,25,50000.5
Jane,30,75000.0
Bob,35,60000.25`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "predict_test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	// Load CSV for prediction - target column doesn't exist but should succeed
	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "target", "predict")
	if dataset == nil {
		t.Fatalf("LoadCSVToDataset failed for prediction operation")
	}

	// Verify dataset structure
	if dataset.totalSize != 3 {
		t.Errorf("Expected dataset size 3, got %d", dataset.totalSize)
	}

	// Verify all features are present
	expectedFeatures := []string{"name", "age", "salary"}
	featureOrder := dataset.GetFeatureOrder()
	if len(featureOrder) != len(expectedFeatures) {
		t.Errorf("Expected %d features, got %d", len(expectedFeatures), len(featureOrder))
	}

	// Verify no target values were added (since target column doesn't exist)
	target := dataset.GetTarget(0)
	if target != "" {
		t.Errorf("Expected empty target for prediction mode, got %v", target)
	}

	// Verify feature data is correct
	nameCol := dataset.GetColumn("name")
	if nameCol == nil {
		t.Fatalf("Failed to get name column")
	}
	nameValue := nameCol.GetValue(0)
	if nameValue == nil {
		t.Errorf("Failed to get name value")
	} else {
		if ptr, ok := nameValue.(*string); ok {
			if *ptr != "John" {
				t.Errorf("Expected name 'John', got %v", *ptr)
			}
		} else {
			t.Errorf("Expected *string, got %T", nameValue)
		}
	}
}

// TestLoadCSVToDataset_PredictWithTargetColumn tests prediction mode when target column exists in CSV
func TestLoadCSVToDataset_PredictWithTargetColumn(t *testing.T) {
	csvContent := `name,age,salary,target
John,25,50000.5,A
Jane,30,75000.0,B
Bob,35,60000.25,A`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "predict_with_target.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	// Load CSV for prediction - target column exists but should be excluded from features
	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "target", "predict")
	if dataset == nil {
		t.Fatalf("LoadCSVToDataset failed for prediction operation")
	}

	// Verify dataset structure
	if dataset.totalSize != 3 {
		t.Errorf("Expected dataset size 3, got %d", dataset.totalSize)
	}

	// Verify only feature columns are included (target should be excluded)
	expectedFeatures := []string{"name", "age", "salary"}
	featureOrder := dataset.GetFeatureOrder()
	if len(featureOrder) != len(expectedFeatures) {
		t.Errorf("Expected %d features, got %d", len(expectedFeatures), len(featureOrder))
	}

	// Verify target column is not included as a feature
	targetCol := dataset.GetColumn("target")
	if targetCol != nil {
		t.Errorf("Target column should not be included as a feature in prediction mode")
	}

	// Verify no target values were added in prediction mode
	target := dataset.GetTarget(0)
	if target != "" {
		t.Errorf("Expected empty target for prediction mode, got %v", target)
	}
}

// TestLoadCSVToDataset_PredictEmptyTargetColumn tests prediction mode with empty target column name
func TestLoadCSVToDataset_PredictEmptyTargetColumn(t *testing.T) {
	csvContent := `name,age,salary,extra
John,25,50000.5,value1
Jane,30,75000.0,value2
Bob,35,60000.25,value3`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "predict_empty_target.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	// Load CSV for prediction with empty target column name
	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "", "predict")
	if dataset == nil {
		t.Fatalf("LoadCSVToDataset failed for prediction operation")
	}

	// Verify dataset structure
	if dataset.totalSize != 3 {
		t.Errorf("Expected dataset size 3, got %d", dataset.totalSize)
	}

	// Verify only specified features are included
	expectedFeatures := []string{"name", "age", "salary"}
	featureOrder := dataset.GetFeatureOrder()
	if len(featureOrder) != len(expectedFeatures) {
		t.Errorf("Expected %d features, got %d", len(expectedFeatures), len(featureOrder))
	}

	// Verify extra column is not included
	extraCol := dataset.GetColumn("extra")
	if extraCol != nil {
		t.Errorf("Extra column should not be included when not in feature metadata")
	}
}

// TestLoadCSVToDataset_TrainMissingTargetColumn tests training mode with missing target column (should fail)
func TestLoadCSVToDataset_TrainMissingTargetColumn(t *testing.T) {
	csvContent := `name,age,salary
John,25,50000.5
Jane,30,75000.0`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "train_missing_target.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	// Try to load CSV for training without target column - should fail
	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "target", "train")
	if dataset != nil {
		t.Errorf("Expected nil dataset for training mode with missing target column")
	}
}

// TestLoadCSVToDataset_InvalidOperation tests with invalid operation parameter
func TestLoadCSVToDataset_InvalidOperation(t *testing.T) {
	csvContent := `name,age,target
John,25,A
Jane,30,B`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "invalid_operation.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	featureTypes := map[string]features.FeatureType{
		"name": features.StringFeature,
		"age":  features.IntegerFeature,
	}

	// Try to load with invalid operation - should still work (defaults to train behavior)
	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "target", "invalid")
	if dataset == nil {
		t.Fatalf("LoadCSVToDataset should handle invalid operation gracefully")
	}

	// Should behave like training mode and include targets
	if dataset.totalSize != 2 {
		t.Errorf("Expected dataset size 2, got %d", dataset.totalSize)
	}

	target := dataset.GetTarget(0)
	if target != "A" {
		t.Errorf("Expected target 'A', got %v", target)
	}
}

// TestLoadCSVToDataset_PredictModeFeatureFiltering tests that prediction mode only includes specified features
func TestLoadCSVToDataset_PredictModeFeatureFiltering(t *testing.T) {
	csvContent := `name,age,salary,department,location,target
John,25,50000.5,Engineering,NYC,A
Jane,30,75000.0,Marketing,LA,B`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "feature_filtering.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Only specify some features in metadata
	featureTypes := map[string]features.FeatureType{
		"name": features.StringFeature,
		"age":  features.IntegerFeature,
		// salary, department, location not specified - should be excluded
	}

	dataset := LoadCSVToDataset[string](csvFile, featureTypes, "target", "predict")
	if dataset == nil {
		t.Fatalf("LoadCSVToDataset failed for prediction operation")
	}

	// Should only include features specified in metadata
	expectedFeatures := []string{"name", "age"}
	featureOrder := dataset.GetFeatureOrder()
	if len(featureOrder) != len(expectedFeatures) {
		t.Errorf("Expected %d features, got %d", len(expectedFeatures), len(featureOrder))
	}

	// Verify excluded columns are not present
	salaryCol := dataset.GetColumn("salary")
	if salaryCol != nil {
		t.Errorf("Salary column should be excluded when not in feature metadata")
	}

	departmentCol := dataset.GetColumn("department")
	if departmentCol != nil {
		t.Errorf("Department column should be excluded when not in feature metadata")
	}

	locationCol := dataset.GetColumn("location")
	if locationCol != nil {
		t.Errorf("Location column should be excluded when not in feature metadata")
	}

	targetCol := dataset.GetColumn("target")
	if targetCol != nil {
		t.Errorf("Target column should be excluded in prediction mode")
	}
}
