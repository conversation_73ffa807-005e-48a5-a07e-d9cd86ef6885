// Package training provides core decision tree training functionality.
//
// This package implements the C4.5 algorithm components including:
// - Entropy-based impurity calculations
// - Split evaluation strategies
// - Stopping criteria evaluation
//
// Design Principles:
// - Simple entropy-only impurity calculation
// - Memory-efficient view-based operations
// - Type-safe split evaluation
package training

import (
	"math"
)

// CalculateEntropy calculates entropy for the given target distribution.
//
// Entropy formula: -Σ(p_i * log2(p_i)) where p_i is probability of class i
// Used as the standard impurity measure in C4.5 decision trees.
//
// Args:
// - distribution: Map of target values to their counts (must sum to totalSamples)
// - totalSamples: Total number of samples (must be > 0)
//
// Returns: Entropy value in range [0.0, log2(k)] where k is number of classes
// Constraints: totalSamples must be > 0, distribution counts must be >= 0
// Performance: O(k) where k is number of unique target values
// Relationships: Core calculation used by all split evaluators
// Side effects: None (pure mathematical calculation)
//
// Special cases:
// - Pure node (one class): returns 0.0
// - Empty distribution: returns 0.0
// - Equal distribution: returns log2(k)
//
// Example:
//
//	// Pure node
//	entropy := CalculateEntropy(map[string]int{"yes": 6}, 6) // Returns 0.0
//
//	// Mixed node
//	entropy := CalculateEntropy(map[string]int{"yes": 4, "no": 2}, 6) // Returns ~0.918
func CalculateEntropy[T comparable](distribution map[T]int, totalSamples int) float64 {
	if totalSamples <= 0 {
		return 0.0
	}

	entropy := 0.0
	for _, count := range distribution {
		if count > 0 {
			probability := float64(count) / float64(totalSamples)
			entropy -= probability * math.Log2(probability)
		}
	}

	return entropy
}

// CalculateGainRatio calculates gain ratio for categorical splits using C4.5 algorithm.
//
// Gain ratio formula: Information Gain / Split Information
// - Information Gain = Parent Entropy - Weighted Average Child Entropy
// - Split Information = -Σ(p_i * log2(p_i)) where p_i is proportion of samples in child i
//
// Gain ratio addresses the bias of information gain toward splits with many outcomes
// by normalizing against the intrinsic information of the split itself.
//
// Args:
// - parentDistribution: Target distribution of parent node before split
// - parentSamples: Total samples in parent node
// - childDistributions: Map of child partitions to their target distributions
//
// Returns: Gain ratio value in range [0.0, 1.0], higher values indicate better splits
// Constraints: parentSamples > 0, childDistributions must not be empty
// Performance: O(k*c) where k is classes and c is number of children
// Relationships: Used by CategoricalSplitEvaluator for split quality assessment
// Side effects: None (pure mathematical calculation)
//
// Special cases:
// - No split information (all samples in one child): returns 0.0
// - Perfect split (pure children): approaches 1.0
// - Uniform split with mixed children: intermediate values
//
// Example:
//
//	// Parent: {"yes": 4, "no": 6}, Children: {"A": {"yes": 3, "no": 1}, "B": {"yes": 1, "no": 5}}
//	gainRatio := CalculateGainRatio(parentDist, 10, childDists) // Returns ~0.31
func CalculateGainRatio[T comparable](
	parentDistribution map[T]int,
	parentSamples int,
	childDistributions map[interface{}]map[T]int,
) float64 {
	if parentSamples <= 0 || len(childDistributions) == 0 {
		return 0.0
	}

	// Calculate parent entropy
	parentEntropy := CalculateEntropy(parentDistribution, parentSamples)

	// Calculate weighted average child entropy (information gain component)
	weightedChildEntropy := 0.0
	splitInformation := 0.0

	for _, childDist := range childDistributions {
		childSamples := 0
		for _, count := range childDist {
			childSamples += count
		}

		if childSamples > 0 {
			// Information gain component
			childEntropy := CalculateEntropy(childDist, childSamples)
			weight := float64(childSamples) / float64(parentSamples)
			weightedChildEntropy += weight * childEntropy

			// Split information component
			splitInformation -= weight * math.Log2(weight)
		}
	}

	// Calculate information gain
	informationGain := parentEntropy - weightedChildEntropy

	// Prevent division by zero
	if splitInformation <= 0 {
		return 0.0
	}

	// Calculate gain ratio
	gainRatio := informationGain / splitInformation
	return math.Max(0.0, gainRatio)
}
