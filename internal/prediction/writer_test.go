package prediction

import (
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/berrijam/mulberri/internal/types"
)

func TestPredictionWriter_WriteDetailedCSV(t *testing.T) {
	// Create test data
	predictions := []*types.DetailedPrediction{
		{
			RowIndex:          0,
			InputFeatures:     map[string]interface{}{"age": 25, "income": 30000},
			Prediction:        "no",
			Confidence:        0.85,
			DecisionPath:      []string{"age <= 30.000000", "income <= 40000.000000", "LEAF[no] (confidence: 0.850)"},
			DecisionRule:      "IF age <= 30.000000 AND income <= 40000.000000 THEN no",
			ClassDistribution: map[string]int{"yes": 2, "no": 8},
		},
		{
			RowIndex:          1,
			InputFeatures:     map[string]interface{}{"age": 35, "income": 60000},
			Prediction:        "yes",
			Confidence:        0.92,
			DecisionPath:      []string{"age > 30.000000", "income > 50000.000000", "LEAF[yes] (confidence: 0.920)"},
			DecisionRule:      "IF age > 30.000000 AND income > 50000.000000 THEN yes",
			ClassDistribution: map[string]int{"yes": 9, "no": 1},
		},
	}

	// Create temporary file
	tempDir := t.TempDir()
	outputPath := filepath.Join(tempDir, "detailed_predictions.csv")

	// Create writer and write predictions
	writer := NewPredictionWriter()
	err := writer.WriteDetailedCSV(predictions, outputPath, true)
	if err != nil {
		t.Fatalf("WriteDetailedCSV failed: %v", err)
	}

	// Verify file exists
	if _, err := os.Stat(outputPath); os.IsNotExist(err) {
		t.Fatalf("Output file was not created: %s", outputPath)
	}

	// Read and verify content
	content, err := os.ReadFile(outputPath)
	if err != nil {
		t.Fatalf("Failed to read output file: %v", err)
	}

	contentStr := string(content)

	// Check headers
	if !strings.Contains(contentStr, "row_index,prediction,confidence,decision_rule") {
		t.Errorf("Expected headers not found in output")
	}

	// Check first prediction data
	if !strings.Contains(contentStr, "0,no,0.8500") {
		t.Errorf("Expected first prediction data not found")
	}

	// Check decision rule
	if !strings.Contains(contentStr, "IF age <= 30.000000 AND income <= 40000.000000 THEN no") {
		t.Errorf("Expected decision rule not found")
	}

	// Check second prediction
	if !strings.Contains(contentStr, "1,yes,0.9200") {
		t.Errorf("Expected second prediction data not found")
	}
}

func TestPredictionWriter_WriteSimpleCSV(t *testing.T) {
	// Create test data
	predictions := []string{"no", "yes", "no", "yes"}

	// Create temporary file
	tempDir := t.TempDir()
	outputPath := filepath.Join(tempDir, "simple_predictions.csv")

	// Create writer and write predictions
	writer := NewPredictionWriter()
	err := writer.WriteSimpleCSV(predictions, outputPath, true)
	if err != nil {
		t.Fatalf("WriteSimpleCSV failed: %v", err)
	}

	// Verify file exists
	if _, err := os.Stat(outputPath); os.IsNotExist(err) {
		t.Fatalf("Output file was not created: %s", outputPath)
	}

	// Read and verify content
	content, err := os.ReadFile(outputPath)
	if err != nil {
		t.Fatalf("Failed to read output file: %v", err)
	}

	contentStr := string(content)

	// Check header
	if !strings.Contains(contentStr, "prediction") {
		t.Errorf("Expected header not found in output")
	}

	// Check predictions
	lines := strings.Split(strings.TrimSpace(contentStr), "\n")
	if len(lines) != 5 { // header + 4 predictions
		t.Errorf("Expected 5 lines, got %d", len(lines))
	}

	expectedPredictions := []string{"prediction", "no", "yes", "no", "yes"}
	for i, line := range lines {
		if line != expectedPredictions[i] {
			t.Errorf("Line %d: expected %s, got %s", i, expectedPredictions[i], line)
		}
	}
}

