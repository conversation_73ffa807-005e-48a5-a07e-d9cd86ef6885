# Profile Analysis Quick Reference Guide

## 🚀 Quick Start

```bash
# 1. Generate profiles
./mulberri train -i data.csv -t target -o model.dt -f features.yaml --cpu-profile --mem-profile

# 2. Analyze profiles automatically
./scripts/analyze_profiles.sh

# 3. Explore profiles interactively
./scripts/profile_reader.sh
```

## 📊 Reading Profile Output

### CPU Profile Format
```
      flat  flat%   sum%        cum   cum%
      10ms 50.00% 50.00%       10ms 50.00%  function_name
```

- **flat**: Time spent in this function only
- **flat%**: Percentage of total time in this function
- **sum%**: Cumulative percentage up to this line
- **cum**: Time spent in this function + callees
- **cum%**: Cumulative percentage including callees

### Memory Profile Format
```
      flat  flat%   sum%        cum   cum%
 1762.94kB 53.42% 53.42%  1762.94kB 53.42%  function_name
```

- **flat**: Memory allocated by this function only
- **flat%**: Percentage of total memory allocated here
- **cum**: Memory allocated by this function + callees

## 🎯 What to Look For

### 🔴 CRITICAL & SERIOUS (Fix Immediately)

| Issue | Indicator | Action |
|-------|-----------|--------|
| CPU Hotspots | flat% > 20% | Optimize algorithm |
| Memory Leaks | Growing allocations | Fix leak source |
| Large Allocations | Single alloc > 1MB | Implement streaming |
| Blocking Operations | High wait times | Make async |

### 🟡 CRITICAL BUT NOT SERIOUS (Optimize Soon)

| Issue | Indicator | Action |
|-------|-----------|--------|
| JSON Overhead | encoding/json in top 10 | Use binary format |
| Logging Overhead | logger/zap in top 10 | Reduce verbosity |
| Pool Inefficiency | sync.Pool allocations | Tune pool sizes |
| GC Pressure | runtime.gc frequent | Reduce allocations |

### ✅ OKAY (Monitor)

| Pattern | Indicator | Action |
|---------|-----------|--------|
| Algorithm Functions | BuildTree, findBestSplit | Monitor performance |
| Expected Overhead | pprof, profiling functions | Disable in production |
| Normal GC | Reasonable gc times | Continue monitoring |
| Library Usage | Standard library calls | Document baselines |

## 🔍 Common Patterns & Solutions

### Pattern: High JSON Serialization Time
```
encoding/json.Marshal         50% CPU
encoding/json.(*encodeState)   25% CPU
```
**Solution**: Use binary serialization (protobuf, msgpack) for large models

### Pattern: Excessive Logging
```
go.uber.org/zap.(*Logger)     30% CPU
logger.Debug                  20% CPU
```
**Solution**: Implement log levels, use async logging, reduce debug logs

### Pattern: Memory Pool Thrashing
```
sync.(*Pool).Get              40% memory
sync.(*Pool).pinSlow          30% memory
```
**Solution**: Tune pool sizes, pre-allocate pools, reduce pool contention

### Pattern: Large Dataset Loading
```
csv.Reader                    60% memory
dataset.LoadCSV               40% memory
```
**Solution**: Implement streaming, process in chunks, use memory mapping

## 📈 Performance Analysis Workflow

### 1. Baseline Establishment
```bash
# Profile with current implementation
./mulberri train --cpu-profile --mem-profile [args]

# Document baseline metrics
echo "Baseline: $(date)" >> performance_log.txt
```

### 2. Identify Bottlenecks
```bash
# Generate analysis report
./scripts/analyze_profiles.sh

# Focus on CRITICAL & SERIOUS issues first
grep "🔴" profile_reports/analysis_report_*.md
```

### 3. Implement Optimizations
- Fix one issue at a time
- Profile after each change
- Compare before/after results

### 4. Validate Improvements
```bash
# Compare profiles
go tool pprof -base=old.prof new.prof

# Measure improvement
echo "Improvement: X% faster, Y% less memory" >> performance_log.txt
```

## 🛠️ Useful Commands

### Quick Analysis
```bash
# Top CPU consumers
go tool pprof -top profiles/train-cpu-*.prof

# Top memory allocators  
go tool pprof -top profiles/train-mem-*.prof

# Function-specific analysis
go tool pprof -list=FunctionName profiles/train-cpu-*.prof
```

### Visual Analysis
```bash
# Generate flame graph (requires graphviz)
go tool pprof -svg profiles/train-cpu-*.prof > flame.svg

# Web interface
go tool pprof -http=:8080 profiles/train-cpu-*.prof
```

### Comparative Analysis
```bash
# Compare two profiles
go tool pprof -base=baseline.prof current.prof

# Diff analysis
go tool pprof -diff_base=old.prof new.prof
```

## 📋 Analysis Checklist

### Before Optimization
- [ ] Profile with realistic dataset sizes
- [ ] Establish performance baselines
- [ ] Identify top 3 bottlenecks
- [ ] Document current behavior

### During Optimization
- [ ] Fix one issue at a time
- [ ] Profile after each change
- [ ] Verify improvements with benchmarks
- [ ] Test with various dataset sizes

### After Optimization
- [ ] Compare before/after profiles
- [ ] Update performance baselines
- [ ] Document optimization results
- [ ] Monitor for regressions

## 🎯 Target Performance Goals

### Small Datasets (< 1k samples)
- Training: < 100ms
- Prediction: < 10ms
- Memory: < 10MB

### Medium Datasets (1k - 10k samples)
- Training: < 1s
- Prediction: < 100ms  
- Memory: < 100MB

### Large Datasets (> 10k samples)
- Training: < 10s
- Prediction: < 1s
- Memory: < 1GB

## 🚨 Red Flags

Watch out for these warning signs:

| Red Flag | Meaning | Action |
|----------|---------|--------|
| Memory growth over time | Potential leak | Profile memory over time |
| CPU usage > 100% | Inefficient algorithms | Optimize hot paths |
| Allocation rate > 1GB/s | Excessive allocations | Implement object pooling |
| GC time > 10% | GC pressure | Reduce allocations |

## 📚 Additional Resources

- [Go pprof documentation](https://pkg.go.dev/runtime/pprof)
- [Profiling Go programs](https://go.dev/blog/pprof)
- [Memory profiling with pprof](https://go.dev/blog/profiling-go-programs)
- [Flame graphs](http://www.brendangregg.com/flamegraphs.html)

## 🔧 Troubleshooting

### No Profile Data
- Ensure profiling flags are enabled
- Check that operations run long enough (>100ms)
- Verify profile files are created and non-empty

### Misleading Results
- Profile with realistic workloads
- Disable profiling overhead in production
- Use multiple profile samples for accuracy

### Performance Regressions
- Compare profiles before/after changes
- Use automated benchmarks in CI/CD
- Monitor key metrics over time
