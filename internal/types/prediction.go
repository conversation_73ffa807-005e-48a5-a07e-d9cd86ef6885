// Package types contains shared type definitions used across multiple packages.
package types

// DetailedPrediction contains comprehensive prediction information for a single row.
//
// This structure provides detailed information about how a prediction was made,
// including the decision path, confidence, and reasoning for transparency.
type DetailedPrediction struct {
	// RowIndex indicates which row this prediction is for
	RowIndex int

	// InputFeatures contains the feature values used for this prediction
	InputFeatures map[string]interface{}

	// Prediction is the final predicted class
	Prediction string

	// Confidence indicates the confidence level of the prediction (0.0 to 1.0)
	Confidence float64

	// DecisionPath contains the sequence of decisions made during tree traversal
	DecisionPath []string

	// DecisionRule contains the complete rule in human-readable format
	DecisionRule string

	// ClassDistribution shows the class distribution at the leaf node
	ClassDistribution map[string]int

	// Error contains any error that occurred during prediction for this row
	Error string
}
