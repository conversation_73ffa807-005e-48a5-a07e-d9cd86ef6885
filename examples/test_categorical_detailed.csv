row_index,prediction,confidence,decision_rule,decision_path,input_features,class_distribution,error
0,yes,1.0000,IF education = college AND income = high THEN yes,education = college → income = high → LEAF[yes] (confidence: 1.000),"{education:college, income:high}",{yes:2},
1,no,1.0000,IF education = high_school THEN no,education = high_school → LEAF[no] (confidence: 1.000),"{education:high_school, income:low}",{no:3},
2,yes,1.0000,IF education = graduate THEN yes,education = graduate → LEAF[yes] (confidence: 1.000),"{education:graduate, income:medium}",{yes:3},
3,no,1.0000,IF education = college AND income = low THEN no,education = college → income = low → LEAF[no] (confidence: 1.000),"{education:college, income:low}",{no:1},
4,yes,0.6000,IF education = unknown THEN yes,education = unknown → NO_CHILD -> yes,"{education:unknown, income:high}","{no:4, yes:6}",
