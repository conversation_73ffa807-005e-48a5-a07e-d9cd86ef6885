#!/bin/bash

# Interactive Profile Reader for Mulberri
# This script provides an interactive way to explore profile data

set -e

PROFILES_DIR="./profiles"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

show_menu() {
    echo
    echo -e "${CYAN}=== Mulberri Profile Reader ===${NC}"
    echo
    echo "Available profiles:"
    ls -la "$PROFILES_DIR"/*.prof 2>/dev/null | awk '{print NR". "$9" ("$5" bytes, "$6" "$7" "$8")"}' || echo "No profiles found"
    echo
    echo -e "${YELLOW}Analysis Options:${NC}"
    echo "1. Quick CPU analysis (top functions)"
    echo "2. Quick memory analysis (top allocators)"
    echo "3. Detailed CPU analysis (with source code)"
    echo "4. Detailed memory analysis (allocation patterns)"
    echo "5. Compare two profiles"
    echo "6. Generate flame graph (requires graphviz)"
    echo "7. Interactive pprof session"
    echo "8. Export analysis to file"
    echo "9. Show profile statistics"
    echo "0. Exit"
    echo
}

select_profile() {
    local profile_type="$1"
    echo -e "${BLUE}Available $profile_type profiles:${NC}"
    
    local profiles=($(ls "$PROFILES_DIR"/*$profile_type*.prof 2>/dev/null))
    if [ ${#profiles[@]} -eq 0 ]; then
        echo "No $profile_type profiles found"
        return 1
    fi
    
    for i in "${!profiles[@]}"; do
        local file="${profiles[$i]}"
        local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "unknown")
        local date=$(stat -f%Sm "$file" 2>/dev/null || stat -c%y "$file" 2>/dev/null || echo "unknown")
        echo "$((i+1)). $(basename "$file") (${size} bytes, ${date})"
    done
    
    echo -n "Select profile (1-${#profiles[@]}): "
    read selection
    
    if [[ "$selection" =~ ^[0-9]+$ ]] && [ "$selection" -ge 1 ] && [ "$selection" -le ${#profiles[@]} ]; then
        echo "${profiles[$((selection-1))]}"
        return 0
    else
        echo "Invalid selection"
        return 1
    fi
}

quick_cpu_analysis() {
    local profile=$(select_profile "cpu")
    [ $? -ne 0 ] && return
    
    echo -e "${GREEN}Quick CPU Analysis: $(basename "$profile")${NC}"
    echo
    
    # Basic stats
    echo -e "${YELLOW}Profile Statistics:${NC}"
    go tool pprof -text "$profile" | head -5
    echo
    
    # Top functions
    echo -e "${YELLOW}Top 10 CPU Consumers:${NC}"
    go tool pprof -text "$profile" | head -15 | tail -10
    echo
    
    # Look for specific patterns
    echo -e "${YELLOW}Algorithm Functions:${NC}"
    go tool pprof -text "$profile" | grep -E "(BuildTree|buildNode|findBestSplit|training|prediction)" | head -5
    echo
    
    echo -e "${YELLOW}System/Runtime Functions:${NC}"
    go tool pprof -text "$profile" | grep -E "(runtime|syscall|gc)" | head -5
    echo
}

quick_memory_analysis() {
    local profile=$(select_profile "mem")
    [ $? -ne 0 ] && return
    
    echo -e "${GREEN}Quick Memory Analysis: $(basename "$profile")${NC}"
    echo
    
    # Top allocators
    echo -e "${YELLOW}Top 10 Memory Allocators:${NC}"
    go tool pprof -text "$profile" | head -15 | tail -10
    echo
    
    # Memory by category
    echo -e "${YELLOW}Algorithm Memory Usage:${NC}"
    go tool pprof -text "$profile" | grep -E "(BuildTree|buildNode|findBestSplit|training|prediction|dataset)" | head -5
    echo
    
    echo -e "${YELLOW}System Memory Usage:${NC}"
    go tool pprof -text "$profile" | grep -E "(runtime|gc|pool|sync)" | head -5
    echo
    
    echo -e "${YELLOW}Library Memory Usage:${NC}"
    go tool pprof -text "$profile" | grep -E "(json|yaml|csv|log)" | head -5
    echo
}

detailed_cpu_analysis() {
    local profile=$(select_profile "cpu")
    [ $? -ne 0 ] && return
    
    echo -e "${GREEN}Detailed CPU Analysis: $(basename "$profile")${NC}"
    echo
    
    echo "Enter function name to analyze (or press Enter for top function):"
    read func_name
    
    if [ -z "$func_name" ]; then
        # Get top function
        func_name=$(go tool pprof -text "$profile" | awk 'NR==7 {print $6}')
    fi
    
    if [ -n "$func_name" ]; then
        echo -e "${YELLOW}Source code analysis for: $func_name${NC}"
        go tool pprof -list="$func_name" "$profile" 2>/dev/null || echo "Source code not available or function not found"
        echo
        
        echo -e "${YELLOW}Call graph for: $func_name${NC}"
        go tool pprof -peek="$func_name" "$profile" 2>/dev/null || echo "Call graph not available"
    fi
}

detailed_memory_analysis() {
    local profile=$(select_profile "mem")
    [ $? -ne 0 ] && return
    
    echo -e "${GREEN}Detailed Memory Analysis: $(basename "$profile")${NC}"
    echo
    
    # Allocation patterns
    echo -e "${YELLOW}Allocation Patterns:${NC}"
    go tool pprof -alloc_space -text "$profile" | head -10
    echo
    
    echo -e "${YELLOW}Allocation Objects:${NC}"
    go tool pprof -alloc_objects -text "$profile" | head -10
    echo
    
    echo -e "${YELLOW}In-use Space:${NC}"
    go tool pprof -inuse_space -text "$profile" | head -10
    echo
    
    echo -e "${YELLOW}In-use Objects:${NC}"
    go tool pprof -inuse_objects -text "$profile" | head -10
    echo
}

compare_profiles() {
    echo "Select first profile:"
    local profile1=$(select_profile "")
    [ $? -ne 0 ] && return
    
    echo "Select second profile:"
    local profile2=$(select_profile "")
    [ $? -ne 0 ] && return
    
    echo -e "${GREEN}Comparing profiles:${NC}"
    echo "Base: $(basename "$profile1")"
    echo "Compare: $(basename "$profile2")"
    echo
    
    echo -e "${YELLOW}Difference Analysis:${NC}"
    go tool pprof -base="$profile1" -text "$profile2" | head -15
}

generate_flame_graph() {
    local profile=$(select_profile "cpu")
    [ $? -ne 0 ] && return
    
    local output_file="flame_graph_$(date +%Y%m%d_%H%M%S).svg"
    
    echo -e "${GREEN}Generating flame graph...${NC}"
    
    # Check if graphviz is available
    if ! command -v dot &> /dev/null; then
        echo -e "${RED}Error: graphviz not installed. Install with:${NC}"
        echo "  Ubuntu/Debian: sudo apt install graphviz"
        echo "  macOS: brew install graphviz"
        return 1
    fi
    
    go tool pprof -svg "$profile" > "$output_file" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Flame graph saved to: $output_file${NC}"
        echo "Open with: xdg-open $output_file (Linux) or open $output_file (macOS)"
    else
        echo -e "${RED}Failed to generate flame graph${NC}"
    fi
}

interactive_pprof() {
    local profile=$(select_profile "")
    [ $? -ne 0 ] && return
    
    echo -e "${GREEN}Starting interactive pprof session...${NC}"
    echo -e "${YELLOW}Useful commands:${NC}"
    echo "  top10     - Show top 10 functions"
    echo "  list func - Show source code for function"
    echo "  web       - Open web interface"
    echo "  png       - Generate PNG graph"
    echo "  help      - Show all commands"
    echo "  quit      - Exit"
    echo
    
    go tool pprof "$profile"
}

export_analysis() {
    local profile=$(select_profile "")
    [ $? -ne 0 ] && return
    
    local output_file="analysis_$(basename "$profile" .prof)_$(date +%Y%m%d_%H%M%S).txt"
    
    echo -e "${GREEN}Exporting analysis to: $output_file${NC}"
    
    {
        echo "Profile Analysis Report"
        echo "======================"
        echo "Profile: $(basename "$profile")"
        echo "Generated: $(date)"
        echo
        
        echo "Profile Statistics:"
        go tool pprof -text "$profile" | head -5
        echo
        
        echo "Top 20 Functions:"
        go tool pprof -text "$profile" | head -25 | tail -20
        echo
        
        echo "Algorithm Functions:"
        go tool pprof -text "$profile" | grep -E "(BuildTree|buildNode|findBestSplit|training|prediction)" || echo "None found"
        echo
        
        echo "Memory Functions (if memory profile):"
        if [[ "$profile" == *"mem"* ]]; then
            go tool pprof -alloc_space -text "$profile" | head -10
        fi
        
    } > "$output_file"
    
    echo -e "${GREEN}Analysis exported successfully${NC}"
}

show_statistics() {
    echo -e "${GREEN}Profile Statistics Summary${NC}"
    echo
    
    if [ ! -d "$PROFILES_DIR" ] || [ -z "$(ls -A "$PROFILES_DIR"/*.prof 2>/dev/null)" ]; then
        echo "No profiles found in $PROFILES_DIR"
        return
    fi
    
    echo -e "${YELLOW}Available Profiles:${NC}"
    for profile in "$PROFILES_DIR"/*.prof; do
        if [ -f "$profile" ]; then
            local size=$(stat -f%z "$profile" 2>/dev/null || stat -c%s "$profile" 2>/dev/null || echo "unknown")
            local date=$(stat -f%Sm "$profile" 2>/dev/null || stat -c%y "$profile" 2>/dev/null || echo "unknown")
            echo "$(basename "$profile"): ${size} bytes, ${date}"
            
            # Quick stats
            if [[ "$profile" == *"cpu"* ]]; then
                local duration=$(go tool pprof -text "$profile" 2>/dev/null | grep "Duration:" | awk '{print $2}' || echo "N/A")
                local samples=$(go tool pprof -text "$profile" 2>/dev/null | grep "Total samples" | awk '{print $4}' || echo "N/A")
                echo "  Duration: $duration, Samples: $samples"
            elif [[ "$profile" == *"mem"* ]]; then
                local total_mem=$(go tool pprof -text "$profile" 2>/dev/null | grep "Showing nodes accounting for" | awk '{print $5}' || echo "N/A")
                echo "  Total Memory: $total_mem"
            fi
            echo
        fi
    done
}

main() {
    if [ ! -d "$PROFILES_DIR" ]; then
        echo -e "${RED}Profiles directory not found: $PROFILES_DIR${NC}"
        echo "Run profiling first with: ./scripts/profile_example.sh"
        exit 1
    fi
    
    while true; do
        show_menu
        echo -n "Select option (0-9): "
        read choice
        
        case $choice in
            1) quick_cpu_analysis ;;
            2) quick_memory_analysis ;;
            3) detailed_cpu_analysis ;;
            4) detailed_memory_analysis ;;
            5) compare_profiles ;;
            6) generate_flame_graph ;;
            7) interactive_pprof ;;
            8) export_analysis ;;
            9) show_statistics ;;
            0) echo "Goodbye!"; exit 0 ;;
            *) echo -e "${RED}Invalid option${NC}" ;;
        esac
        
        echo
        echo -n "Press Enter to continue..."
        read
    done
}

main "$@"
