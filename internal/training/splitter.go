// Package training provides split evaluation functionality for decision trees.
//
// This module implements the Strategy pattern for different split evaluation
// algorithms, supporting both numerical (int/float) and categorical (string)
// features with unified interfaces.
//
// Design Principles:
// - Strategy pattern for different feature types
// - View-based operations for memory efficiency
// - Generic interfaces for different target types
// - Type-safe split candidate generation
package training

import (
	"fmt"
	"math"
	"sort"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// floatTolerance defines the minimum difference between consecutive float values
// to consider them different for split evaluation. This prevents unnecessary
// splits due to floating point precision issues.
const floatTolerance = 1e-9

// SplitType represents the type of split operation.
type SplitType int

const (
	// NumericalSplit uses threshold-based splitting for int/float features
	NumericalSplit SplitType = iota
	// CategoricalSplit uses multi-way splitting for string features
	// Binary categorical splits are treated as n-ary splits where n=2
	CategoricalSplit
)

// SplitCandidate represents a potential split for any feature type.
//
// Contains all information needed to evaluate and apply a split,
// including the split condition, quality metrics, and resulting subset sizes.
//
// Performance: Lightweight struct for efficient candidate comparison
// Relationships: Generated by SplitEvaluator, consumed by tree builder
// Side effects: None (immutable data structure)
//
// Example:
//
//	// Numerical split: "age <= 30.5"
//	candidate := SplitCandidate{
//	    FeatureName: "age",
//	    Type:        NumericalSplit,
//	    Threshold:   &threshold,
//	    Gain:        0.23,
//	    LeftSize:    15,
//	    RightSize:   10,
//	}
type SplitCandidate struct {
	FeatureName string    // Name of the feature being split
	Type        SplitType // Type of split (numerical or categorical)

	// For numerical splits (int/float): "feature <= threshold"
	Threshold *float64 // Split threshold value

	// For categorical splits (string): "feature == value"
	Value interface{} // Split value for categorical features

	Gain        float64 // Information gain from this split
	BranchSizes map[interface{}]int

	// For n-ary categorical splits: map of categorical values to sample indices
	// Key: categorical value (e.g., "red", "blue", "green")
	// Value: slice of sample indices that have this categorical value
	// Only populated for n-ary categorical splits, nil for binary splits
	ChildPartitions map[interface{}][]int
}

// SplitEvaluator defines the interface for evaluating splits on different feature types.
//
// Generic interface supporting different target types and feature types.
// Implementations handle the specifics of numerical vs categorical splitting.
//
// Args:
// - view: DatasetView containing the subset of data to split
// - column: FeatureColumn interface for accessing feature values
// - baseImpurity: Current impurity of the node before splitting
// - featureName: Name of the feature being evaluated
//
// Returns: Slice of split candidates ordered by quality (best first)
// Performance: O(n log n) for numerical features, O(n*k) for categorical
// Relationships: Used by tree builder to find best splits
// Side effects: None (read-only operations on view)
//
// Example:
//
//	evaluator := factory.CreateEvaluator(column)
//	candidates, err := evaluator.EvaluateSplits(view, column, 0.918, "age")
type SplitEvaluator[T comparable] interface {
	EvaluateSplits(view *dataset.DatasetView[T], column dataset.FeatureColumn, baseImpurity float64, featureName string) []SplitCandidate
}

// numericalPair represents a (value, target) pair for numerical split evaluation.
//
// Used internally by NumericalSplitEvaluator to sort feature values
// and efficiently evaluate all possible threshold splits.
//
// Performance: Lightweight struct for sorting operations
// Relationships: Internal to NumericalSplitEvaluator
type numericalPair[T comparable] struct {
	value  float64 // Feature value converted to float64
	target T       // Target value for this sample
}

// NumericalSplitEvaluator handles threshold-based splits for int/float features.
//
// Implements the standard decision tree splitting algorithm for numerical features:
// 1. Sort all feature values
// 2. Evaluate split at midpoint between each adjacent pair
// 3. Calculate information gain for each potential split
// 4. Return candidates sorted by gain
//
// Performance: O(n log n) due to sorting, where n is number of samples
// Relationships: Created by SplitEvaluatorFactory for numerical features
// Side effects: None (operates on view data without modification)
//
// Example:
//
//	evaluator := &NumericalSplitEvaluator[string]{impurityCalc: entropyCalc}
//	candidates, _ := evaluator.EvaluateSplits(view, ageColumn, 0.918)
type NumericalSplitEvaluator[T comparable] struct{}

// EvaluateSplits finds the best threshold-based splits for a numerical feature.
//
// Algorithm:
// 1. Extract and sort (value, target) pairs from the view
// 2. Initialize left/right target distributions
// 3. For each potential threshold between adjacent values:
//   - Update distributions by moving samples from right to left
//   - Calculate weighted impurity and information gain
//   - Create split candidate if gain is positive
//
// 4. Return all candidates (caller selects best)
//
// Args:
// - view: DatasetView containing active samples for this node
// - column: FeatureColumn for accessing numerical feature values
// - baseImpurity: Current node impurity before splitting
// - featureName: Name of the feature being evaluated
//
// Returns: Slice of split candidates, empty if no valid splits found
// Constraints: Feature must be numerical (int/float), view must not be empty
// Performance: O(n log n) for sorting + O(n) for evaluation
// Relationships: Core algorithm for numerical feature splitting
// Side effects: None (read-only operations)
//
// Example:
//
//	// For age feature with values [25, 30, 35, 40] and targets ["young", "old", "old", "old"]
//	// Might generate candidates for thresholds: 27.5, 32.5, 37.5
//	candidates, err := evaluator.EvaluateSplits(view, ageColumn, 1.0, "age")
func (n *NumericalSplitEvaluator[T]) EvaluateSplits(
	view *dataset.DatasetView[T],
	column dataset.FeatureColumn,
	baseImpurity float64,
	featureName string,
) []SplitCandidate {

	// Get sorted pairs from ONLY the active indices in the view
	sortedPairs := n.getSortedNumericalPairs(view, featureName)
	if sortedPairs == nil {
		logger.Warn(fmt.Sprintf("Failed to get sorted pairs for feature %s", featureName))
		return []SplitCandidate{}
	}

	if len(sortedPairs) < 2 {
		return []SplitCandidate{} // Need at least 2 samples to split
	}

	var candidates []SplitCandidate

	// Initialize target distributions for the VIEW's data only
	leftDist := make(map[T]int)
	rightDist := make(map[T]int)

	// All samples from VIEW start in right distribution
	for _, pair := range sortedPairs {
		rightDist[pair.target]++
	}
	// Get Parent distribution
	parentDist := view.GetTargetDistribution()
	parentSamples := view.GetSize()

	// Evaluate each potential split point
	for i := 0; i < len(sortedPairs)-1; i++ {
		// Move current sample from right to left
		target := sortedPairs[i].target
		leftDist[target]++
		rightDist[target]--
		if rightDist[target] == 0 {
			delete(rightDist, target)
		}

		// Create split candidate if values are different (with floating point tolerance)
		if math.Abs(sortedPairs[i].value-sortedPairs[i+1].value) > floatTolerance {
			threshold := (sortedPairs[i].value + sortedPairs[i+1].value) / 2

			leftSize := i + 1
			rightSize := len(sortedPairs) - leftSize
			// Build child distributions for gain ratio
			childDistributions := map[interface{}]map[T]int{
				"lte": leftDist,
				"gt":  rightDist,
			}

			gainRatio := CalculateGainRatio(parentDist, parentSamples, childDistributions)
			if gainRatio > 0 { // Only include splits that improve purity
				candidates = append(candidates, SplitCandidate{
					FeatureName: featureName,
					Type:        NumericalSplit,
					Threshold:   &threshold,
					Gain:        gainRatio,
					BranchSizes: map[interface{}]int{
						"lte": leftSize,
						"rt":  rightSize,
					},
				})
			}
		}
	}

	return candidates
}

// getSortedNumericalPairs extracts and sorts (value, target) pairs from the view.
//
// Args:
// - view: DatasetView containing active samples
// - column: FeatureColumn for accessing feature values
// - featureName: Name of the feature being accessed
//
// Returns: Sorted slice of numericalPair structs
// Performance: O(n log n) due to sorting
// Relationships: Helper method for EvaluateSplits
// Side effects: None (read-only operations)
func (n *NumericalSplitEvaluator[T]) getSortedNumericalPairs(
	view *dataset.DatasetView[T],
	featureName string,
) []numericalPair[T] {

	var pairs []numericalPair[T]

	// Extract (value, target) pairs from VIEW's active indices only
	for i := 0; i < view.GetSize(); i++ {
		value := view.GetFeatureValue(i, featureName)
		if value == nil {
			continue // Skip missing values
		}

		target := view.GetTarget(i)

		// Convert value to float64 for numerical comparison
		var floatValue float64
		switch v := value.(type) {
		case *int64:
			floatValue = float64(*v)
		case *float64:
			floatValue = *v
		default:
			continue // Skip non-numerical values
		}

		pairs = append(pairs, numericalPair[T]{
			value:  floatValue,
			target: target,
		})
	}

	// Sort by numerical value
	sort.Slice(pairs, func(i, j int) bool {
		return pairs[i].value < pairs[j].value
	})

	return pairs
}

// CategoricalSplitEvaluator handles multi-way splits for categorical features.
//
// Implements n-ary categorical splitting for improved tree interpretability:
// 1. Build joint distribution of (feature_value, target_value) from view
// 2. Create single n-ary split with one child per unique categorical value
// 3. Calculate gain ratio (information gain / split information) for the split
// 4. Return single candidate with multiple child partitions
//
// This approach creates balanced, interpretable trees by avoiding deep binary
// splits for high-cardinality categorical features. Instead of 50 nested nodes
// for US states, creates 1 node with 50 branches.
//
// Performance: O(n*k) where n is samples and k is unique feature values
// Relationships: Alternative to CategoricalSplitEvaluator for n-ary splits
// Side effects: None (operates on view data without modification)
//
// Example:
//
//	evaluator := &CategoricalSplitEvaluator[string]{}
//	candidates := evaluator.EvaluateSplits(view, educationColumn, 1.0, "education")
type CategoricalSplitEvaluator[T comparable] struct{}

// EvaluateSplits finds the best n-ary split for a categorical feature using gain ratio.
//
// Algorithm:
// 1. Build joint distribution of (feature_value, target_value) from view
// 2. Create child partitions: one partition per unique categorical value
// 3. Calculate gain ratio for the n-ary split using CalculateGainRatio
// 4. Return single split candidate if gain ratio is positive
//
// Args:
// - view: DatasetView containing active samples for this node
// - column: FeatureColumn for accessing categorical feature values
// - baseImpurity: Current node impurity before splitting (unused for gain ratio)
// - featureName: Name of the feature being evaluated
//
// Returns: Slice with single split candidate or empty if no valid split
// Constraints: Feature must be categorical (string), view must not be empty
// Performance: O(n*k) where n is samples and k is unique feature values
// Relationships: Uses CalculateGainRatio for split quality assessment
// Side effects: None (read-only operations)
//
// Example:
//
//	// For education feature with values ["college", "high_school", "graduate"]
//	// Creates single candidate with 3 child partitions instead of 3 binary splits
//	candidates := evaluator.EvaluateSplits(view, educationColumn, 1.0, "education")
func (n *CategoricalSplitEvaluator[T]) EvaluateSplits(
	view *dataset.DatasetView[T],
	column dataset.FeatureColumn,
	baseImpurity float64,
	featureName string,
) []SplitCandidate {
	// Build joint distribution: feature_value -> target_value -> count
	jointDist := make(map[interface{}]map[T]int)
	childPartitions := make(map[interface{}][]int)

	// Get active indices for physical index mapping
	activeIndices := view.GetActiveIndices()

	// Process each sample in the view
	for i := 0; i < view.GetSize(); i++ {
		value := view.GetFeatureValue(i, featureName)
		if value == nil {
			continue // Skip missing values
		}

		// Dereference the pointer to get the actual categorical value
		var actualValue interface{}
		switch ptr := value.(type) {
		case *string:
			actualValue = *ptr
		case *int64:
			actualValue = *ptr
		case *float64:
			actualValue = *ptr
		default:
			// If it's not a pointer, use the value directly
			actualValue = value
		}

		target := view.GetTarget(i)
		physicalIndex := activeIndices[i]

		// Initialize maps if needed
		if jointDist[actualValue] == nil {
			jointDist[actualValue] = make(map[T]int)
			childPartitions[actualValue] = make([]int, 0)
		}

		// Update distributions and partitions
		jointDist[actualValue][target]++
		childPartitions[actualValue] = append(childPartitions[actualValue], physicalIndex)
	}

	// Need at least 2 unique values to create a meaningful split
	if len(jointDist) < 2 {
		return []SplitCandidate{}
	}

	// Calculate parent distribution for gain ratio
	parentDist := view.GetTargetDistribution()
	parentSamples := view.GetSize()

	// Calculate gain ratio for the n-ary split
	gainRatio := CalculateGainRatio(parentDist, parentSamples, jointDist)

	// Only return split if it provides positive gain ratio
	if gainRatio > 0 {
		// Calculate total child sizes for metadata
		branchSizes := make(map[interface{}]int)
		for value, indices := range childPartitions {
			branchSizes[value] = len(indices)
		}

		return []SplitCandidate{{
			FeatureName:     featureName,
			Type:            CategoricalSplit,
			Value:           nil, // N-ary splits don't have single split value
			Gain:            gainRatio,
			BranchSizes:     branchSizes,
			ChildPartitions: childPartitions,
		}}
	}

	return []SplitCandidate{}
}

// SplitEvaluatorFactory creates appropriate split evaluators based on feature type.
//
// Implements the Factory pattern to abstract the creation of different
// split evaluator types based on feature characteristics. Always uses n-ary
// evaluators for categorical features (binary splits are a special case where n=2).
//
// Performance: O(1) factory method
// Relationships: Used by tree builder to create evaluators for each feature
// Side effects: Allocates new evaluator instances
//
// Example:
//
//	factory := &SplitEvaluatorFactory[string]{}
//	evaluator := factory.CreateEvaluator(column.GetType())
type SplitEvaluatorFactory[T comparable] struct {
	// No configuration needed - always use optimal evaluator for each feature type
}

// CreateEvaluator creates the appropriate split evaluator for the given feature type.
//
// Args:
// - featureType: Type of the feature (IntegerFeature, FloatFeature, StringFeature)
//
// Returns: SplitEvaluator implementation or nil for unsupported types
// Constraints: Only supports IntegerFeature, FloatFeature, and StringFeature
// Performance: O(1) switch statement
// Relationships: Maps feature types to evaluator implementations
// Side effects: Allocates new evaluator instance
//
// Example:
//
//	factory := &SplitEvaluatorFactory[string]{}
//	evaluator := factory.CreateEvaluator(features.StringFeature)  // Returns CategoricalSplitEvaluator
func (f *SplitEvaluatorFactory[T]) CreateEvaluator(featureType features.FeatureType) SplitEvaluator[T] {
	switch featureType {
	case features.IntegerFeature, features.FloatFeature:
		// BOTH int and float use numerical splitting
		return &NumericalSplitEvaluator[T]{}
	case features.StringFeature:
		// Always use n-ary categorical evaluator (binary is special case where n=2)
		return &CategoricalSplitEvaluator[T]{}
	default:
		logger.Error(fmt.Sprintf("unsupported feature type %v requested in CreateEvaluator - this indicates a missing implementation for a new feature type", featureType))
		return nil // Unsupported feature type
	}
}

// NewSplitEvaluatorFactory creates a new factory for split evaluators.
//
// Returns: New factory instance ready to create evaluators
// Performance: O(1) factory creation
// Relationships: Used by training configuration to create factory
// Side effects: Allocates factory structure
//
// Example:
//
//	factory := NewSplitEvaluatorFactory[string]() // Always uses optimal evaluators
func NewSplitEvaluatorFactory[T comparable]() *SplitEvaluatorFactory[T] {
	return &SplitEvaluatorFactory[T]{}
}

// ====================
// Split Application Methods
// ====================

// ApplySplit applies this split candidate to a dataset view, creating child views.
//
// Unified method that handles both binary and n-ary splits by delegating to
// SplitDatasetViewByBranch. Returns a map of branch values to child views.
//
// Args:
// - view: DatasetView to split (must contain the feature referenced by this candidate)
//
// Returns: Map of branch values to child DatasetViews, or error if split cannot be applied.
// Constraints: view must contain feature specified in FeatureName, split parameters must be valid.
// Performance: O(n) where n = number of active indices in view.
// Relationships: Uses SplitDatasetViewByBranch for unified split application.
// Side effects: Creates new child DatasetViews, logs split statistics.
//
// Split Logic:
// - Numerical: Creates 2 branches ("left" for ≤ threshold, "right" for > threshold)
// - Categorical Binary: Creates 2 branches ("left" for = value, "right" for ≠ value)
// - Categorical N-ary: Creates n branches (one per unique categorical value)
//
// Example:
//
//	candidate := SplitCandidate{FeatureName: "age", Type: NumericalSplit, Threshold: &threshold}
//	childViews, err := candidate.ApplySplit(parentView)
//	for branchValue, childView := range childViews {
//	    fmt.Printf("Branch %v: %d samples", branchValue, childView.GetSize())
//	}
func (sc *SplitCandidate) ApplySplit(view *dataset.DatasetView[string]) (map[interface{}]*dataset.DatasetView[string], error) {
	// Validate split candidate
	switch sc.Type {
	case NumericalSplit:
		if sc.Threshold == nil {
			return nil, fmt.Errorf("numerical split requires threshold value")
		}
		// categorical splits treated as n-ary splits
	// Binary categorical splits are now handled as n-ary splits with n=2
	case CategoricalSplit:
		if sc.ChildPartitions == nil {
			return nil, fmt.Errorf("n-ary categorical split requires child partitions")
		}
	default:
		return nil, fmt.Errorf("unknown split type: %v", sc.Type)
	}

	// n-ary and binary splits handled in one function
	// binary splits are special case of n-ary splits with 2 child views
	childViews := SplitDatasetViewByBranch(view, view.GetDataset(), sc)
	if childViews == nil {
		return nil, fmt.Errorf("failed to apply split: no child views created")
	}

	return childViews, nil
}

// ====================
// Best Split Finding Methods
// ====================

// findBestSplit evaluates all features to find the split with highest information gain.
//
// Implements the core C4.5 split selection algorithm by evaluating every available
// feature and selecting the split that provides the maximum information gain.
// Uses the Strategy pattern with SplitEvaluator implementations for different feature types.
//
// Args:
// - view: DatasetView containing samples to split
// - featureTypes: Map of feature names to their types (determines evaluator selection)
// - splitFactory: Factory for creating split evaluators
//
// Returns: Best SplitCandidate found, or nil if no beneficial splits exist.
// Constraints: view must be non-empty, featureTypes must contain valid features.
// Performance: O(n log n * f) where n=samples, f=features (dominated by split evaluation).
// Relationships: Uses SplitEvaluatorFactory to create appropriate evaluators per feature.
// Side effects: Logs split evaluation progress and results.
//
// Algorithm:
// 1. Calculate base impurity for current node
// 2. For each feature: create appropriate evaluator and evaluate splits
// 3. Collect all split candidates from all features
// 4. Select candidate with highest information gain
// 5. Return best candidate (or nil if no beneficial splits)
//
// Example: Internal method used by buildNode during tree construction.
func findBestSplit(
	view *dataset.DatasetView[string],
	featureTypes map[string]features.FeatureType,
	splitFactory *SplitEvaluatorFactory[string],
) (*SplitCandidate, error) {
	if view.GetSize() == 0 {
		return nil, fmt.Errorf("cannot find split for empty view")
	}

	// Calculate base impurity for information gain calculation
	targetDist := view.GetTargetDistribution()
	baseImpurity := CalculateEntropy(targetDist, view.GetSize())

	logger.Debug(fmt.Sprintf("Finding best split for %d samples with base impurity %.6f",
		view.GetSize(), baseImpurity))

	var bestCandidate *SplitCandidate

	// Evaluate splits for each feature
	for featureName, featureType := range featureTypes {
		// Create appropriate evaluator for this feature type
		evaluator := splitFactory.CreateEvaluator(featureType)
		if evaluator == nil {
			logger.Error(fmt.Sprintf("No evaluator available for feature type %v", featureType))
			continue
		}

		// Get feature column from dataset
		column := view.GetDataset().GetColumn(featureName)
		if column == nil {
			logger.Error(fmt.Sprintf("Feature column %s not found in dataset", featureName))
			continue
		}

		// Evaluate all possible splits for this feature
		candidates := evaluator.EvaluateSplits(view, column, baseImpurity, featureName)
		if len(candidates) == 0 {
			logger.Debug(fmt.Sprintf("No valid splits found for feature %s", featureName))
			continue
		}

		logger.Debug(fmt.Sprintf("Feature %s: found %d split candidates, best gain: %.6f",
			featureName, len(candidates), candidates[0].Gain))

		// Update best candidate if this feature has better splits
		for i := range candidates {
			if bestCandidate == nil || candidates[i].Gain > bestCandidate.Gain {
				bestCandidate = &candidates[i]
			}
		}
	}

	if bestCandidate == nil {
		logger.Debug("No beneficial splits found across any features")
		return nil, nil
	}

	logger.Debug(fmt.Sprintf("Best split selected: %s (gain: %.6f)",
		formatSplit(bestCandidate), bestCandidate.Gain))

	return bestCandidate, nil
}

// formatSplit creates a human-readable description of a split candidate.
//
// Args:
// - split: SplitCandidate to format
//
// Returns: String description of the split condition.
// Performance: O(1) string formatting.
// Relationships: Helper for logging split information.
// Side effects: None (pure function).
//
// Example:
//
//	"age <= 30.5" for numerical split
//	"education == 'college'" for categorical split
func formatSplit(split *SplitCandidate) string {
	if split == nil {
		return "nil split"
	}

	switch split.Type {
	case NumericalSplit:
		if split.Threshold != nil {
			return fmt.Sprintf("%s <= %.3f", split.FeatureName, *split.Threshold)
		}
		return fmt.Sprintf("%s <= <nil>", split.FeatureName)

	case CategoricalSplit:
		return fmt.Sprintf("%s categorical split", split.FeatureName)

	default:
		return fmt.Sprintf("%s <unknown split type>", split.FeatureName)
	}
}

// getGainSafe safely extracts gain from split candidate, handling nil case.
//
// Args:
// - split: SplitCandidate to extract gain from (may be nil)
//
// Returns: Gain value or 0.0 if split is nil.
// Performance: O(1) safe access.
// Relationships: Helper for safe gain comparison.
// Side effects: None (pure function).
func getGainSafe(split *SplitCandidate) float64 {
	if split == nil {
		return 0.0
	}
	return split.Gain
}
