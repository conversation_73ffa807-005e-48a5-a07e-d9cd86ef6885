// Package batch provides utilities for batch processing operations to eliminate code duplication.
//
// This package consolidates common batch processing patterns found in the prediction service:
// - Batch index creation for dataset views
// - Worker pool management patterns
// - Panic recovery in goroutines
// - Result collection and synchronization
//
// Design Principles:
// - DRY compliance: Single implementation for common batch patterns
// - Type-safe batch operations with generics
// - Consistent error handling across batch operations
// - Efficient memory usage with object pooling
//
// Usage Examples:
//   indices := batch.CreateBatchIndices(start, end)
//   batch.ExecuteWithWorkerPool(ctx, workerPool, func() { ... })
//   batch.WithPanicRecovery(resultChan, batchIndex, func() { ... })
package batch

import (
	"context"
	"fmt"

	"github.com/berrijam/mulberri/internal/data/dataset"
)

// CreateBatchIndices creates a slice of indices for batch processing.
//
// Args:
//   - start: Starting index (inclusive)
//   - end: Ending index (exclusive)
//
// Returns:
//   - []int: Slice of indices from start to end-1
//
// Performance: O(n) where n = end - start
// Side effects: None
func CreateBatchIndices(start, end int) []int {
	if start >= end {
		return []int{}
	}
	
	batchIndices := make([]int, end-start)
	for i := 0; i < end-start; i++ {
		batchIndices[i] = start + i
	}
	return batchIndices
}

// CreateDatasetView creates a dataset view for the given batch indices.
//
// Args:
//   - dataset: Source dataset to create view from
//   - start: Starting index (inclusive)
//   - end: Ending index (exclusive)
//
// Returns:
//   - *dataset.DatasetView: View containing the specified range
//
// Performance: O(n) where n = end - start for index creation
// Side effects: None
func CreateDatasetView[T comparable](dataset *dataset.Dataset[T], start, end int) *dataset.DatasetView[T] {
	batchIndices := CreateBatchIndices(start, end)
	return dataset.CreateView(batchIndices)
}

// WorkerPool represents a semaphore-based worker pool for limiting concurrency.
type WorkerPool interface {
	// acquire attempts to acquire a worker slot with context cancellation
	acquire(ctx context.Context) error
	// release releases a worker slot back to the pool
	release()
}

// ExecuteWithWorkerPool executes a function with worker pool management.
//
// Args:
//   - ctx: Context for cancellation
//   - workerPool: Worker pool for concurrency control
//   - fn: Function to execute with worker pool protection
//
// Returns:
//   - error: Error from worker pool acquisition or function execution
//
// Performance: O(1) + function execution time
// Side effects: Acquires and releases worker pool slot
func ExecuteWithWorkerPool(ctx context.Context, workerPool WorkerPool, fn func() error) error {
	// Acquire worker slot
	if err := workerPool.acquire(ctx); err != nil {
		return err
	}
	defer workerPool.release()
	
	// Execute function
	return fn()
}

// BatchResult represents a generic result from batch processing.
type BatchResult[T any] struct {
	StartIdx int
	Data     []T
	Err      error
}

// PanicRecoveryHandler handles panic recovery in batch processing goroutines.
type PanicRecoveryHandler[T any] struct {
	ResultChan chan BatchResult[T]
	BatchIndex int
	StartIdx   int
}

// WithPanicRecovery wraps a function with panic recovery for batch processing.
//
// Args:
//   - handler: Panic recovery handler configuration
//   - fn: Function to execute with panic protection
//
// Returns: None (sends result to channel)
// Performance: O(1) + function execution time
// Side effects: May send error result to channel on panic
func WithPanicRecovery[T any](handler PanicRecoveryHandler[T], fn func()) {
	defer func() {
		if r := recover(); r != nil {
			handler.ResultChan <- BatchResult[T]{
				StartIdx: handler.StartIdx,
				Err:      fmt.Errorf("batch %d panic: %v", handler.BatchIndex, r),
			}
		}
	}()
	
	fn()
}

// BatchProcessor provides a generic batch processing framework.
type BatchProcessor[T any] struct {
	BatchSize   int
	MaxWorkers  int
	WorkerPool  WorkerPool
}

// ProcessBatch processes a batch of items using the provided processor function.
//
// Args:
//   - ctx: Context for cancellation
//   - items: Items to process
//   - processor: Function to process each batch
//
// Returns:
//   - []T: Processed results
//   - error: Error if processing fails
//
// Performance: O(n/batchSize * processingTime) where n is number of items
// Side effects: May create goroutines and use worker pool
func (bp *BatchProcessor[T]) ProcessBatch(
	ctx context.Context, 
	totalItems int,
	processor func(ctx context.Context, start, end int) ([]T, error),
) ([]T, error) {
	if totalItems == 0 {
		return []T{}, nil
	}
	
	// Calculate number of batches
	numBatches := (totalItems + bp.BatchSize - 1) / bp.BatchSize
	resultChan := make(chan BatchResult[T], numBatches)
	
	// Process batches
	for i := 0; i < numBatches; i++ {
		start := i * bp.BatchSize
		end := start + bp.BatchSize
		if end > totalItems {
			end = totalItems
		}
		
		go func(batchStart, batchEnd, batchIdx int) {
			handler := PanicRecoveryHandler[T]{
				ResultChan: resultChan,
				BatchIndex: batchIdx,
				StartIdx:   batchStart,
			}
			
			WithPanicRecovery(handler, func() {
				err := ExecuteWithWorkerPool(ctx, bp.WorkerPool, func() error {
					results, err := processor(ctx, batchStart, batchEnd)
					if err != nil {
						resultChan <- BatchResult[T]{
							StartIdx: batchStart,
							Err:      err,
						}
						return err
					}
					
					resultChan <- BatchResult[T]{
						StartIdx: batchStart,
						Data:     results,
						Err:      nil,
					}
					return nil
				})
				
				if err != nil {
					resultChan <- BatchResult[T]{
						StartIdx: batchStart,
						Err:      err,
					}
				}
			})
		}(start, end, i)
	}
	
	// Collect results
	results := make([]T, totalItems)
	for i := 0; i < numBatches; i++ {
		result := <-resultChan
		if result.Err != nil {
			return nil, result.Err
		}
		
		// Copy results to final slice
		copy(results[result.StartIdx:], result.Data)
	}
	
	return results, nil
}
