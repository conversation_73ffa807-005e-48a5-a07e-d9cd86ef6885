row_index,prediction,confidence,decision_rule,decision_path,input_features,class_distribution,error
0,no,1.0000,IF income <= 54000.000000 AND education != college THEN no,income <= 54000.000000 → education != college → LEAF[no] (confidence: 1.000),"{age:27, income:32000, education:high_school}",{no:9},
1,yes,1.0000,IF income > 54000.000000 THEN yes,income > 54000.000000 → LEAF[yes] (confidence: 1.000),"{age:39, income:65000, education:graduate}",{yes:11},
2,no,1.0000,IF income <= 54000.000000 AND education = college AND age <= 32.000000 AND income <= 49000.000000 AND income > 41500.000000 THEN no,income <= 54000.000000 → education = college → age <= 32.000000 → income <= 49000.000000 → income > 41500.000000 → LEAF[no] (confidence: 1.000),"{age:31, income:47000, education:college}",{no:3},
3,no,1.0000,IF income <= 54000.000000 AND education != college THEN no,income <= 54000.000000 → education != college → LEAF[no] (confidence: 1.000),"{income:22000, education:high_school, age:23}",{no:9},
4,yes,1.0000,IF income > 54000.000000 THEN yes,income > 54000.000000 → LEAF[yes] (confidence: 1.000),"{age:42, income:75000, education:graduate}",{yes:11},
5,yes,1.0000,IF income <= 54000.000000 AND education = college AND age <= 32.000000 AND income <= 49000.000000 AND income <= 41500.000000 AND age > 27.000000 THEN yes,income <= 54000.000000 → education = college → age <= 32.000000 → income <= 49000.000000 → income <= 41500.000000 → age > 27.000000 → LEAF[yes] (confidence: 1.000),"{age:29, income:38000, education:college}",{yes:1},
6,yes,1.0000,IF income > 54000.000000 THEN yes,income > 54000.000000 → LEAF[yes] (confidence: 1.000),"{age:35, income:55000, education:college}",{yes:11},
7,no,1.0000,IF income <= 54000.000000 AND education != college THEN no,income <= 54000.000000 → education != college → LEAF[no] (confidence: 1.000),"{education:high_school, age:26, income:29000}",{no:9},
8,yes,1.0000,IF income > 54000.000000 THEN yes,income > 54000.000000 → LEAF[yes] (confidence: 1.000),"{education:graduate, age:46, income:82000}",{yes:11},
9,yes,1.0000,IF income <= 54000.000000 AND education = college AND age > 32.000000 THEN yes,income <= 54000.000000 → education = college → age > 32.000000 → LEAF[yes] (confidence: 1.000),"{education:college, age:33, income:51000}",{yes:3},
10,yes,1.0000,IF income <= 54000.000000 AND education = college AND age <= 32.000000 AND income <= 49000.000000 AND income <= 41500.000000 AND age > 27.000000 THEN yes,income <= 54000.000000 → education = college → age <= 32.000000 → income <= 49000.000000 → income <= 41500.000000 → age > 27.000000 → LEAF[yes] (confidence: 1.000),"{income:41000, education:college, age:28}",{yes:1},
11,yes,1.0000,IF income > 54000.000000 THEN yes,income > 54000.000000 → LEAF[yes] (confidence: 1.000),"{education:graduate, age:37, income:58000}",{yes:11},
12,no,1.0000,IF income <= 54000.000000 AND education != college THEN no,income <= 54000.000000 → education != college → LEAF[no] (confidence: 1.000),"{age:24, income:26000, education:high_school}",{no:9},
13,yes,1.0000,IF income > 54000.000000 THEN yes,income > 54000.000000 → LEAF[yes] (confidence: 1.000),"{age:41, income:68000, education:graduate}",{yes:11},
14,no,1.0000,IF income <= 54000.000000 AND education = college AND age <= 32.000000 AND income <= 49000.000000 AND income > 41500.000000 THEN no,income <= 54000.000000 → education = college → age <= 32.000000 → income <= 49000.000000 → income > 41500.000000 → LEAF[no] (confidence: 1.000),"{income:44000, education:college, age:30}",{no:3},
