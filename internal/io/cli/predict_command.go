// Package cli provides command-line interface functionality for the Mulberri decision tree system.
//
// This package implements the predict subcommand which handles making predictions on new data
// using previously trained C4.5 decision tree models. The prediction workflow includes:
//   - Loading trained models from serialized files
//   - Processing input CSV data and validating against model requirements
//   - Creating prediction datasets with proper feature type conversion
//   - Applying the decision tree to generate predictions
//   - Saving results to output files
//
// The predict command ensures that only model-required features are used for prediction,
// automatically excluding target columns and handling missing values appropriately.
package cli

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/io/formats/csv"
	"github.com/berrijam/mulberri/internal/io/persistence"
	"github.com/berrijam/mulberri/internal/prediction"
	"github.com/berrijam/mulberri/internal/tree"
	"github.com/berrijam/mulberri/internal/utils/errors"
	"github.com/berrijam/mulberri/internal/utils/logger"
	"github.com/berrijam/mulberri/internal/utils/profiling"
	"github.com/spf13/cobra"
)

// NewPredictCommand creates and configures the predict subcommand for the Mulberri CLI.
//
// This function sets up the complete prediction command with all required flags, validation,
// and execution logic. The predict command loads a trained C4.5 decision tree model and
// applies it to new CSV data to generate predictions.
//
// Command Structure:
//   - Required flags: --input (CSV file), --model (trained model), --output (results file)
//   - Optional flags: --verbose (detailed logging)
//   - Validation: Ensures all required flags are provided before execution
//   - Execution: Delegates to runPrediction() with validated configuration
//
// Usage Examples:
//
//	mulberri predict -i test_data.csv -m model.dt -o predictions.csv
//	mulberri predict -i data.csv -m model.dt -o results.csv --verbose
//
// Returns:
//
//	*cobra.Command: Fully configured predict command ready for CLI registration
//
// Dependencies:
//   - PredictionConfig: Configuration struct for prediction parameters
//   - runPrediction(): Core prediction execution function
func NewPredictCommand() *cobra.Command {
	// Initialize prediction configuration container using the consolidated CLI config
	cfg := NewPredictionConfig()

	// Create the predict command with comprehensive metadata and execution logic
	predictCmd := &cobra.Command{
		Use:   "predict",
		Short: "Make predictions using trained model",
		Long: `Make predictions on new data using a previously trained decision tree model.

The prediction process loads a trained model and applies it to new CSV data,
outputting predictions in a structured format with optional confidence scores.`,
		Example: `  # Basic prediction
  mulberri predict -i test_data.csv -m model.dt -o predictions.csv

  # Verbose prediction with detailed output
  mulberri predict -i data.csv -m model.dt -o results.csv --verbose`,
		RunE: func(cmd *cobra.Command, args []string) error {
			// Set global subcommand context for logging and error handling
			CurrentSubcommand = cmd.Name()

			// Validate configuration before proceeding with prediction
			// CLI validation uses log.Fatal for immediate feedback
			cfg.Validate()

			// Execute the core prediction workflow
			return runPrediction(cfg)
		},
	}

	// Configure required command-line flags for prediction workflow
	// These flags are mandatory and must be provided by the user
	predictCmd.Flags().StringVarP(&cfg.InputFile, "input", "i", "", "Input CSV file path containing data for prediction")
	predictCmd.Flags().StringVarP(&cfg.ModelFile, "model", "m", "", "Trained decision tree model file path (.dt format)")
	predictCmd.Flags().StringVarP(&cfg.OutputFile, "output", "o", "", "Output file path for saving prediction results (.csv format)")

	// Configure optional command-line flags for enhanced functionality
	predictCmd.Flags().BoolVarP(&cfg.Verbose, "verbose", "v", false, "Enable verbose logging output for debugging")
	predictCmd.Flags().BoolVar(&cfg.DetailedOutput, "detailed", false, "Enable detailed prediction output with decision paths and reasoning")

	// Profiling flags
	predictCmd.Flags().BoolVar(&cfg.EnableCPUProfile, "cpu-profile", false, "Enable CPU profiling")
	predictCmd.Flags().BoolVar(&cfg.EnableMemProfile, "mem-profile", false, "Enable memory profiling")
	predictCmd.Flags().StringVar(&cfg.ProfileOutputDir, "profile-dir", "./profiles", "Directory for profile output files")

	// Mark essential flags as required to ensure proper command execution
	// The command will fail validation if any of these flags are missing
	predictCmd.MarkFlagRequired("input")
	predictCmd.MarkFlagRequired("model")
	predictCmd.MarkFlagRequired("output")

	return predictCmd
}

// runPrediction executes the complete prediction workflow using a trained decision tree model.
//
// This function orchestrates the entire prediction process from model loading to result output.
// It handles all aspects of the prediction pipeline including data validation, feature processing,
// model application, and error handling with comprehensive logging throughout.
//
// The function now uses the new prediction service architecture to separate concerns between
// CLI interface and business logic, following the requirements specification.
//
// Workflow Steps:
//  1. Initialize logging system with user-specified verbosity level
//  2. Load and validate the trained decision tree model from file
//  3. Extract feature metadata and target information from the model
//  4. Load and validate input CSV data for prediction
//  5. Create prediction dataset using only model-required features
//  6. Use prediction service to generate predictions
//  7. Use batch processor to save prediction results to output file
//  8. Provide comprehensive success summary with processing statistics
//
// Parameters:
//
//	cfg *PredictionConfig: Validated configuration containing:
//	  - InputFile: Path to CSV file with data for prediction
//	  - ModelFile: Path to trained decision tree model file
//	  - OutputFile: Path where prediction results will be saved
//	  - Verbose: Flag to enable detailed logging output
//
// Returns:
//   - error: Error if any step of the prediction workflow fails
//
// Error Handling:
//   - Returns errors instead of fatal termination for better error handling
//   - All errors are logged to both console and log files for debugging
//   - Uses service layer for validation and processing
//
// Side Effects:
//   - Creates log files in the "logs" directory
//   - Reads the specified model and input CSV files
//   - Creates the output file with prediction results
//
// Dependencies:
//   - logger: For comprehensive logging throughout the process
//   - persistence: For loading serialized decision tree models
//   - dataset: For CSV data loading and processing
//   - prediction: For prediction service and batch processing
func runPrediction(cfg *PredictionConfig) error {
	// Configure comprehensive logging system for the prediction workflow
	// This setup ensures all prediction activities are properly logged for debugging and monitoring
	logConfig := logger.LogConfig{
		LogFolder:     "logs",      // Directory for log file storage
		MaxSize:       10,          // Maximum log file size in MB
		EnableConsole: true,        // Enable console output for real-time feedback
		AppName:       "mulberri",  // Application name for log identification
		EnableColors:  true,        // Enable colored console output for better readability
		MaxBackups:    7,           // Number of old log files to retain
		MaxAge:        7,           // Maximum age of log files in days
		Verbose:       cfg.Verbose, // User-specified verbosity level
		Operation:     "predict",   // Operation type for log categorization
	}

	// Initialize the global logger with the configured settings
	// This must succeed before any other operations can proceed
	if err := logger.SetGlobalConfig(logConfig); err != nil {
		return fmt.Errorf("failed to initialize logger: %w", err)
	}

	// Log the initiation of the prediction workflow with input parameters
	logger.Info("Starting C4.5 decision tree prediction")
	logger.Info(fmt.Sprintf("Input: %s, Model: %s, Output: %s",
		cfg.InputFile, cfg.ModelFile, cfg.OutputFile))

	// Initialize profiler if profiling is enabled
	var profiler *profiling.Profiler
	if cfg.EnableCPUProfile || cfg.EnableMemProfile {
		profiler = profiling.NewProfiler(cfg.ProfileOutputDir)
		logger.Info(fmt.Sprintf("Profiling enabled, output directory: %s", cfg.ProfileOutputDir))

		// Start CPU profiling if enabled
		if cfg.EnableCPUProfile {
			cpuProfileFile := profiling.GenerateTimestampedFilename("predict", "cpu")
			if err := profiler.StartCPUProfile(cpuProfileFile); err != nil {
				logger.Error(fmt.Sprintf("Failed to start CPU profiling: %v", err))
			} else {
				defer profiler.StopCPUProfile()
			}
		}
	}

	// Step 1: Load and deserialize the trained decision tree model from file
	// This step validates the model file format and reconstructs the tree structure
	logger.Info("Loading trained model from file")

	serializer := persistence.NewTreeSerializer()
	decisionTree, err := serializer.LoadTreeFromFile(cfg.ModelFile)
	if err != nil {
		return errors.LogErrorAndReturn("Failed to load model", err)
	}

	// Log successful model loading with key statistics for verification
	logger.Info(fmt.Sprintf("Model loaded successfully: %d nodes (%d leaves), algorithm: %s",
		decisionTree.Metadata.TotalNodes, decisionTree.Metadata.LeafNodes, decisionTree.Metadata.Algorithm))

	// Step 2: Extract and validate feature metadata and target information from the loaded model
	// This information is crucial for validating input data and ensuring prediction compatibility
	logger.Info("Extracting feature metadata from loaded model")

	// Log the features expected by the model for validation against input data
	logger.Info(fmt.Sprintf("Model expects %d features: %v", len(decisionTree.Features), decisionTree.GetFeatureNames()))
	logger.Info(fmt.Sprintf("Target classes: %v", decisionTree.Classes))

	// Provide detailed tree structure information when verbose mode is enabled
	logger.Debug("Tree Summary:")
	logger.Debug(decisionTree.GetTreeSummary())

	// Step 3: Create feature metadata map from the decision tree's features
	featureMetadata := createFeatureMetadataMap(decisionTree.Features)

	// Step 4: Create prediction dataset using only model-required features
	// This step automatically excludes target columns and validates feature compatibility
	logger.Info("Creating prediction dataset using model features")
	predictionDataset := dataset.LoadCSVToDataset[string](cfg.InputFile, featureMetadata, decisionTree.Metadata.TargetColumn, "predict")
	if predictionDataset == nil {
		logger.Error("Failed to create prediction dataset")
		return fmt.Errorf("failed to create prediction dataset")
	}

	// Log successful dataset creation with feature statistics
	logger.Info(fmt.Sprintf("Created prediction dataset: %d features", len(predictionDataset.GetFeatureOrder())))

	// Step 5: Initialize prediction service and generate predictions
	logger.Info("Initializing prediction service")
	predictionService := prediction.NewPredictionService()

	// Generate predictions directly without complex request pattern
	logger.Info("Applying decision tree model to generate predictions")

	if cfg.DetailedOutput {
		// Generate detailed predictions with decision paths
		detailedPredictions, err := predictionService.PredictDetailed(decisionTree, predictionDataset, cfg.BatchSize)
		if err != nil {
			return errors.LogErrorAndReturn("Failed to generate detailed predictions", err)
		}

		logger.Info(fmt.Sprintf("Generated %d detailed predictions", len(detailedPredictions)))

		// Write detailed predictions to CSV
		err = csv.WriteDetailedCSV(detailedPredictions, cfg.OutputFile, cfg.IncludeHeaders)
		if err != nil {
			return errors.LogErrorAndReturn("Failed to write detailed predictions", err)
		}
	} else {
		// Generate simple predictions
		predictions, err := predictionService.PredictSimple(decisionTree, predictionDataset, cfg.BatchSize)
		if err != nil {
			return errors.LogErrorAndReturn("Failed to generate predictions", err)
		}

		logger.Info(fmt.Sprintf("Generated %d predictions", len(predictions)))

		// Write simple predictions to CSV
		err = csv.WriteSimpleCSV(predictions, cfg.OutputFile, cfg.IncludeHeaders)
		if err != nil {
			return errors.LogErrorAndReturn("Failed to write predictions", err)
		}
	}

	// Write memory profile if enabled
	if cfg.EnableMemProfile && profiler != nil {
		memProfileFile := profiling.GenerateTimestampedFilename("predict", "mem")
		if err := profiler.WriteMemProfile(memProfileFile); err != nil {
			logger.Error(fmt.Sprintf("Failed to write memory profile: %v", err))
		}
	}

	// Step 6: Provide comprehensive success summary
	logger.Info("Prediction completed successfully!")
	logger.Info(fmt.Sprintf("- Model loaded from: %s (%d nodes, algorithm: %s)",
		cfg.ModelFile, decisionTree.Metadata.TotalNodes, decisionTree.Metadata.Algorithm))
	logger.Info(fmt.Sprintf("- Results saved to: %s", cfg.OutputFile))

	return nil
}

// createFeatureMetadataMap creates a map of feature names to their types from tree features.
//
// This helper function extracts feature type information from a decision tree's feature
// definitions and converts it to the format expected by the dataset loading functions.
//
// Args:
//   - treeFeatures: Slice of Feature pointers from a decision tree
//
// Returns:
//   - map[string]features.FeatureType: Map of feature names to their types
//
// Performance: O(n) where n is the number of features
// Side effects: None, pure function
func createFeatureMetadataMap(treeFeatures []*tree.Feature) map[string]features.FeatureType {
	featureMetadata := make(map[string]features.FeatureType)
	for _, feature := range treeFeatures {
		featureMetadata[feature.Name] = feature.Type
	}
	return featureMetadata
}
