{"features": [{"name": "education", "type": "string"}, {"name": "income", "type": "string"}], "classes": ["no", "yes"], "metadata": {"created_at": "2025-09-15T19:15:29.367514017+03:00", "algorithm": "C4.5", "max_depth": 10, "min_samples": 2, "criterion": "entropy", "total_nodes": 7, "leaf_nodes": 5, "training_samples": 10, "target_column": "approved"}, "root": {"type": "decision", "feature": {"name": "education", "type": "string"}, "split_value": "education", "children": {"college": {"type": "decision", "feature": {"name": "income", "type": "string"}, "split_value": "income", "children": {"high": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 2}, "samples": 2, "confidence": 1}, "low": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 1}, "samples": 1, "confidence": 1}, "medium": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"no": 1, "yes": 3}, "samples": 4, "confidence": 0.75}, "graduate": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 3}, "samples": 3, "confidence": 1}, "high_school": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 3}, "samples": 3, "confidence": 1}}, "class_distribution": {"no": 4, "yes": 6}, "samples": 10, "confidence": 0.6}}