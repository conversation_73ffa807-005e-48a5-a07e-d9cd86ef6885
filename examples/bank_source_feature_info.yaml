age:
  type: numeric
  handle_as: integer
job:
  type: nominal
  handle_as: string
marital:
  type: nominal
  handle_as: string
education:
  type: nominal
  handle_as: string
default:
  type: binary
  handle_as: string
balance:
  type: numeric
  handle_as: integer
housing:
  type: binary
  handle_as: string
loan:
  type: binary
  handle_as: string
contact:
  type: nominal
  handle_as: string
day:
  type: numeric
  handle_as: integer
month:
  type: nominal
  handle_as: string
duration:
  type: numeric
  handle_as: integer
campaign:
  type: numeric
  handle_as: integer
pdays:
  type: numeric
  handle_as: integer
previous:
  type: numeric
  handle_as: integer
poutcome:
  type: nominal
  handle_as: string