package training

import (
	"testing"

	"github.com/berrijam/mulberri/internal/data/features"
)

// TestSplitCandidate tests the SplitCandidate struct.
func TestSplitCandidate(t *testing.T) {
	t.Run("Numerical split candidate", func(t *testing.T) {
		threshold := 30.5
		candidate := SplitCandidate{
			FeatureName: "age",
			Type:        NumericalSplit,
			Threshold:   &threshold,
			Gain:        0.23,
		}

		if candidate.FeatureName != "age" {
			t.<PERSON>("Expected feature name 'age', got %v", candidate.FeatureName)
		}

		if candidate.Type != NumericalSplit {
			t.Errorf("Expected NumericalSplit, got %v", candidate.Type)
		}

		if candidate.Threshold == nil || *candidate.Threshold != 30.5 {
			t.<PERSON><PERSON>rf("Expected threshold 30.5, got %v", candidate.Threshold)
		}

		if candidate.Gain != 0.23 {
			t.<PERSON>rrorf("Expected gain 0.23, got %v", candidate.Gain)
		}
	})

	t.Run("N-ary categorical split candidate", func(t *testing.T) {
		candidate := SplitCandidate{
			FeatureName: "education",
			Type:        CategoricalSplit,
			Value:       "college",
		}

		if candidate.FeatureName != "education" {
			t.Errorf("Expected feature name 'education', got %v", candidate.FeatureName)
		}

		if candidate.Type != CategoricalSplit {
			t.Errorf("Expected CategoricalSplit, got %v", candidate.Type)
		}

		if candidate.Value != "college" {
			t.Errorf("Expected value 'college', got %v", candidate.Value)
		}
	})
}

// TestNumericalSplitEvaluator tests the numerical split evaluator structure.

// TestSplitEvaluatorFactory tests the factory pattern for creating evaluators.
func TestSplitEvaluatorFactory(t *testing.T) {
	factory := NewSplitEvaluatorFactory[string]()

	t.Run("Create numerical evaluator for integer feature", func(t *testing.T) {
		evaluator := factory.CreateEvaluator(features.IntegerFeature)

		if evaluator == nil {
			t.Error("Expected non-nil evaluator for integer feature")
		}

		if _, ok := evaluator.(*NumericalSplitEvaluator[string]); !ok {
			t.Errorf("Expected NumericalSplitEvaluator, got %T", evaluator)
		}
	})

	t.Run("Create numerical evaluator for float feature", func(t *testing.T) {
		evaluator := factory.CreateEvaluator(features.FloatFeature)

		if evaluator == nil {
			t.Error("Expected non-nil evaluator for float feature")
		}

		if _, ok := evaluator.(*NumericalSplitEvaluator[string]); !ok {
			t.Errorf("Expected NumericalSplitEvaluator, got %T", evaluator)
		}
	})

	t.Run("Create categorical evaluator for string feature", func(t *testing.T) {
		evaluator := factory.CreateEvaluator(features.StringFeature)

		if evaluator == nil {
			t.Error("Expected non-nil evaluator for string feature")
		}

		if _, ok := evaluator.(*CategoricalSplitEvaluator[string]); !ok {
			t.Errorf("Expected CategoricalSplitEvaluator, got %T", evaluator)
		}
	})

	t.Run("Unsupported feature type", func(t *testing.T) {
		// Use an invalid feature type (cast to valid type for testing)
		invalidType := features.FeatureType(999)
		evaluator := factory.CreateEvaluator(invalidType)

		if evaluator != nil {
			t.Error("Expected nil evaluator for unsupported feature type")
		}
	})
}
