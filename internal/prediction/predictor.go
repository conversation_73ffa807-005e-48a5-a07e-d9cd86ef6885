package prediction

import (
	"context"
	"fmt"
	"runtime"
	"sync"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/tree"
	"github.com/berrijam/mulberri/internal/types"
	"github.com/berrijam/mulberri/internal/utils/batch"
	"github.com/berrijam/mulberri/internal/utils/errors"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

const (
	// DefaultBatchSize is the default number of rows to process in each batch
	DefaultBatchSize = 1000

	// DefaultMaxWorkers is the default maximum number of concurrent workers
	DefaultMaxWorkers = 4

	// MinBatchSize is the minimum batch size for concurrent processing
	MinBatchSize = 100
)

// batchResult represents the result of processing a single batch
type batchResult struct {
	startIdx int
	results  []interface{} // Can hold either strings or *types.DetailedPrediction
	err      error
}

// worker<PERSON><PERSON> manages a pool of workers for batch processing
type workerPool struct {
	maxWorkers int
	semaphore  chan struct{}
}

// newWorkerPool creates a new worker pool with the specified maximum workers.
//
// Args:
//   - maxWorkers: Maximum number of concurrent workers (0 = use CPU count)
//
// Returns:
//   - *workerPool: Configured worker pool ready for use
//
// Performance: O(1) initialization with semaphore channel creation
// Side effects: Creates buffered channel for worker coordination
func newWorkerPool(maxWorkers int) *workerPool {
	if maxWorkers <= 0 {
		maxWorkers = runtime.NumCPU()
	}
	return &workerPool{
		maxWorkers: maxWorkers,
		semaphore:  make(chan struct{}, maxWorkers),
	}
}

// acquire acquires a worker slot, blocking if all workers are busy.
//
// Args:
//   - ctx: Context for cancellation support
//
// Returns:
//   - error: Context cancellation error or nil on success
//
// Performance: O(1) channel operation, may block if pool is full
// Side effects: Blocks until worker slot available or context cancelled
func (wp *workerPool) acquire(ctx context.Context) error {
	select {
	case wp.semaphore <- struct{}{}:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// release releases a worker slot back to the pool.
//
// Performance: O(1) channel operation
// Side effects: Makes worker slot available for other goroutines
func (wp *workerPool) release() {
	<-wp.semaphore
}

// PredictionService handles decision tree prediction operations.
//
// This service provides the core prediction functionality for applying trained
// decision tree models to new data. It handles feature validation, data conversion,
// and batch prediction processing while maintaining separation from CLI concerns.
//
// Architecture: Follows the service layer pattern using global logger functions
// for simplicity and consistency with the existing codebase architecture.
// Performance: Uses worker pools and memory pooling for efficient batch processing.
// Security: No sensitive data storage, inherits dataset security model.
type PredictionService struct {
	// maxWorkers controls concurrent goroutine limit for batch processing
	maxWorkers int

	// featureMapPool provides memory pooling for feature maps to reduce GC pressure
	featureMapPool sync.Pool
}

// NewPredictionService creates a new prediction service instance.
//
// Returns:
//   - *PredictionService: Configured service with default worker count and memory pooling
//
// Performance: O(1) initialization with sync.Pool setup
// Side effects: Initializes memory pool for feature maps
func NewPredictionService() *PredictionService {
	return &PredictionService{
		maxWorkers: DefaultMaxWorkers,
		featureMapPool: sync.Pool{
			New: func() interface{} {
				return make(map[string]interface{})
			},
		},
	}
}

// getFeatureMap retrieves a clean feature map from the memory pool.
//
// Returns:
//   - map[string]interface{}: Clean feature map ready for use
//
// Performance: O(n) where n is previous map size for clearing, reduces allocations
// Side effects: Clears any existing entries from pooled map
func (ps *PredictionService) getFeatureMap() map[string]interface{} {
	featureMap := ps.featureMapPool.Get().(map[string]interface{})
	// Clear the map for reuse
	for k := range featureMap {
		delete(featureMap, k)
	}
	return featureMap
}

// putFeatureMap returns a feature map to the memory pool for reuse.
//
// Args:
//   - featureMap: Map to return to pool (should be cleared by getFeatureMap on next use)
//
// Performance: O(1) pool return operation
func (ps *PredictionService) putFeatureMap(featureMap map[string]interface{}) {
	ps.featureMapPool.Put(featureMap)
}

// createDetailedPrediction creates a DetailedPrediction from tree result and input data.
//
// Args:
//   - rowIdx: Row index for the prediction
//   - featureMap: Input features used for prediction
//   - treeResult: Result from tree prediction with decision path
//
// Returns:
//   - *types.DetailedPrediction: Detailed prediction with all metadata
//
// Performance: O(num_features + num_classes) for map copying and conversion
// Side effects: Creates copies of input maps to avoid reference issues
func (ps *PredictionService) createDetailedPrediction(rowIdx int, featureMap map[string]interface{}, treeResult *tree.DetailedPredictionResult) *types.DetailedPrediction {
	// Convert class distribution from interface{} keys to string keys
	stringClassDistribution := make(map[string]int)
	for class, count := range treeResult.ClassDistribution {
		stringClassDistribution[fmt.Sprintf("%v", class)] = count
	}

	// Create a copy of the feature map for the result
	inputFeaturesCopy := make(map[string]interface{})
	for k, v := range featureMap {
		inputFeaturesCopy[k] = v
	}

	return &types.DetailedPrediction{
		RowIndex:          rowIdx,
		InputFeatures:     inputFeaturesCopy,
		Prediction:        fmt.Sprintf("%v", treeResult.Prediction),
		Confidence:        treeResult.Confidence,
		DecisionPath:      treeResult.DecisionPath,
		DecisionRule:      treeResult.DecisionRule,
		ClassDistribution: stringClassDistribution,
	}
}

// extractFeatures extracts features from a dataset row into a feature map.
//
// Args:
//   - datasetView: Dataset view containing the data
//   - rowIdx: Logical row index within the view
//   - featureOrder: Ordered list of feature names to extract
//   - featureMap: Target map to populate with feature values
//
// Performance: O(f) where f is number of features
// Side effects: Populates featureMap with feature values, handles missing features gracefully
func (ps *PredictionService) extractFeatures(datasetView *dataset.DatasetView[string], rowIdx int, featureOrder []string, featureMap map[string]interface{}) {
	for _, featureName := range featureOrder {
		value := datasetView.GetFeatureValue(rowIdx, featureName)
		if value != nil {
			// Convert pointer values to actual values for the tree prediction
			featureMap[featureName] = ps.convertPointerToValue(value)
		}
		// If value is nil (missing), we don't add it to the map
		// The tree will handle missing features appropriately
	}
}

// processRowSimple processes a single row for simple prediction.
//
// Args:
//   - ctx: Context for cancellation support
//   - decisionTree: Trained decision tree model
//   - datasetView: Dataset view containing the row data
//   - rowIdx: Logical row index within the view
//   - featureOrder: Ordered list of feature names
//
// Returns:
//   - string: Predicted class as string
//   - error: Error if prediction fails or context cancelled
//
// Performance: O(log(tree_depth)) for single row prediction
// Side effects: Uses memory pool for feature map, logs errors on failure
func (ps *PredictionService) processRowSimple(ctx context.Context, decisionTree *tree.DecisionTree, datasetView *dataset.DatasetView[string], rowIdx int, featureOrder []string) (string, error) {
	select {
	case <-ctx.Done():
		return "", ctx.Err()
	default:
	}

	featureMap := ps.getFeatureMap()
	defer ps.putFeatureMap(featureMap)

	ps.extractFeatures(datasetView, rowIdx, featureOrder, featureMap)

	prediction := decisionTree.Predict(featureMap)
	if prediction == nil {
		return "", fmt.Errorf("failed to get prediction for row %d", rowIdx)
	}

	return fmt.Sprintf("%v", prediction), nil
}

// processRowDetailed processes a single row for detailed prediction.
//
// Args:
//   - ctx: Context for cancellation support
//   - decisionTree: Trained decision tree model
//   - datasetView: Dataset view containing the row data
//   - rowIdx: Logical row index within the view
//   - featureOrder: Ordered list of feature names
//
// Returns:
//   - *types.DetailedPrediction: Detailed prediction with decision path and confidence
//   - error: Error if prediction fails or context cancelled
//
// Performance: O(log(tree_depth)) for single row prediction with path tracking
// Side effects: Uses memory pool for feature map, logs errors on failure
func (ps *PredictionService) processRowDetailed(ctx context.Context, decisionTree *tree.DecisionTree, datasetView *dataset.DatasetView[string], rowIdx int, featureOrder []string) (*types.DetailedPrediction, error) {
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	featureMap := ps.getFeatureMap()
	defer ps.putFeatureMap(featureMap)

	ps.extractFeatures(datasetView, rowIdx, featureOrder, featureMap)

	treeResult := decisionTree.PredictDetailed(featureMap)
	if treeResult == nil {
		return nil, fmt.Errorf("failed to get detailed prediction for row %d", rowIdx)
	}

	return ps.createDetailedPrediction(rowIdx, featureMap, treeResult), nil
}

// PredictSimple generates simple predictions for the dataset.
//
// Args:
//   - model: Trained decision tree model for predictions
//   - dataset: Input dataset without target column
//   - batchSize: Number of rows to process per batch (0 = use default)
//
// Returns:
//   - []string: Prediction results for each row
//   - error: Error if prediction fails
//
// Performance: O(n * log(tree_depth)) where n is number of rows
// Side effects: Logs prediction progress and performance metrics
func (ps *PredictionService) PredictSimple(model *tree.DecisionTree, dataset *dataset.Dataset[string], batchSize int) ([]string, error) {
	return ps.PredictSimpleWithContext(context.Background(), model, dataset, batchSize)
}

// PredictSimpleWithContext generates simple predictions with context support.
//
// Args:
//   - ctx: Context for cancellation and timeout control
//   - model: Trained decision tree model for predictions
//   - dataset: Input dataset without target column
//   - batchSize: Number of rows to process per batch (0 = use default)
//
// Returns:
//   - []string: Prediction results for each row
//   - error: Error if prediction fails or context cancelled
//
// Performance: O(n * log(tree_depth) / workers) for large datasets
// Side effects: May spawn worker goroutines for batch processing
func (ps *PredictionService) PredictSimpleWithContext(ctx context.Context, model *tree.DecisionTree, dataset *dataset.Dataset[string], batchSize int) ([]string, error) {
	// Validate inputs
	if model == nil {
		return nil, fmt.Errorf("decision tree model cannot be nil")
	}
	if dataset == nil {
		return nil, fmt.Errorf("prediction dataset cannot be nil")
	}

	rowCount := dataset.GetRowCount()
	if rowCount == 0 {
		logger.Info("No rows to predict")
		return []string{}, nil
	}

	logger.Info(fmt.Sprintf("Starting simple prediction for %d rows", rowCount))

	// Set default batch size if not provided
	if batchSize <= 0 {
		batchSize = DefaultBatchSize
	}

	// Use batch processing for large datasets
	if rowCount > MinBatchSize {
		return ps.generatePredictionsBatch(ctx, model, dataset, batchSize)
	} else {
		return ps.generatePredictions(ctx, model, dataset)
	}
}

// PredictDetailed generates detailed predictions for the dataset.
//
// Args:
//   - model: Trained decision tree model for predictions
//   - dataset: Input dataset without target column
//   - batchSize: Number of rows to process per batch (0 = use default)
//
// Returns:
//   - []*types.DetailedPrediction: Detailed predictions with decision paths and confidence
//   - error: Error if prediction fails
//
// Performance: O(n * log(tree_depth)) where n is number of rows
// Side effects: Logs prediction progress and performance metrics
func (ps *PredictionService) PredictDetailed(model *tree.DecisionTree, dataset *dataset.Dataset[string], batchSize int) ([]*types.DetailedPrediction, error) {
	return ps.PredictDetailedWithContext(context.Background(), model, dataset, batchSize)
}

// PredictDetailedWithContext generates detailed predictions with context support.
//
// Args:
//   - ctx: Context for cancellation and timeout control
//   - model: Trained decision tree model for predictions
//   - dataset: Input dataset without target column
//   - batchSize: Number of rows to process per batch (0 = use default)
//
// Returns:
//   - []*types.DetailedPrediction: Detailed predictions with decision paths and confidence
//   - error: Error if prediction fails or context cancelled
//
// Performance: O(n * log(tree_depth) / workers) for large datasets
// Side effects: May spawn worker goroutines for batch processing
func (ps *PredictionService) PredictDetailedWithContext(ctx context.Context, model *tree.DecisionTree, dataset *dataset.Dataset[string], batchSize int) ([]*types.DetailedPrediction, error) {
	// Validate inputs
	if model == nil {
		return nil, fmt.Errorf("decision tree model cannot be nil")
	}
	if dataset == nil {
		return nil, fmt.Errorf("prediction dataset cannot be nil")
	}

	rowCount := dataset.GetRowCount()
	if rowCount == 0 {
		logger.Info("No rows to predict")
		return []*types.DetailedPrediction{}, nil
	}

	logger.Info(fmt.Sprintf("Starting detailed prediction for %d rows", rowCount))

	// Set default batch size if not provided
	if batchSize <= 0 {
		batchSize = DefaultBatchSize
	}

	// Use batch processing for large datasets
	if rowCount > MinBatchSize {
		return ps.generateDetailedPredictionsBatch(ctx, model, dataset, batchSize)
	} else {
		return ps.generateDetailedPredictions(ctx, model, dataset)
	}
}

// generatePredictions applies the decision tree to each row in the dataset to generate predictions.
//
// This internal method handles the core prediction logic by iterating through dataset rows,
// converting each row to a feature map, and applying the decision tree for prediction.
//
// Args:
//   - ctx: Context for cancellation support
//   - decisionTree: Trained decision tree model to use for predictions
//   - predictionDataset: Dataset containing the data to make predictions on
//
// Returns:
//   - []string: Slice of predictions, one for each row in the dataset
//   - error: Error if prediction generation fails
//
// Performance: O(n * log(tree_depth)) where n is the number of rows
// Side effects: Logs prediction progress for debugging
func (ps *PredictionService) generatePredictions(ctx context.Context, decisionTree *tree.DecisionTree, predictionDataset *dataset.Dataset[string]) ([]string, error) {
	rowCount := predictionDataset.GetRowCount()
	predictions := make([]string, rowCount)
	featureOrder := predictionDataset.GetFeatureOrder()

	logger.Debug(fmt.Sprintf("Generating predictions for %d rows using %d features", rowCount, len(featureOrder)))

	// Create a view of all indices for iteration
	allIndices := make([]int, rowCount)
	for i := 0; i < rowCount; i++ {
		allIndices[i] = i
	}
	datasetView := predictionDataset.CreateView(allIndices)

	// Process each row
	for i := 0; i < rowCount; i++ {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		prediction, err := ps.processRowSimple(ctx, decisionTree, datasetView, i, featureOrder)
		if err != nil {
			return nil, errors.LogErrorAndReturn(fmt.Sprintf("Failed to get prediction for row %d", i), err)
		}

		predictions[i] = prediction

		// Log progress for large datasets
		if rowCount > 1000 && (i+1)%1000 == 0 {
			logger.Debug(fmt.Sprintf("Processed %d/%d predictions", i+1, rowCount))
		}
	}

	logger.Debug(fmt.Sprintf("Successfully generated %d predictions", len(predictions)))
	return predictions, nil
}

// generateDetailedPredictions applies the decision tree to each row to generate detailed predictions.
//
// This internal method handles the detailed prediction logic by iterating through dataset rows,
// converting each row to a feature map, and applying the decision tree for detailed prediction
// including decision path, confidence, and reasoning.
//
// Args:
//   - ctx: Context for cancellation support
//   - decisionTree: Trained decision tree model to use for predictions
//   - predictionDataset: Dataset containing the data to make predictions on
//
// Returns:
//   - []*DetailedPrediction: Slice of detailed predictions, one for each row in the dataset
//   - error: Error if prediction generation fails
//
// Performance: O(n * log(tree_depth)) where n is the number of rows
// Side effects: Logs prediction progress for debugging
func (ps *PredictionService) generateDetailedPredictions(ctx context.Context, decisionTree *tree.DecisionTree, predictionDataset *dataset.Dataset[string]) ([]*types.DetailedPrediction, error) {
	rowCount := predictionDataset.GetRowCount()
	detailedPredictions := make([]*types.DetailedPrediction, rowCount)
	featureOrder := predictionDataset.GetFeatureOrder()

	logger.Debug(fmt.Sprintf("Generating detailed predictions for %d rows using %d features", rowCount, len(featureOrder)))

	// Create a view of all indices for iteration
	allIndices := make([]int, rowCount)
	for i := 0; i < rowCount; i++ {
		allIndices[i] = i
	}
	datasetView := predictionDataset.CreateView(allIndices)

	// Process each row
	for i := 0; i < rowCount; i++ {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		detailedPrediction, err := ps.processRowDetailed(ctx, decisionTree, datasetView, i, featureOrder)
		if err != nil {
			return nil, errors.LogErrorAndReturn(fmt.Sprintf("Failed to get detailed prediction for row %d", i), err)
		}

		detailedPredictions[i] = detailedPrediction

		// Log progress for large datasets
		if rowCount > 1000 && (i+1)%1000 == 0 {
			logger.Debug(fmt.Sprintf("Processed %d/%d detailed predictions", i+1, rowCount))
		}
	}

	logger.Debug(fmt.Sprintf("Successfully generated %d detailed predictions", len(detailedPredictions)))
	return detailedPredictions, nil
}

// convertPointerToValue converts pointer values from dataset to actual values for tree prediction.
//
// The dataset returns pointers to distinguish between valid data and missing values.
// convertPointerToValue converts pointer values to actual values for tree prediction.
//
// This method extracts the actual values needed for tree traversal from dataset
// pointer types, handling nil pointers gracefully.
//
// Args:
//   - value: Pointer value from dataset (may be *int64, *float64, *string, or direct value)
//
// Returns:
//   - interface{}: Dereferenced value ready for tree prediction, or nil if pointer is nil
//
// Performance: O(1) type switch and dereference operation
// Side effects: None, pure function for value conversion
func (ps *PredictionService) convertPointerToValue(value interface{}) interface{} {
	switch v := value.(type) {
	case *int64:
		if v != nil {
			return *v
		}
	case *float64:
		if v != nil {
			return *v
		}
	case *string:
		if v != nil {
			return *v
		}
	}
	return value
}

// generatePredictionsBatch generates simple predictions using goroutines for batch processing.
//
// This method processes large datasets efficiently by dividing the work into batches
// and using multiple goroutines to process batches concurrently with proper synchronization.
//
// Args:
//   - ctx: Context for cancellation support
//   - decisionTree: Trained decision tree model to use for predictions
//   - predictionDataset: Dataset containing the data to make predictions on
//   - batchSize: Number of rows to process in each batch
//
// Returns:
//   - []string: Slice of predictions, one for each row in the dataset
//   - error: Error if prediction generation fails
//
// Performance: O(n * log(tree_depth) / num_workers) where n is the number of rows
func (ps *PredictionService) generatePredictionsBatch(ctx context.Context, decisionTree *tree.DecisionTree, predictionDataset *dataset.Dataset[string], batchSize int) ([]string, error) {
	rowCount := predictionDataset.GetRowCount()
	predictions := make([]string, rowCount)
	featureOrder := predictionDataset.GetFeatureOrder()

	logger.Debug(fmt.Sprintf("Generating predictions for %d rows using batch processing (batch size: %d, max workers: %d)", rowCount, batchSize, ps.maxWorkers))

	// Calculate number of batches
	numBatches := (rowCount + batchSize - 1) / batchSize

	// Create worker pool and result channel
	workerPool := newWorkerPool(ps.maxWorkers)
	resultChan := make(chan batchResult, numBatches)
	var wg sync.WaitGroup

	// Process batches concurrently
	for batchIdx := 0; batchIdx < numBatches; batchIdx++ {
		startIdx := batchIdx * batchSize
		endIdx := startIdx + batchSize
		if endIdx > rowCount {
			endIdx = rowCount
		}

		wg.Add(1)
		go func(start, end, batchIndex int) {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					resultChan <- batchResult{
						startIdx: start,
						err:      fmt.Errorf("batch %d panic: %v", batchIndex, r),
					}
				}
			}()

			// Acquire worker slot
			if err := workerPool.acquire(ctx); err != nil {
				resultChan <- batchResult{
					startIdx: start,
					err:      err,
				}
				return
			}
			defer workerPool.release()

			// Create dataset view for this batch
			datasetView := batch.CreateDatasetView(predictionDataset, start, end)

			// Process each row in this batch
			batchResults := make([]interface{}, end-start)
			for i := 0; i < end-start; i++ {
				select {
				case <-ctx.Done():
					resultChan <- batchResult{
						startIdx: start,
						err:      ctx.Err(),
					}
					return
				default:
				}

				prediction, err := ps.processRowSimple(ctx, decisionTree, datasetView, i, featureOrder)
				if err != nil {
					resultChan <- batchResult{
						startIdx: start,
						err:      fmt.Errorf("failed to get prediction for row %d: %v", start+i, err),
					}
					return
				}

				batchResults[i] = prediction
			}

			resultChan <- batchResult{
				startIdx: start,
				results:  batchResults,
				err:      nil,
			}
		}(startIdx, endIdx, batchIdx)
	}

	// Close result channel when all workers are done
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// Collect results
	for result := range resultChan {
		if result.err != nil {
			return nil, result.err
		}

		// Copy results to the main predictions slice
		for i, pred := range result.results {
			predictions[result.startIdx+i] = pred.(string)
		}
	}

	logger.Debug(fmt.Sprintf("Successfully generated %d predictions using batch processing", len(predictions)))
	return predictions, nil
}

// generateDetailedPredictionsBatch generates detailed predictions using goroutines for batch processing.
//
// This method processes large datasets efficiently by dividing the work into batches
// and using multiple goroutines to process batches concurrently with proper synchronization.
//
// Args:
//   - ctx: Context for cancellation support
//   - decisionTree: Trained decision tree model to use for predictions
//   - predictionDataset: Dataset containing the data to make predictions on
//   - batchSize: Number of rows to process in each batch
//
// Returns:
//   - []*DetailedPrediction: Slice of detailed predictions with decision paths
//   - error: Error if prediction generation fails
//
// Performance: O(n * log(tree_depth) / num_workers) where n is the number of rows
func (ps *PredictionService) generateDetailedPredictionsBatch(ctx context.Context, decisionTree *tree.DecisionTree, predictionDataset *dataset.Dataset[string], batchSize int) ([]*types.DetailedPrediction, error) {
	rowCount := predictionDataset.GetRowCount()
	detailedPredictions := make([]*types.DetailedPrediction, rowCount)
	featureOrder := predictionDataset.GetFeatureOrder()

	logger.Debug(fmt.Sprintf("Generating detailed predictions for %d rows using batch processing (batch size: %d, max workers: %d)", rowCount, batchSize, ps.maxWorkers))

	// Calculate number of batches
	numBatches := (rowCount + batchSize - 1) / batchSize

	// Create worker pool and result channel
	workerPool := newWorkerPool(ps.maxWorkers)
	resultChan := make(chan batchResult, numBatches)
	var wg sync.WaitGroup

	// Process batches concurrently
	for batchIdx := 0; batchIdx < numBatches; batchIdx++ {
		startIdx := batchIdx * batchSize
		endIdx := startIdx + batchSize
		if endIdx > rowCount {
			endIdx = rowCount
		}

		wg.Add(1)
		go func(start, end, batchIndex int) {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					resultChan <- batchResult{
						startIdx: start,
						err:      fmt.Errorf("batch %d panic: %v", batchIndex, r),
					}
				}
			}()

			// Acquire worker slot
			if err := workerPool.acquire(ctx); err != nil {
				resultChan <- batchResult{
					startIdx: start,
					err:      err,
				}
				return
			}
			defer workerPool.release()

			// Create dataset view for this batch
			datasetView := batch.CreateDatasetView(predictionDataset, start, end)

			// Process each row in this batch
			batchResults := make([]interface{}, end-start)
			for i := 0; i < end-start; i++ {
				select {
				case <-ctx.Done():
					resultChan <- batchResult{
						startIdx: start,
						err:      ctx.Err(),
					}
					return
				default:
				}

				detailedPrediction, err := ps.processRowDetailed(ctx, decisionTree, datasetView, i, featureOrder)
				if err != nil {
					resultChan <- batchResult{
						startIdx: start,
						err:      fmt.Errorf("failed to get detailed prediction for row %d: %v", start+i, err),
					}
					return
				}

				// Update the row index to reflect the global position
				detailedPrediction.RowIndex = start + i
				batchResults[i] = detailedPrediction
			}

			resultChan <- batchResult{
				startIdx: start,
				results:  batchResults,
				err:      nil,
			}
		}(startIdx, endIdx, batchIdx)
	}

	// Close result channel when all workers are done
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// Collect results
	for result := range resultChan {
		if result.err != nil {
			return nil, result.err
		}

		// Copy results to the main predictions slice
		for i, pred := range result.results {
			if detailedPred, ok := pred.(*types.DetailedPrediction); ok {
				detailedPredictions[result.startIdx+i] = detailedPred
			} else {
				return nil, errors.LogErrorAndReturnSimple(fmt.Sprintf("Invalid prediction type in batch result: %T", pred))
			}
		}
	}

	logger.Debug(fmt.Sprintf("Successfully generated %d detailed predictions using batch processing", len(detailedPredictions)))
	return detailedPredictions, nil
}
