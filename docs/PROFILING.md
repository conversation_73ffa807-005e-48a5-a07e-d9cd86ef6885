# Profiling Guide for Mulberri

This guide explains how to use the built-in profiling capabilities in Mulberri to analyze performance and identify bottlenecks in your decision tree training and prediction workflows.

## Overview

Mulberri includes file-based profiling support using Go's built-in `pprof` package. This allows you to:

- **CPU Profiling**: Identify which functions consume the most CPU time
- **Memory Profiling**: Analyze memory allocation patterns and identify memory leaks
- **Goroutine Profiling**: Debug concurrency issues and goroutine leaks

Profile data is written to files that can be analyzed using Go's `pprof` tool.

## Quick Start

### Enable Profiling

Add profiling flags to your train or predict commands:

```bash
# Enable CPU profiling for training
./mulberri train -i data.csv -t target -o model.dt -f features.yaml --cpu-profile

# Enable memory profiling for prediction
./mulberri predict -i test.csv -m model.dt -o predictions.csv --mem-profile

# Enable both CPU and memory profiling
./mulberri train -i data.csv -t target -o model.dt -f features.yaml --cpu-profile --mem-profile

# Specify custom profile output directory
./mulberri train -i data.csv -t target -o model.dt -f features.yaml --cpu-profile --profile-dir ./my-profiles
```

### Profile Files

Profile files are automatically named with timestamps:
- CPU profiles: `train-cpu-20240917-143052.prof`
- Memory profiles: `predict-mem-20240917-143052.prof`
- Default location: `./profiles/` directory

## Analyzing Profiles

### CPU Profiles

CPU profiles show where your program spends time:

```bash
# Interactive analysis
go tool pprof profiles/train-cpu-20240917-143052.prof

# Generate web interface (requires graphviz)
go tool pprof -http=:8080 profiles/train-cpu-20240917-143052.prof

# Top functions by CPU usage
go tool pprof -top profiles/train-cpu-20240917-143052.prof

# Generate flame graph
go tool pprof -png profiles/train-cpu-20240917-143052.prof > cpu-profile.png
```

### Memory Profiles

Memory profiles show allocation patterns:

```bash
# Interactive analysis
go tool pprof profiles/predict-mem-20240917-143052.prof

# Top functions by memory allocation
go tool pprof -top profiles/predict-mem-20240917-143052.prof

# Show allocations in specific function
go tool pprof -list=FunctionName profiles/predict-mem-20240917-143052.prof

# Generate memory usage graph
go tool pprof -png profiles/predict-mem-20240917-143052.prof > mem-profile.png
```

## Common pprof Commands

Once in the interactive pprof shell:

```
(pprof) top10          # Show top 10 functions
(pprof) list main      # Show source code for main function
(pprof) web            # Open web interface (requires graphviz)
(pprof) png            # Generate PNG graph
(pprof) help           # Show all available commands
```

## Performance Analysis Workflow

### 1. Baseline Measurement

First, run without profiling to establish baseline performance:

```bash
time ./mulberri train -i large-dataset.csv -t target -o model.dt -f features.yaml
```

### 2. CPU Profiling

Identify CPU bottlenecks:

```bash
./mulberri train -i large-dataset.csv -t target -o model.dt -f features.yaml --cpu-profile
go tool pprof -top profiles/train-cpu-*.prof
```

Look for:
- Functions with high `flat%` (time spent in function itself)
- Functions with high `cum%` (time spent in function and callees)
- Unexpected hot spots

### 3. Memory Profiling

Analyze memory usage:

```bash
./mulberri train -i large-dataset.csv -t target -o model.dt -f features.yaml --mem-profile
go tool pprof -top profiles/train-mem-*.prof
```

Look for:
- Large memory allocations
- Functions that allocate frequently
- Potential memory leaks

### 4. Optimization

Based on profiling results:
- Optimize hot code paths identified in CPU profiles
- Reduce allocations in memory-intensive functions
- Consider algorithmic improvements for bottlenecks

## Example Analysis Session

```bash
# 1. Train with profiling enabled
./mulberri train -i examples/loan_approval.csv -t approved -o model.dt -f examples/loan_approval_features.yaml --cpu-profile --mem-profile

# 2. Analyze CPU usage
go tool pprof profiles/train-cpu-*.prof
(pprof) top10
(pprof) list BuildTree
(pprof) web

# 3. Analyze memory usage
go tool pprof profiles/train-mem-*.prof
(pprof) top10
(pprof) list LoadCSVToDataset
```

## Profiling Best Practices

### When to Profile

- **Performance Issues**: When training or prediction is slower than expected
- **Memory Problems**: When experiencing high memory usage or potential leaks
- **Optimization**: Before and after performance improvements to measure impact
- **Large Datasets**: When working with datasets that push system limits

### What to Profile

- **Training**: Focus on tree building algorithms, data loading, and feature processing
- **Prediction**: Focus on tree traversal, batch processing, and output generation
- **Both**: Memory allocation patterns and garbage collection impact

### Profile Interpretation

- **CPU Profiles**: Focus on `flat%` for direct optimization targets
- **Memory Profiles**: Look for allocation patterns, not just total usage
- **Comparative Analysis**: Profile before and after changes to measure improvement

## Troubleshooting

### Common Issues

1. **No profile generated**: Check that profiling flags are enabled and output directory is writable
2. **Empty profiles**: Ensure the operation runs long enough to collect meaningful data
3. **Large profile files**: Consider profiling smaller datasets first for initial analysis

### Performance Impact

- CPU profiling adds ~5% overhead
- Memory profiling adds minimal overhead
- Profile file sizes are typically small (< 10MB)

## Integration with CI/CD

For automated performance monitoring:

```bash
# Run benchmarks with profiling
./scripts/benchmark.sh --profile

# Compare profiles across versions
go tool pprof -base=old.prof new.prof
```

## Reading and Interpreting Profile Files

### Automated Analysis Tools

We provide several tools to help you analyze profiles:

```bash
# Generate comprehensive analysis report
./scripts/analyze_profiles.sh

# Interactive profile explorer
./scripts/profile_reader.sh

# Quick analysis example
./scripts/profile_example.sh
```

### Manual Profile Analysis

#### CPU Profile Interpretation

```bash
# View top CPU consumers
go tool pprof -text profiles/train-cpu-*.prof
```

**Key metrics to understand:**
- **flat%**: Time spent directly in this function (excluding callees)
- **cum%**: Time spent in this function and all its callees
- **flat**: Absolute time spent in this function
- **cum**: Absolute time including callees

**What to look for:**
- Functions with high `flat%` (>10%) are optimization targets
- Functions with high `cum%` but low `flat%` indicate expensive callees
- Unexpected functions in top 10 may indicate inefficiencies

#### Memory Profile Interpretation

```bash
# View memory allocations
go tool pprof -text profiles/train-mem-*.prof
```

**Key metrics:**
- **flat**: Memory allocated directly by this function
- **flat%**: Percentage of total memory allocated by this function
- **cum**: Memory allocated by this function and callees
- **cum%**: Cumulative percentage

**What to look for:**
- Large single allocations (>1MB) may need streaming
- High allocation frequency may benefit from object pooling
- Memory leaks show up as continuously growing allocations

### Profile Analysis Report Categories

Our analysis tool categorizes findings into three levels:

#### 🔴 CRITICAL & SERIOUS
**Immediate attention required**
- Functions consuming >20% CPU time
- Memory allocations >1MB per operation
- Memory leaks or unbounded growth
- Blocking operations in hot paths

**Actions:**
- Profile with larger datasets to confirm
- Implement algorithmic optimizations
- Add streaming/chunking for large data
- Fix memory leaks immediately

#### 🟡 CRITICAL BUT NOT SERIOUS
**Optimization opportunities**
- JSON serialization overhead
- Excessive logging in hot paths
- Inefficient memory pool usage
- Suboptimal data structures

**Actions:**
- Consider binary serialization formats
- Implement log levels and async logging
- Tune memory pool configurations
- Optimize data structure choices

#### ✅ OKAY
**Normal operation, monitoring recommended**
- Expected algorithm execution patterns
- Reasonable profiling overhead
- Normal garbage collection activity
- Appropriate library usage

**Actions:**
- Monitor for performance regressions
- Establish performance baselines
- Continue regular profiling
- Document expected patterns

### Specific Findings from Current Analysis

Based on the generated report, here are the key findings:

#### Training Performance
- **Duration**: ~200ms for 30 samples
- **Memory Usage**: ~3.3MB total
- **Main Issues**:
  - JSON serialization overhead (50% of CPU time)
  - Logging overhead in debug mode
  - Expected profiling overhead

#### Prediction Performance
- **Duration**: ~200ms for 15 samples
- **Memory Usage**: ~1.2MB total
- **Main Issues**:
  - Minimal CPU usage (good!)
  - Profiling overhead dominates memory usage

### Recommendations by Priority

#### High Priority (Implement Soon)
1. **Reduce JSON overhead**: Consider binary formats for model serialization
2. **Optimize logging**: Implement log levels, disable debug logs in production
3. **Benchmark with larger datasets**: Current profiles are too small to show real bottlenecks

#### Medium Priority (Next Quarter)
1. **Memory pool optimization**: Tune buffer pool sizes based on usage patterns
2. **Streaming implementation**: For datasets >10k samples
3. **Parallel processing**: Implement concurrent tree building

#### Low Priority (Future Optimization)
1. **Algorithm optimization**: Profile with real-world datasets
2. **Memory-mapped files**: For very large datasets
3. **Caching strategies**: Implement intelligent caching

### Performance Baselines

Current performance with small test dataset:

| Operation | Duration | Memory | Samples |
|-----------|----------|---------|---------|
| Training | 200ms | 3.3MB | 30 |
| Prediction | 200ms | 1.2MB | 15 |

**Note**: These baselines will change significantly with larger datasets. The current profiles are dominated by overhead rather than algorithm performance.

## Advanced Features

The profiling package also supports:
- Goroutine profiling for concurrency analysis
- Custom profile output directories
- Timestamped filenames for historical analysis
- Automated report generation with severity classification
- Interactive profile exploration tools

See the source code in `internal/utils/profiling/` for additional capabilities.
