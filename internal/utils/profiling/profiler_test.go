package profiling

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

func TestNewProfiler(t *testing.T) {
	outputDir := "./test_profiles"
	profiler := NewProfiler(outputDir)

	if profiler.outputDir != outputDir {
		t.<PERSON>rrorf("Expected output directory %s, got %s", outputDir, profiler.outputDir)
	}

	if profiler.cpuProfiling {
		t.<PERSON>rror("Expected CPU profiling to be false initially")
	}

	if profiler.cpuFile != nil {
		t.Error("Expected CPU file to be nil initially")
	}
}

func TestEnsureOutputDir(t *testing.T) {
	outputDir := "./test_profiles_ensure"
	profiler := NewProfiler(outputDir)

	// Clean up before test
	os.RemoveAll(outputDir)

	err := profiler.ensureOutputDir()
	if err != nil {
		t.Fatalf("Failed to ensure output directory: %v", err)
	}

	// Check if directory was created
	if _, err := os.Stat(outputDir); os.IsNotExist(err) {
		t.Error("Output directory was not created")
	}

	// Clean up after test
	os.RemoveAll(outputDir)
}

func TestStartStopCPUProfile(t *testing.T) {
	outputDir := "./test_profiles_cpu"
	profiler := NewProfiler(outputDir)

	// Clean up before test
	os.RemoveAll(outputDir)
	defer os.RemoveAll(outputDir)

	filename := "test-cpu.prof"

	// Test starting CPU profile
	err := profiler.StartCPUProfile(filename)
	if err != nil {
		t.Fatalf("Failed to start CPU profile: %v", err)
	}

	if !profiler.cpuProfiling {
		t.Error("Expected CPU profiling to be true after starting")
	}

	if profiler.cpuFile == nil {
		t.Error("Expected CPU file to be set after starting")
	}

	// Test that starting again fails
	err = profiler.StartCPUProfile("another.prof")
	if err == nil {
		t.Error("Expected error when starting CPU profile twice")
	}

	// Do some work to generate profile data
	time.Sleep(10 * time.Millisecond)

	// Test stopping CPU profile
	profiler.StopCPUProfile()

	if profiler.cpuProfiling {
		t.Error("Expected CPU profiling to be false after stopping")
	}

	if profiler.cpuFile != nil {
		t.Error("Expected CPU file to be nil after stopping")
	}

	// Check if profile file was created
	profilePath := filepath.Join(outputDir, filename)
	if _, err := os.Stat(profilePath); os.IsNotExist(err) {
		t.Error("CPU profile file was not created")
	}

	// Test that stopping again is safe
	profiler.StopCPUProfile() // Should not panic or error
}

func TestWriteMemProfile(t *testing.T) {
	outputDir := "./test_profiles_mem"
	profiler := NewProfiler(outputDir)

	// Clean up before test
	os.RemoveAll(outputDir)
	defer os.RemoveAll(outputDir)

	filename := "test-mem.prof"

	err := profiler.WriteMemProfile(filename)
	if err != nil {
		t.Fatalf("Failed to write memory profile: %v", err)
	}

	// Check if profile file was created
	profilePath := filepath.Join(outputDir, filename)
	if _, err := os.Stat(profilePath); os.IsNotExist(err) {
		t.Error("Memory profile file was not created")
	}

	// Check if file has content
	info, err := os.Stat(profilePath)
	if err != nil {
		t.Fatalf("Failed to stat profile file: %v", err)
	}

	if info.Size() == 0 {
		t.Error("Memory profile file is empty")
	}
}

func TestWriteGoroutineProfile(t *testing.T) {
	outputDir := "./test_profiles_goroutine"
	profiler := NewProfiler(outputDir)

	// Clean up before test
	os.RemoveAll(outputDir)
	defer os.RemoveAll(outputDir)

	filename := "test-goroutine.prof"

	err := profiler.WriteGoroutineProfile(filename)
	if err != nil {
		t.Fatalf("Failed to write goroutine profile: %v", err)
	}

	// Check if profile file was created
	profilePath := filepath.Join(outputDir, filename)
	if _, err := os.Stat(profilePath); os.IsNotExist(err) {
		t.Error("Goroutine profile file was not created")
	}

	// Check if file has content
	info, err := os.Stat(profilePath)
	if err != nil {
		t.Fatalf("Failed to stat profile file: %v", err)
	}

	if info.Size() == 0 {
		t.Error("Goroutine profile file is empty")
	}
}

func TestGenerateTimestampedFilename(t *testing.T) {
	operation := "test"
	profileType := "cpu"

	filename := GenerateTimestampedFilename(operation, profileType)

	// Check format: operation-profileType-timestamp.prof
	if !strings.HasPrefix(filename, operation+"-"+profileType+"-") {
		t.Errorf("Filename should start with '%s-%s-', got %s", operation, profileType, filename)
	}

	if !strings.HasSuffix(filename, ".prof") {
		t.Errorf("Filename should end with '.prof', got %s", filename)
	}

	// Check that timestamp is reasonable (should be current time)
	parts := strings.Split(filename, "-")
	if len(parts) < 3 {
		t.Errorf("Filename should have at least 3 parts separated by '-', got %s", filename)
	}

	// Extract timestamp part (remove .prof extension)
	timestampPart := strings.TrimSuffix(parts[len(parts)-1], ".prof")
	if len(timestampPart) != 6 { // HHMMSS format
		t.Errorf("Timestamp part should be 6 characters (HHMMSS), got %s", timestampPart)
	}
}

func TestGetProfileOutputDir(t *testing.T) {
	expected := "./profiles"
	actual := GetProfileOutputDir()

	if actual != expected {
		t.Errorf("Expected default profile output dir %s, got %s", expected, actual)
	}
}

func TestProfilerWithInvalidDirectory(t *testing.T) {
	// Try to create profiler with invalid directory (assuming /root is not writable)
	outputDir := "/root/invalid_profiles"
	profiler := NewProfiler(outputDir)

	err := profiler.StartCPUProfile("test.prof")
	if err == nil {
		t.Error("Expected error when creating profile in invalid directory")
		profiler.StopCPUProfile()
	}
}

func TestMultipleMemoryProfiles(t *testing.T) {
	outputDir := "./test_profiles_multi_mem"
	profiler := NewProfiler(outputDir)

	// Clean up before test
	os.RemoveAll(outputDir)
	defer os.RemoveAll(outputDir)

	// Write multiple memory profiles
	for i := 0; i < 3; i++ {
		filename := GenerateTimestampedFilename("test", "mem")
		err := profiler.WriteMemProfile(filename)
		if err != nil {
			t.Fatalf("Failed to write memory profile %d: %v", i, err)
		}

		// Longer delay to ensure different timestamps
		time.Sleep(1 * time.Second)
	}

	// Check that all files were created
	files, err := os.ReadDir(outputDir)
	if err != nil {
		t.Fatalf("Failed to read output directory: %v", err)
	}

	if len(files) != 3 {
		t.Errorf("Expected 3 profile files, got %d", len(files))
	}
}
