package prediction

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestNewBatchProcessor(t *testing.T) {
	processor := NewBatchProcessor()
	if processor == nil {
		t.Error("NewBatchProcessor should return a non-nil processor")
	}
}

func TestBatchProcessor_ValidateBatchRequest(t *testing.T) {
	processor := NewBatchProcessor()

	tests := []struct {
		name    string
		request *BatchRequest
		wantErr bool
		errMsg  string
	}{
		{
			name:    "nil request",
			request: nil,
			wantErr: true,
			errMsg:  "batch request cannot be nil",
		},
		{
			name: "nil predictions",
			request: &BatchRequest{
				Predictions: nil,
				OutputPath:  "output.csv",
			},
			wantErr: true,
			errMsg:  "predictions cannot be nil",
		},
		{
			name: "empty output path",
			request: &BatchRequest{
				Predictions: []string{"class1", "class2"},
				OutputPath:  "",
			},
			wantErr: true,
			errMsg:  "output path is required",
		},
		{
			name: "unsupported format",
			request: &BatchRequest{
				Predictions: []string{"class1", "class2"},
				OutputPath:  "output.csv",
				Format:      "XML",
			},
			wantErr: true,
			errMsg:  "unsupported output format: XML (supported: CSV)",
		},
		{
			name: "valid request",
			request: &BatchRequest{
				Predictions: []string{"class1", "class2"},
				OutputPath:  "output.csv",
				Format:      "CSV",
			},
			wantErr: false,
		},
		{
			name: "valid request with default format",
			request: &BatchRequest{
				Predictions: []string{"class1", "class2"},
				OutputPath:  "output.csv",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := processor.ValidateBatchRequest(tt.request)
			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if tt.errMsg != "" && err.Error() != tt.errMsg {
					t.Errorf("expected error message %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestBatchProcessor_ProcessToCSV(t *testing.T) {
	processor := NewBatchProcessor()
	tempDir := t.TempDir()

	tests := []struct {
		name         string
		request      *BatchRequest
		wantErr      bool
		expectedRows int
	}{
		{
			name: "valid predictions",
			request: &BatchRequest{
				Predictions:    []string{"class1", "class2", "class1"},
				OutputPath:     filepath.Join(tempDir, "test1.csv"),
				Format:         "CSV",
				IncludeHeaders: true,
			},
			wantErr:      false,
			expectedRows: 4, // header + 3 data rows
		},
		{
			name: "empty predictions",
			request: &BatchRequest{
				Predictions:    []string{},
				OutputPath:     filepath.Join(tempDir, "test2.csv"),
				Format:         "CSV",
				IncludeHeaders: true,
			},
			wantErr:      false,
			expectedRows: 1, // header only
		},
		{
			name: "single prediction",
			request: &BatchRequest{
				Predictions:    []string{"class1"},
				OutputPath:     filepath.Join(tempDir, "test3.csv"),
				Format:         "CSV",
				IncludeHeaders: true,
			},
			wantErr:      false,
			expectedRows: 2, // header + 1 data row
		},
		{
			name: "no headers",
			request: &BatchRequest{
				Predictions:    []string{"class1", "class2"},
				OutputPath:     filepath.Join(tempDir, "test4.csv"),
				Format:         "CSV",
				IncludeHeaders: false,
			},
			wantErr:      false,
			expectedRows: 2, // data rows only
		},
		{
			name: "invalid output directory",
			request: &BatchRequest{
				Predictions:    []string{"class1"},
				OutputPath:     filepath.Join(tempDir, "nonexistent", "test.csv"),
				Format:         "CSV",
				IncludeHeaders: true,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := processor.ProcessToCSV(tt.request)
			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if result == nil {
				t.Error("expected non-nil result")
				return
			}

			// Verify file was created
			if _, err := os.Stat(tt.request.OutputPath); os.IsNotExist(err) {
				t.Error("output file was not created")
				return
			}

			// Read and verify file contents
			content, err := os.ReadFile(tt.request.OutputPath)
			if err != nil {
				t.Errorf("failed to read output file: %v", err)
				return
			}

			lines := strings.Split(strings.TrimSpace(string(content)), "\n")
			if len(lines) != tt.expectedRows {
				t.Errorf("expected %d rows, got %d", tt.expectedRows, len(lines))
			}

			// Verify header if included
			if tt.request.IncludeHeaders && len(lines) > 0 && lines[0] != "prediction" {
				t.Errorf("expected header 'prediction', got %q", lines[0])
			}

			// Verify predictions match
			startIdx := 0
			if tt.request.IncludeHeaders {
				startIdx = 1
			}
			for i, prediction := range tt.request.Predictions {
				lineIdx := i + startIdx
				if lineIdx < len(lines) && lines[lineIdx] != prediction {
					t.Errorf("expected prediction %q at row %d, got %q", prediction, lineIdx, lines[lineIdx])
				}
			}
		})
	}
}

func TestBatchProcessor_ProcessBatch(t *testing.T) {
	processor := NewBatchProcessor()
	tempDir := t.TempDir()

	tests := []struct {
		name    string
		request *BatchRequest
		wantErr bool
	}{
		{
			name: "valid batch processing",
			request: &BatchRequest{
				Predictions:    []string{"class1", "class2", "class1"},
				OutputPath:     filepath.Join(tempDir, "batch1.csv"),
				Format:         "CSV",
				IncludeHeaders: true,
			},
			wantErr: false,
		},
		{
			name: "empty predictions",
			request: &BatchRequest{
				Predictions:    []string{},
				OutputPath:     filepath.Join(tempDir, "batch2.csv"),
				Format:         "CSV",
				IncludeHeaders: true,
			},
			wantErr: false,
		},
		{
			name: "invalid request - nil predictions",
			request: &BatchRequest{
				Predictions:    nil,
				OutputPath:     filepath.Join(tempDir, "batch3.csv"),
				Format:         "CSV",
				IncludeHeaders: true,
			},
			wantErr: true,
		},
		{
			name: "invalid request - empty output path",
			request: &BatchRequest{
				Predictions:    []string{"class1"},
				OutputPath:     "",
				Format:         "CSV",
				IncludeHeaders: true,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := processor.ProcessBatch(tt.request)
			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if result == nil {
				t.Error("expected non-nil result")
				return
			}

			// Verify file was created
			if _, err := os.Stat(tt.request.OutputPath); os.IsNotExist(err) {
				t.Error("output file was not created")
				return
			}

			// Read and verify basic structure
			content, err := os.ReadFile(tt.request.OutputPath)
			if err != nil {
				t.Errorf("failed to read output file: %v", err)
				return
			}

			lines := strings.Split(strings.TrimSpace(string(content)), "\n")
			expectedRows := len(tt.request.Predictions)
			if tt.request.IncludeHeaders {
				expectedRows++ // +1 for header
			}

			if len(lines) != expectedRows {
				t.Errorf("expected %d rows, got %d", expectedRows, len(lines))
			}

			// Verify header if included
			if tt.request.IncludeHeaders && len(lines) > 0 && lines[0] != "prediction" {
				t.Errorf("expected header 'prediction', got %q", lines[0])
			}
		})
	}
}

func TestCreateBatchRequest(t *testing.T) {
	tests := []struct {
		name        string
		predictions []string
		outputPath  string
		format      string
		expected    *BatchRequest
	}{
		{
			name:        "with format specified",
			predictions: []string{"class1", "class2"},
			outputPath:  "output.csv",
			format:      "CSV",
			expected: &BatchRequest{
				Predictions:    []string{"class1", "class2"},
				OutputPath:     "output.csv",
				Format:         "CSV",
				IncludeHeaders: true,
			},
		},
		{
			name:        "with default format",
			predictions: []string{"class1"},
			outputPath:  "output.csv",
			format:      "",
			expected: &BatchRequest{
				Predictions:    []string{"class1"},
				OutputPath:     "output.csv",
				Format:         "CSV",
				IncludeHeaders: true,
			},
		},
		{
			name:        "empty predictions",
			predictions: []string{},
			outputPath:  "output.csv",
			format:      "CSV",
			expected: &BatchRequest{
				Predictions:    []string{},
				OutputPath:     "output.csv",
				Format:         "CSV",
				IncludeHeaders: true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CreateBatchRequest(tt.predictions, tt.outputPath, tt.format)

			if result.OutputPath != tt.expected.OutputPath {
				t.Errorf("expected OutputPath %q, got %q", tt.expected.OutputPath, result.OutputPath)
			}
			if result.Format != tt.expected.Format {
				t.Errorf("expected Format %q, got %q", tt.expected.Format, result.Format)
			}
			if result.IncludeHeaders != tt.expected.IncludeHeaders {
				t.Errorf("expected IncludeHeaders %v, got %v", tt.expected.IncludeHeaders, result.IncludeHeaders)
			}
			if len(result.Predictions) != len(tt.expected.Predictions) {
				t.Errorf("expected %d predictions, got %d", len(tt.expected.Predictions), len(result.Predictions))
			}
		})
	}
}

func BenchmarkBatchProcessor_ProcessToCSV(b *testing.B) {
	processor := NewBatchProcessor()
	tempDir := b.TempDir()

	// Create test predictions
	predictions := make([]string, 1000)
	for i := 0; i < 1000; i++ {
		predictions[i] = "class1"
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		outputPath := filepath.Join(tempDir, "bench.csv")
		request := &BatchRequest{
			Predictions:    predictions,
			OutputPath:     outputPath,
			Format:         "CSV",
			IncludeHeaders: true,
		}
		_, err := processor.ProcessToCSV(request)
		if err != nil {
			b.Fatalf("processing failed: %v", err)
		}
		// Clean up for next iteration
		os.Remove(outputPath)
	}
}

func BenchmarkBatchProcessor_ProcessBatch(b *testing.B) {
	processor := NewBatchProcessor()
	tempDir := b.TempDir()

	// Create test predictions
	predictions := make([]string, 1000)
	for i := 0; i < 1000; i++ {
		predictions[i] = "class1"
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		outputPath := filepath.Join(tempDir, "bench.csv")
		request := &BatchRequest{
			Predictions:    predictions,
			OutputPath:     outputPath,
			Format:         "CSV",
			IncludeHeaders: true,
		}
		_, err := processor.ProcessBatch(request)
		if err != nil {
			b.Fatalf("processing failed: %v", err)
		}
		// Clean up for next iteration
		os.Remove(outputPath)
	}
}
