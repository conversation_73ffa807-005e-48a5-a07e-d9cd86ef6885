package tree

import (
	"encoding/json"
	"fmt"
	"testing"

	"github.com/berrijam/mulberri/internal/data/features"
)

// ====================
// Feature Tests
// ====================

func TestNewFeature(t *testing.T) {
	tests := []struct {
		name        string
		featureName string
		featureType features.FeatureType
	}{
		{
			name:        "valid integer feature",
			featureName: "age",
			featureType: features.IntegerFeature,
		},
		{
			name:        "valid float feature",
			featureName: "salary",
			featureType: features.FloatFeature,
		},
		{
			name:        "valid string feature",
			featureName: "department",
			featureType: features.StringFeature,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			feature, err := NewFeature(tt.featureName, tt.featureType)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			if feature.Name != tt.featureName {
				t.<PERSON><PERSON><PERSON>("expected name %s, got %s", tt.featureName, feature.Name)
			}
			if feature.Type != tt.featureType {
				t.<PERSON>rf("expected type %v, got %v", tt.featureType, feature.Type)
			}
		})
	}
}

func TestNewFeature_ErrorCases(t *testing.T) {
	tests := []struct {
		name        string
		featureName string
		featureType features.FeatureType
	}{
		{
			name:        "empty feature name",
			featureName: "",
			featureType: features.IntegerFeature,
		},
		{
			name:        "whitespace only feature name",
			featureName: "   ",
			featureType: features.StringFeature,
		},
		{
			name:        "tab only feature name",
			featureName: "\t",
			featureType: features.FloatFeature,
		},
		{
			name:        "newline only feature name",
			featureName: "\n",
			featureType: features.IntegerFeature,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			feature, err := NewFeature(tt.featureName, tt.featureType)
			if err == nil {
				t.Error("expected error for invalid feature name, but got none")
			}
			if feature != nil {
				t.Error("expected nil feature for invalid name, but got feature")
			}
		})
	}
}

func TestFeature_IsNumerical(t *testing.T) {
	tests := []struct {
		name        string
		featureType features.FeatureType
		isNumerical bool
	}{
		{"integer feature", features.IntegerFeature, true},
		{"float feature", features.FloatFeature, true},
		{"string feature", features.StringFeature, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			feature := &Feature{Name: "test", Type: tt.featureType}
			if feature.IsNumerical() != tt.isNumerical {
				t.Errorf("expected IsNumerical() = %v, got %v", tt.isNumerical, feature.IsNumerical())
			}
		})
	}
}

func TestFeature_IsCategorical(t *testing.T) {
	tests := []struct {
		name          string
		featureType   features.FeatureType
		isCategorical bool
	}{
		{"integer feature", features.IntegerFeature, false},
		{"float feature", features.FloatFeature, false},
		{"string feature", features.StringFeature, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			feature := &Feature{Name: "test", Type: tt.featureType}
			if feature.IsCategorical() != tt.isCategorical {
				t.Errorf("expected IsCategorical() = %v, got %v", tt.isCategorical, feature.IsCategorical())
			}
		})
	}
}

// ====================
// LeafNode Tests
// ====================

func TestNewLeafNode(t *testing.T) {
	tests := []struct {
		name            string
		distribution    map[interface{}]int
		expectedPred    interface{}
		expectedConf    float64
		expectedSamples int
	}{
		{
			name:            "valid single class",
			distribution:    map[interface{}]int{"approved": 10},
			expectedPred:    "approved",
			expectedConf:    1.0,
			expectedSamples: 10,
		},
		{
			name:            "valid multiple classes",
			distribution:    map[interface{}]int{"approved": 8, "rejected": 2},
			expectedPred:    "approved",
			expectedConf:    0.8,
			expectedSamples: 10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			leaf, err := NewLeafNode(tt.distribution)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			if leaf.Prediction != tt.expectedPred {
				t.Errorf("expected prediction %v, got %v", tt.expectedPred, leaf.Prediction)
			}
			if leaf.Confidence != tt.expectedConf {
				t.Errorf("expected confidence %.2f, got %.2f", tt.expectedConf, leaf.Confidence)
			}
			if leaf.Samples != tt.expectedSamples {
				t.Errorf("expected samples %d, got %d", tt.expectedSamples, leaf.Samples)
			}
		})
	}
}

func TestNewLeafNode_ErrorCases(t *testing.T) {
	tests := []struct {
		name         string
		distribution map[interface{}]int
	}{
		{
			name:         "empty distribution",
			distribution: map[interface{}]int{},
		},
		{
			name:         "nil distribution",
			distribution: nil,
		},
		{
			name:         "negative count",
			distribution: map[interface{}]int{"approved": -5},
		},
		{
			name:         "zero count",
			distribution: map[interface{}]int{"approved": 0},
		},
		{
			name:         "mixed positive and negative",
			distribution: map[interface{}]int{"approved": 5, "rejected": -2},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			leaf, err := NewLeafNode(tt.distribution)
			if err == nil {
				t.Error("expected error for invalid distribution, but got none")
			}
			if leaf != nil {
				t.Error("expected nil leaf for invalid distribution, but got leaf")
			}
		})
	}
}

func TestLeafNode_Interface(t *testing.T) {
	distribution := map[interface{}]int{"yes": 7, "no": 3}
	leaf, err := NewLeafNode(distribution)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}

	// Test Node interface methods
	if !leaf.IsLeaf() {
		t.Error("leaf node should return true for IsLeaf()")
	}
	if leaf.GetSamples() != 10 {
		t.Errorf("expected 10 samples, got %d", leaf.GetSamples())
	}
	if leaf.GetMajorityClass() != "yes" {
		t.Errorf("expected majority class 'yes', got %v", leaf.GetMajorityClass())
	}
	if leaf.GetConfidence() != 0.7 {
		t.Errorf("expected confidence 0.7, got %.2f", leaf.GetConfidence())
	}
	if leaf.GetNodeType() != "leaf" {
		t.Errorf("expected node type 'leaf', got %s", leaf.GetNodeType())
	}

	dist := leaf.GetClassDistribution()
	if dist["yes"] != 7 || dist["no"] != 3 {
		t.Errorf("class distribution mismatch: %v", dist)
	}
}

func TestLeafNode_Validate(t *testing.T) {
	tests := []struct {
		name        string
		leaf        *LeafNode
		expectValid bool
	}{
		{
			name: "valid leaf",
			leaf: &LeafNode{
				Prediction:        "approved",
				ClassDistribution: map[interface{}]int{"approved": 5},
				Samples:           5,
				Confidence:        1.0,
			},
			expectValid: true,
		},
		{
			name: "nil prediction",
			leaf: &LeafNode{
				Prediction: nil,
				Samples:    5,
				Confidence: 1.0,
			},
			expectValid: false,
		},
		{
			name: "zero samples",
			leaf: &LeafNode{
				Prediction: "approved",
				Samples:    0,
				Confidence: 1.0,
			},
			expectValid: false,
		},
		{
			name: "invalid confidence high",
			leaf: &LeafNode{
				Prediction: "approved",
				Samples:    5,
				Confidence: 1.5,
			},
			expectValid: false,
		},
		{
			name: "invalid confidence low",
			leaf: &LeafNode{
				Prediction: "approved",
				Samples:    5,
				Confidence: -0.1,
			},
			expectValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isValid := tt.leaf.Validate()
			if tt.expectValid && !isValid {
				t.Error("expected valid node but validation failed")
			}
			if !tt.expectValid && isValid {
				t.Error("expected validation to fail but it passed")
			}
		})
	}
}

// ====================
// DecisionNode Tests
// ====================

func TestNewDecisionNode(t *testing.T) {
	feature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}
	distribution := map[interface{}]int{"yes": 6, "no": 4}

	tests := []struct {
		name         string
		feature      *Feature
		splitValue   interface{}
		distribution map[interface{}]int
	}{
		{
			name:         "valid decision node",
			feature:      feature,
			splitValue:   30.0,
			distribution: distribution,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node, err := NewDecisionNode(tt.feature, tt.splitValue, tt.distribution)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			if node.Feature != tt.feature {
				t.Errorf("feature mismatch")
			}
			if node.SplitValue != tt.splitValue {
				t.Errorf("split value mismatch")
			}
			if node.GetMajorityClass() != "yes" {
				t.Errorf("expected majority class 'yes', got %v", node.GetMajorityClass())
			}
		})
	}
}

func TestNewDecisionNode_ErrorCases(t *testing.T) {
	validFeature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}
	validDistribution := map[interface{}]int{"yes": 6, "no": 4}

	tests := []struct {
		name         string
		feature      *Feature
		splitValue   interface{}
		distribution map[interface{}]int
	}{
		{
			name:         "nil feature",
			feature:      nil,
			splitValue:   30.0,
			distribution: validDistribution,
		},
		{
			name:         "nil split value",
			feature:      validFeature,
			splitValue:   nil,
			distribution: validDistribution,
		},
		{
			name:         "empty distribution",
			feature:      validFeature,
			splitValue:   30.0,
			distribution: map[interface{}]int{},
		},
		{
			name:         "nil distribution",
			feature:      validFeature,
			splitValue:   30.0,
			distribution: nil,
		},
		{
			name:         "negative count in distribution",
			feature:      validFeature,
			splitValue:   30.0,
			distribution: map[interface{}]int{"yes": -5, "no": 4},
		},
		{
			name:         "zero count in distribution",
			feature:      validFeature,
			splitValue:   30.0,
			distribution: map[interface{}]int{"yes": 0, "no": 0},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node, err := NewDecisionNode(tt.feature, tt.splitValue, tt.distribution)
			if err == nil {
				t.Error("expected error for invalid input, but got none")
			}
			if node != nil {
				t.Error("expected nil node for invalid input, but got node")
			}
		})
	}
}

func TestDecisionNode_Interface(t *testing.T) {
	feature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}
	distribution := map[interface{}]int{"yes": 6, "no": 4}
	node, err := NewDecisionNode(feature, 30.0, distribution)
	if err != nil {
		t.Fatalf("unexpected error creating node: %v", err)
	}

	// Test Node interface methods
	if node.IsLeaf() {
		t.Error("decision node should return false for IsLeaf()")
	}
	if node.GetSamples() != 10 {
		t.Errorf("expected 10 samples, got %d", node.GetSamples())
	}
	if node.GetMajorityClass() != "yes" {
		t.Errorf("expected majority class 'yes', got %v", node.GetMajorityClass())
	}
	if node.GetConfidence() != 0.6 {
		t.Errorf("expected confidence 0.6, got %.2f", node.GetConfidence())
	}
	if node.GetNodeType() != "decision" {
		t.Errorf("expected node type 'decision', got %s", node.GetNodeType())
	}

	// Test GetClassDistribution (currently 0% coverage)
	classDist := node.GetClassDistribution()
	if classDist == nil {
		t.Error("expected non-nil class distribution")
	}
	if len(classDist) != 2 {
		t.Errorf("expected 2 classes in distribution, got %d", len(classDist))
	}
	if classDist["yes"] != 6 {
		t.Errorf("expected 6 'yes' samples, got %d", classDist["yes"])
	}
	if classDist["no"] != 4 {
		t.Errorf("expected 4 'no' samples, got %d", classDist["no"])
	}
}

func TestDecisionNode_ChildManagement(t *testing.T) {
	feature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}
	distribution := map[interface{}]int{"yes": 10}
	parent, err := NewDecisionNode(feature, 30.0, distribution)
	if err != nil {
		t.Fatalf("unexpected error creating parent: %v", err)
	}

	// Create child nodes
	leftDist := map[interface{}]int{"yes": 8, "no": 2}
	rightDist := map[interface{}]int{"yes": 2, "no": 8}
	leftChild, err := NewLeafNode(leftDist)
	if err != nil {
		t.Fatalf("unexpected error creating left child: %v", err)
	}
	rightChild, err := NewLeafNode(rightDist)
	if err != nil {
		t.Fatalf("unexpected error creating right child: %v", err)
	}

	// Test SetChild and GetChild
	parent.SetChild("custom", leftChild)

	retrieved := parent.GetChild("custom")
	if retrieved != leftChild {
		t.Error("retrieved child doesn't match set child")
	}

	// Test convenience methods for binary splits
	parent.SetLeftChild(leftChild)
	parent.SetRightChild(rightChild)

	if parent.GetLeftChild() != leftChild {
		t.Error("left child mismatch")
	}
	if parent.GetRightChild() != rightChild {
		t.Error("right child mismatch")
	}

	// Test GetAllChildren
	allChildren := parent.GetAllChildren()
	if len(allChildren) != 3 { // custom, left, right
		t.Errorf("expected 3 children, got %d", len(allChildren))
	}
}

func TestDecisionNode_SetChild_ErrorCases(t *testing.T) {
	feature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}
	distribution := map[interface{}]int{"yes": 10}
	parent, err := NewDecisionNode(feature, 30.0, distribution)
	if err != nil {
		t.Fatalf("unexpected error creating parent: %v", err)
	}

	// Test SetChild with nil child (should log error but not crash)
	parent.SetChild("nil_child", nil)
	nilChild := parent.GetChild("nil_child")
	if nilChild != nil {
		t.Error("expected nil child to not be set")
	}

	// Test GetChild with non-existent key
	nonExistent := parent.GetChild("non_existent")
	if nonExistent != nil {
		t.Error("expected nil for non-existent child")
	}

	// Test SetChild with different key types
	validChild, err := NewLeafNode(map[interface{}]int{"test": 5})
	if err != nil {
		t.Fatalf("unexpected error creating child: %v", err)
	}

	parent.SetChild(42, validChild) // integer key
	retrieved := parent.GetChild(42)
	if retrieved != validChild {
		t.Error("SetChild/GetChild with integer key failed")
	}
}

func TestDecisionNode_Validate(t *testing.T) {
	feature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}
	distribution := map[interface{}]int{"yes": 5}

	tests := []struct {
		name        string
		setupNode   func() *DecisionNode
		expectValid bool
	}{
		{
			name: "valid node with children",
			setupNode: func() *DecisionNode {
				node, _ := NewDecisionNode(feature, 30.0, distribution)
				leftChild, _ := NewLeafNode(map[interface{}]int{"yes": 3})
				node.SetLeftChild(leftChild)
				return node
			},
			expectValid: true,
		},
		{
			name: "node without children",
			setupNode: func() *DecisionNode {
				node, _ := NewDecisionNode(feature, 30.0, distribution)
				return node
			},
			expectValid: false,
		},
		{
			name: "node with invalid child",
			setupNode: func() *DecisionNode {
				node, _ := NewDecisionNode(feature, 30.0, distribution)
				// Create invalid child directly
				invalidChild := &LeafNode{
					Prediction: nil, // Invalid
					Samples:    5,
					Confidence: 1.0,
				}
				node.Children["invalid"] = invalidChild
				return node
			},
			expectValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node := tt.setupNode()
			isValid := node.Validate()

			if tt.expectValid && !isValid {
				t.Error("expected validation to pass but it failed")
			}
			if !tt.expectValid && isValid {
				t.Error("expected validation to fail but it passed")
			}
		})
	}
}

// ====================
// Serialization Tests
// ====================

func TestLeafNode_Serialization(t *testing.T) {
	distribution := map[interface{}]int{"approved": 7, "rejected": 3}
	leaf, err := NewLeafNode(distribution)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}

	// Test ToSerializable
	serializable := leaf.ToSerializable()
	if serializable.Type != "leaf" {
		t.Errorf("expected type 'leaf', got %s", serializable.Type)
	}
	if serializable.Prediction != "approved" {
		t.Errorf("expected prediction 'approved', got %v", serializable.Prediction)
	}
	if serializable.Samples != 10 {
		t.Errorf("expected 10 samples, got %d", serializable.Samples)
	}

	// Check string distribution conversion
	if serializable.ClassDistribution["approved"] != 7 {
		t.Errorf("expected 'approved': 7, got %d", serializable.ClassDistribution["approved"])
	}
	if serializable.ClassDistribution["rejected"] != 3 {
		t.Errorf("expected 'rejected': 3, got %d", serializable.ClassDistribution["rejected"])
	}
}

func TestDecisionNode_Serialization(t *testing.T) {
	feature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}
	distribution := map[interface{}]int{"yes": 6, "no": 4}
	decision, err := NewDecisionNode(feature, 30.0, distribution)
	if err != nil {
		t.Fatalf("unexpected error creating decision: %v", err)
	}

	// Add children
	leftChild, err := NewLeafNode(map[interface{}]int{"yes": 5, "no": 1})
	if err != nil {
		t.Fatalf("unexpected error creating left child: %v", err)
	}
	rightChild, err := NewLeafNode(map[interface{}]int{"yes": 1, "no": 3})
	if err != nil {
		t.Fatalf("unexpected error creating right child: %v", err)
	}
	decision.SetLeftChild(leftChild)
	decision.SetRightChild(rightChild)

	// Test ToSerializable
	serializable := decision.ToSerializable()
	if serializable.Type != "decision" {
		t.Errorf("expected type 'decision', got %s", serializable.Type)
	}
	if serializable.Feature.Name != "age" {
		t.Errorf("expected feature name 'age', got %s", serializable.Feature.Name)
	}
	if serializable.SplitValue != 30.0 {
		t.Errorf("expected split value 30.0, got %v", serializable.SplitValue)
	}
	if len(serializable.Children) != 2 {
		t.Errorf("expected 2 children, got %d", len(serializable.Children))
	}

	// Check children serialization
	if serializable.Children["left"] == nil {
		t.Error("left child not serialized")
	}
	if serializable.Children["right"] == nil {
		t.Error("right child not serialized")
	}
}

func TestSerialization_RoundTrip(t *testing.T) {
	// Create a simple tree structure
	feature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}
	distribution := map[interface{}]int{"approved": 10, "rejected": 8}
	root, err := NewDecisionNode(feature, 30.0, distribution)
	if err != nil {
		t.Fatalf("unexpected error creating root: %v", err)
	}

	leftDist := map[interface{}]int{"approved": 8, "rejected": 2}
	rightDist := map[interface{}]int{"approved": 2, "rejected": 6}
	leftChild, err := NewLeafNode(leftDist)
	if err != nil {
		t.Fatalf("unexpected error creating left child: %v", err)
	}
	rightChild, err := NewLeafNode(rightDist)
	if err != nil {
		t.Fatalf("unexpected error creating right child: %v", err)
	}

	root.SetLeftChild(leftChild)
	root.SetRightChild(rightChild)

	// Serialize to JSON
	serializable := root.ToSerializable()
	jsonData, err := json.MarshalIndent(serializable, "", "  ")
	if err != nil {
		t.Fatalf("failed to marshal JSON: %v", err)
	}

	// Deserialize from JSON
	var deserializedSerializable SerializableNode
	err = json.Unmarshal(jsonData, &deserializedSerializable)
	if err != nil {
		t.Fatalf("failed to unmarshal JSON: %v", err)
	}

	// Convert back to Node
	reconstructed, err := FromSerializable(&deserializedSerializable)
	if err != nil {
		t.Fatalf("unexpected error during deserialization: %v", err)
	}

	// Validate reconstruction
	if reconstructed.IsLeaf() {
		t.Error("root should be a decision node")
	}

	decisionNode := reconstructed.(*DecisionNode)
	if decisionNode.Feature.Name != "age" {
		t.Errorf("expected feature 'age', got %s", decisionNode.Feature.Name)
	}
	if decisionNode.SplitValue != 30.0 {
		t.Errorf("expected split value 30.0, got %v", decisionNode.SplitValue)
	}

	// Check children
	leftReconstructed := decisionNode.GetLeftChild()
	rightReconstructed := decisionNode.GetRightChild()

	if leftReconstructed == nil || rightReconstructed == nil {
		t.Error("children not properly reconstructed")
	}

	if !leftReconstructed.IsLeaf() || !rightReconstructed.IsLeaf() {
		t.Error("children should be leaf nodes")
	}

	// Validate statistics preservation
	if leftReconstructed.GetMajorityClass() != "approved" {
		t.Errorf("left child majority class mismatch")
	}
	if rightReconstructed.GetMajorityClass() != "rejected" {
		t.Errorf("right child majority class mismatch")
	}
}

// ====================
// Tree Utility Function Tests
// ====================

func TestCountNodes(t *testing.T) {
	tests := []struct {
		name          string
		setupTree     func() Node
		expectedCount int
	}{
		{
			name: "nil node",
			setupTree: func() Node {
				return nil
			},
			expectedCount: 0,
		},
		{
			name: "single leaf node",
			setupTree: func() Node {
				leaf, _ := NewLeafNode(map[interface{}]int{"yes": 5})
				return leaf
			},
			expectedCount: 1,
		},
		{
			name: "decision node with two leaf children",
			setupTree: func() Node {
				feature, _ := NewFeature("age", features.IntegerFeature)
				root, _ := NewDecisionNode(feature, 30.0, map[interface{}]int{"yes": 10})

				leftChild, _ := NewLeafNode(map[interface{}]int{"yes": 6})
				rightChild, _ := NewLeafNode(map[interface{}]int{"no": 4})

				root.SetLeftChild(leftChild)
				root.SetRightChild(rightChild)

				return root
			},
			expectedCount: 3, // root + 2 leaves
		},
		{
			name: "deeper tree",
			setupTree: func() Node {
				// Root: age <= 30
				ageFeature, _ := NewFeature("age", features.IntegerFeature)
				root, _ := NewDecisionNode(ageFeature, 30.0, map[interface{}]int{"yes": 20})

				// Left subtree: another decision node
				salaryFeature, _ := NewFeature("salary", features.FloatFeature)
				leftDecision, _ := NewDecisionNode(salaryFeature, 50000.0, map[interface{}]int{"yes": 12})
				leftLeft, _ := NewLeafNode(map[interface{}]int{"yes": 8})
				leftRight, _ := NewLeafNode(map[interface{}]int{"no": 4})
				leftDecision.SetLeftChild(leftLeft)
				leftDecision.SetRightChild(leftRight)

				// Right subtree: leaf node
				rightLeaf, _ := NewLeafNode(map[interface{}]int{"no": 8})

				root.SetLeftChild(leftDecision)
				root.SetRightChild(rightLeaf)

				return root
			},
			expectedCount: 5, // root + leftDecision + leftLeft + leftRight + rightLeaf
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tree := tt.setupTree()
			count := CountNodes(tree)
			if count != tt.expectedCount {
				t.Errorf("expected %d nodes, got %d", tt.expectedCount, count)
			}
		})
	}
}

func TestCountLeaves(t *testing.T) {
	tests := []struct {
		name          string
		setupTree     func() Node
		expectedCount int
	}{
		{
			name: "nil node",
			setupTree: func() Node {
				return nil
			},
			expectedCount: 0,
		},
		{
			name: "single leaf node",
			setupTree: func() Node {
				leaf, _ := NewLeafNode(map[interface{}]int{"yes": 5})
				return leaf
			},
			expectedCount: 1,
		},
		{
			name: "decision node with two leaf children",
			setupTree: func() Node {
				feature, _ := NewFeature("age", features.IntegerFeature)
				root, _ := NewDecisionNode(feature, 30.0, map[interface{}]int{"yes": 10})

				leftChild, _ := NewLeafNode(map[interface{}]int{"yes": 6})
				rightChild, _ := NewLeafNode(map[interface{}]int{"no": 4})

				root.SetLeftChild(leftChild)
				root.SetRightChild(rightChild)

				return root
			},
			expectedCount: 2, // 2 leaf nodes
		},
		{
			name: "deeper tree with mixed nodes",
			setupTree: func() Node {
				// Same tree as in TestCountNodes
				ageFeature, _ := NewFeature("age", features.IntegerFeature)
				root, _ := NewDecisionNode(ageFeature, 30.0, map[interface{}]int{"yes": 20})

				salaryFeature, _ := NewFeature("salary", features.FloatFeature)
				leftDecision, _ := NewDecisionNode(salaryFeature, 50000.0, map[interface{}]int{"yes": 12})
				leftLeft, _ := NewLeafNode(map[interface{}]int{"yes": 8})
				leftRight, _ := NewLeafNode(map[interface{}]int{"no": 4})
				leftDecision.SetLeftChild(leftLeft)
				leftDecision.SetRightChild(leftRight)

				rightLeaf, _ := NewLeafNode(map[interface{}]int{"no": 8})

				root.SetLeftChild(leftDecision)
				root.SetRightChild(rightLeaf)

				return root
			},
			expectedCount: 3, // leftLeft + leftRight + rightLeaf
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tree := tt.setupTree()
			count := CountLeaves(tree)
			if count != tt.expectedCount {
				t.Errorf("expected %d leaves, got %d", tt.expectedCount, count)
			}
		})
	}
}

// ====================
// FromSerializable Tests
// ====================

func TestFromSerializable(t *testing.T) {
	tests := []struct {
		name        string
		setupSerial func() *SerializableNode
		expectType  string
	}{
		{
			name: "valid leaf node",
			setupSerial: func() *SerializableNode {
				return &SerializableNode{
					Type:              "leaf",
					Prediction:        "approved",
					ClassDistribution: map[string]int{"approved": 8, "rejected": 2},
					Samples:           10,
					Confidence:        0.8,
				}
			},
			expectType: "leaf",
		},
		{
			name: "valid decision node",
			setupSerial: func() *SerializableNode {
				feature := &Feature{Name: "age", Type: features.IntegerFeature}
				return &SerializableNode{
					Type:       "decision",
					Feature:    feature,
					SplitValue: 30.0,
					Children: map[string]*SerializableNode{
						"left": {
							Type:              "leaf",
							Prediction:        "approved",
							ClassDistribution: map[string]int{"approved": 5},
							Samples:           5,
							Confidence:        1.0,
						},
					},
					ClassDistribution: map[string]int{"approved": 5, "rejected": 3},
					Samples:           8,
					Confidence:        0.625,
				}
			},
			expectType: "decision",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			serializable := tt.setupSerial()
			node, err := FromSerializable(serializable)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			if node.GetNodeType() != tt.expectType {
				t.Errorf("expected node type %s, got %s", tt.expectType, node.GetNodeType())
			}

			// Validate the reconstructed node
			if !node.Validate() {
				t.Error("reconstructed node validation failed")
			}
		})
	}
}

func TestFromSerializable_ErrorCases(t *testing.T) {
	tests := []struct {
		name         string
		serializable *SerializableNode
		expectError  bool
	}{
		{
			name:         "nil serializable node",
			serializable: nil,
			expectError:  true,
		},
		{
			name: "unknown node type",
			serializable: &SerializableNode{
				Type:              "unknown",
				ClassDistribution: map[string]int{"yes": 5},
				Samples:           5,
				Confidence:        1.0,
			},
			expectError: true,
		},
		{
			name: "leaf without prediction",
			serializable: &SerializableNode{
				Type:              "leaf",
				Prediction:        nil,
				ClassDistribution: map[string]int{"yes": 5},
				Samples:           5,
				Confidence:        1.0,
			},
			expectError: true,
		},
		{
			name: "decision without feature",
			serializable: &SerializableNode{
				Type:              "decision",
				Feature:           nil,
				SplitValue:        30.0,
				ClassDistribution: map[string]int{"yes": 5},
				Samples:           5,
				Confidence:        1.0,
			},
			expectError: true,
		},
		{
			name: "decision without split value",
			serializable: &SerializableNode{
				Type: "decision",
				Feature: &Feature{
					Name: "age",
					Type: features.IntegerFeature,
				},
				SplitValue:        nil,
				ClassDistribution: map[string]int{"yes": 5},
				Samples:           5,
				Confidence:        1.0,
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node, err := FromSerializable(tt.serializable)
			if tt.expectError {
				if err == nil {
					t.Error("expected error but got none")
				}
				if node != nil {
					t.Error("expected nil node but got node")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if node == nil {
					t.Error("expected node but got nil")
				}
			}
		})
	}
}

// ====================
// Integration Tests
// ====================

func TestCompleteTreeExample(t *testing.T) {
	// Create a complete small tree to test all components together

	// Root: age <= 30
	ageFeature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating age feature: %v", err)
	}
	root, err := NewDecisionNode(ageFeature, 30.0, map[interface{}]int{"approved": 10, "rejected": 8})
	if err != nil {
		t.Fatalf("unexpected error creating root: %v", err)
	}

	// Left child: approved (young people mostly approved)
	leftChild, err := NewLeafNode(map[interface{}]int{"approved": 8, "rejected": 2})
	if err != nil {
		t.Fatalf("unexpected error creating left child: %v", err)
	}

	// Right child: department split
	deptFeature, err := NewFeature("department", features.StringFeature)
	if err != nil {
		t.Fatalf("unexpected error creating dept feature: %v", err)
	}
	rightDecision, err := NewDecisionNode(deptFeature, "engineering", map[interface{}]int{"approved": 2, "rejected": 6})
	if err != nil {
		t.Fatalf("unexpected error creating right decision: %v", err)
	}

	// Right subtree leaves
	engLeaf, err := NewLeafNode(map[interface{}]int{"approved": 2, "rejected": 1})
	if err != nil {
		t.Fatalf("unexpected error creating eng leaf: %v", err)
	}
	nonEngLeaf, err := NewLeafNode(map[interface{}]int{"rejected": 5})
	if err != nil {
		t.Fatalf("unexpected error creating non-eng leaf: %v", err)
	}

	rightDecision.SetLeftChild(engLeaf)     // department == engineering
	rightDecision.SetRightChild(nonEngLeaf) // department != engineering

	root.SetLeftChild(leftChild)
	root.SetRightChild(rightDecision)

	// Test tree structure
	totalNodes := CountNodes(root)
	totalLeaves := CountLeaves(root)

	if totalNodes != 5 {
		t.Errorf("expected 5 total nodes, got %d", totalNodes)
	}
	if totalLeaves != 3 {
		t.Errorf("expected 3 leaf nodes, got %d", totalLeaves)
	}

	// Test serialization round trip
	serializable := root.ToSerializable()
	jsonData, err := json.MarshalIndent(serializable, "", "  ")
	if err != nil {
		t.Fatalf("JSON serialization failed: %v", err)
	}

	var deserializedSerializable SerializableNode
	err = json.Unmarshal(jsonData, &deserializedSerializable)
	if err != nil {
		t.Fatalf("JSON deserialization failed: %v", err)
	}

	reconstructed, err := FromSerializable(&deserializedSerializable)
	if err != nil {
		t.Fatalf("unexpected error during deserialization: %v", err)
	}

	// Validate reconstructed tree structure
	if reconstructed.IsLeaf() {
		t.Error("reconstructed root should be decision node")
	}

	reconstructedRoot := reconstructed.(*DecisionNode)
	if reconstructedRoot.Feature.Name != "age" {
		t.Errorf("reconstructed root feature name mismatch")
	}

	// Validate tree statistics preservation
	if CountNodes(reconstructed) != totalNodes {
		t.Error("node count changed after serialization")
	}
	if CountLeaves(reconstructed) != totalLeaves {
		t.Error("leaf count changed after serialization")
	}

	// Validate child structure preservation
	leftReconstructed := reconstructedRoot.GetLeftChild()
	rightReconstructed := reconstructedRoot.GetRightChild()

	if leftReconstructed == nil || !leftReconstructed.IsLeaf() {
		t.Error("left child structure not preserved")
	}
	if rightReconstructed == nil || rightReconstructed.IsLeaf() {
		t.Error("right child should be decision node")
	}

	// Validate deeper structure (right subtree)
	rightDecisionReconstructed := rightReconstructed.(*DecisionNode)
	if rightDecisionReconstructed.Feature.Name != "department" {
		t.Error("right subtree feature not preserved")
	}

	if rightDecisionReconstructed.GetLeftChild() == nil || rightDecisionReconstructed.GetRightChild() == nil {
		t.Error("right subtree children not preserved")
	}
}

// ====================
// Edge Case Tests
// ====================

func TestEdgeCases(t *testing.T) {
	t.Run("single sample leaf", func(t *testing.T) {
		leaf, err := NewLeafNode(map[interface{}]int{"single": 1})
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}
		if leaf.GetConfidence() != 1.0 {
			t.Errorf("single sample should have 100%% confidence, got %.2f", leaf.GetConfidence())
		}
	})

	t.Run("tied classes in distribution", func(t *testing.T) {
		// When multiple classes have same count, should select alphabetically first
		dist := map[interface{}]int{"B": 5, "A": 5} // B first in map, but A should be selected
		leaf, err := NewLeafNode(dist)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		majority := leaf.GetMajorityClass()
		if majority != "A" {
			t.Errorf("majority class should be A (alphabetically first), got %v", majority)
		}
		if leaf.GetConfidence() != 0.5 {
			t.Errorf("tied distribution should have 0.5 confidence, got %.2f", leaf.GetConfidence())
		}
	})

	t.Run("decision node without required children", func(t *testing.T) {
		feature, err := NewFeature("test", features.IntegerFeature)
		if err != nil {
			t.Fatalf("unexpected error creating feature: %v", err)
		}
		node, err := NewDecisionNode(feature, 5.0, map[interface{}]int{"yes": 10})
		if err != nil {
			t.Fatalf("unexpected error creating node: %v", err)
		}

		// Should fail validation without children
		if node.Validate() {
			t.Error("decision node without children should fail validation")
		}
	})

	t.Run("numeric vs categorical feature types", func(t *testing.T) {
		intFeature, err := NewFeature("age", features.IntegerFeature)
		if err != nil {
			t.Fatalf("unexpected error creating int feature: %v", err)
		}
		floatFeature, err := NewFeature("salary", features.FloatFeature)
		if err != nil {
			t.Fatalf("unexpected error creating float feature: %v", err)
		}
		stringFeature, err := NewFeature("dept", features.StringFeature)
		if err != nil {
			t.Fatalf("unexpected error creating string feature: %v", err)
		}

		if !intFeature.IsNumerical() || intFeature.IsCategorical() {
			t.Error("integer feature type classification failed")
		}
		if !floatFeature.IsNumerical() || floatFeature.IsCategorical() {
			t.Error("float feature type classification failed")
		}
		if stringFeature.IsNumerical() || !stringFeature.IsCategorical() {
			t.Error("string feature type classification failed")
		}
	})

	t.Run("large class distribution", func(t *testing.T) {
		// Test with many classes
		largeDist := make(map[interface{}]int)
		for i := 0; i < 100; i++ {
			largeDist[fmt.Sprintf("class_%d", i)] = i + 1 // class_99 has count 100 (majority)
		}

		leaf, err := NewLeafNode(largeDist)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		if leaf.GetMajorityClass() != "class_99" {
			t.Errorf("expected majority class_99, got %v", leaf.GetMajorityClass())
		}

		expectedSamples := (100 * 101) / 2 // sum of 1+2+...+100
		if leaf.GetSamples() != expectedSamples {
			t.Errorf("expected %d samples, got %d", expectedSamples, leaf.GetSamples())
		}
	})
}

func TestDecisionNode_SetChild_ComprehensiveEdgeCases(t *testing.T) {
	// Test SetChild with comprehensive edge cases to increase SetChild coverage
	feature, err := NewFeature("test", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}

	parent, err := NewDecisionNode(feature, 30.0, map[interface{}]int{"yes": 10})
	if err != nil {
		t.Fatalf("unexpected error creating parent: %v", err)
	}

	validChild, err := NewLeafNode(map[interface{}]int{"test": 5})
	if err != nil {
		t.Fatalf("unexpected error creating child: %v", err)
	}

	tests := []struct {
		name        string
		key         interface{}
		child       Node
		description string
	}{
		{
			name:        "struct key",
			key:         struct{ id int }{id: 1},
			child:       validChild,
			description: "should handle struct key",
		},
		{
			name:        "channel key",
			key:         make(chan int),
			child:       validChild,
			description: "should handle channel key",
		},
		{
			name:        "pointer key",
			key:         &struct{ id int }{id: 1},
			child:       validChild,
			description: "should handle pointer key",
		},
		{
			name:        "string with special characters",
			key:         "key-with_special.chars@123",
			child:       validChild,
			description: "should handle string with special characters",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// This should work without panicking
			parent.SetChild(tt.key, tt.child)

			// Verify the child was set
			retrieved := parent.GetChild(tt.key)
			if retrieved != tt.child {
				t.Errorf("SetChild/GetChild failed for %s", tt.description)
			}

			t.Logf("%s: success", tt.description)
		})
	}

	// Test setting multiple children with different key types
	parent.SetChild("left", validChild)
	parent.SetChild("right", validChild)
	parent.SetChild(42, validChild)
	parent.SetChild(3.14, validChild)

	allChildren := parent.GetAllChildren()
	if len(allChildren) < 4 {
		t.Errorf("expected at least 4 children after setting multiple, got %d", len(allChildren))
	}
}

func TestDecisionNode_Predict_NumericConversionEdgeCases(t *testing.T) {
	// Test DecisionNode.Predict with numeric conversion edge cases to increase Predict coverage
	feature, err := NewFeature("value", features.FloatFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}

	root, err := NewDecisionNode(feature, 50.0, map[interface{}]int{"high": 5, "low": 5})
	if err != nil {
		t.Fatalf("unexpected error creating root: %v", err)
	}

	leftChild, err := NewLeafNode(map[interface{}]int{"low": 8, "high": 2})
	if err != nil {
		t.Fatalf("unexpected error creating left child: %v", err)
	}
	rightChild, err := NewLeafNode(map[interface{}]int{"high": 7, "low": 1})
	if err != nil {
		t.Fatalf("unexpected error creating right child: %v", err)
	}

	root.SetLeftChild(leftChild)
	root.SetRightChild(rightChild)

	tests := []struct {
		name               string
		sample             map[string]interface{}
		expectedPrediction interface{}
		description        string
	}{
		{
			name:               "string that looks like number",
			sample:             map[string]interface{}{"value": "45.5"},
			expectedPrediction: "high", // string conversion fails, uses majority class
			description:        "should use majority class when string conversion fails",
		},
		{
			name:               "boolean value",
			sample:             map[string]interface{}{"value": true},
			expectedPrediction: "high", // should use majority class (tied, so first)
			description:        "should use majority class for boolean",
		},
		{
			name:               "slice value",
			sample:             map[string]interface{}{"value": []int{1, 2, 3}},
			expectedPrediction: "high", // should use majority class
			description:        "should use majority class for slice",
		},
		{
			name:               "map value",
			sample:             map[string]interface{}{"value": map[string]int{"test": 1}},
			expectedPrediction: "high", // should use majority class
			description:        "should use majority class for map",
		},
		{
			name:               "nil value",
			sample:             map[string]interface{}{"value": nil},
			expectedPrediction: "high", // should use majority class
			description:        "should use majority class for nil value",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prediction, confidence, err := root.Predict(tt.sample)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if prediction != tt.expectedPrediction {
				t.Errorf("expected prediction %v, got %v (%s)", tt.expectedPrediction, prediction, tt.description)
			}
			t.Logf("%s: prediction=%v, confidence=%.3f", tt.description, prediction, confidence)
		})
	}
}

func TestLeafNode_Validate_EdgeCases(t *testing.T) {
	// Test LeafNode.Validate with edge cases to increase coverage
	tests := []struct {
		name        string
		setupNode   func() *LeafNode
		expectValid bool
		description string
	}{
		{
			name: "leaf with negative confidence",
			setupNode: func() *LeafNode {
				// Create node manually to bypass constructor validation
				return &LeafNode{
					Prediction:        "yes",
					ClassDistribution: map[interface{}]int{"yes": 5},
					Samples:           5,
					Confidence:        -0.1, // Invalid: negative confidence
				}
			},
			expectValid: false,
			description: "should fail validation with negative confidence",
		},
		{
			name: "leaf with confidence > 1",
			setupNode: func() *LeafNode {
				// Create node manually to bypass constructor validation
				return &LeafNode{
					Prediction:        "yes",
					ClassDistribution: map[interface{}]int{"yes": 5},
					Samples:           5,
					Confidence:        1.5, // Invalid: confidence > 1
				}
			},
			expectValid: false,
			description: "should fail validation with confidence > 1",
		},
		{
			name: "leaf with zero samples",
			setupNode: func() *LeafNode {
				// Create node manually to bypass constructor validation
				return &LeafNode{
					Prediction:        "yes",
					ClassDistribution: map[interface{}]int{"yes": 5},
					Samples:           0, // Invalid: zero samples
					Confidence:        1.0,
				}
			},
			expectValid: false,
			description: "should fail validation with zero samples",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node := tt.setupNode()
			isValid := node.Validate()

			if tt.expectValid && !isValid {
				t.Errorf("expected node to be valid but validation failed (%s)", tt.description)
			}
			if !tt.expectValid && isValid {
				t.Errorf("expected node validation to fail but it passed (%s)", tt.description)
			}
			t.Logf("%s: validation result = %v", tt.description, isValid)
		})
	}
}

func TestFromSerializable_ComplexCases(t *testing.T) {
	// Test FromSerializable with more complex cases to increase coverage
	tests := []struct {
		name         string
		serializable *SerializableNode
		expectError  bool
		description  string
	}{
		{
			name: "decision node with invalid child",
			serializable: &SerializableNode{
				Type: "decision",
				Feature: &Feature{
					Name: "age",
					Type: features.IntegerFeature,
				},
				SplitValue:        30.0,
				ClassDistribution: map[string]int{"yes": 5, "no": 3},
				Samples:           8,
				Confidence:        0.625,
				Children: map[string]*SerializableNode{
					"left": {
						Type:              "leaf",
						Prediction:        nil, // Invalid: nil prediction
						ClassDistribution: map[string]int{"yes": 4, "no": 1},
						Samples:           5,
						Confidence:        0.8,
					},
				},
			},
			expectError: true,
			description: "should fail when child deserialization fails",
		},
		{
			name: "decision node with empty children map",
			serializable: &SerializableNode{
				Type: "decision",
				Feature: &Feature{
					Name: "age",
					Type: features.IntegerFeature,
				},
				SplitValue:        30.0,
				ClassDistribution: map[string]int{"yes": 5, "no": 3},
				Samples:           8,
				Confidence:        0.625,
				Children:          map[string]*SerializableNode{}, // Empty children
			},
			expectError: true, // This should fail because decision nodes need children
			description: "should fail with empty children map",
		},
		{
			name: "leaf node with zero confidence",
			serializable: &SerializableNode{
				Type:              "leaf",
				Prediction:        "yes",
				ClassDistribution: map[string]int{"yes": 1, "no": 0},
				Samples:           1,
				Confidence:        0.0, // Edge case: zero confidence
			},
			expectError: false,
			description: "should handle leaf node with zero confidence",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node, err := FromSerializable(tt.serializable)
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none (%s)", tt.description)
				}
				if node != nil {
					t.Errorf("expected nil node but got node (%s)", tt.description)
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v (%s)", err, tt.description)
				}
				if node == nil {
					t.Errorf("expected node but got nil (%s)", tt.description)
				}
			}
			t.Logf("%s: success", tt.description)
		})
	}
}

func TestDecisionNode_Validate_ComprehensiveCases(t *testing.T) {
	// Test DecisionNode.Validate with more comprehensive cases to increase coverage
	feature, err := NewFeature("test", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}

	tests := []struct {
		name        string
		setupNode   func() *DecisionNode
		expectValid bool
		description string
	}{
		{
			name: "node with zero samples",
			setupNode: func() *DecisionNode {
				child, _ := NewLeafNode(map[interface{}]int{"yes": 3})
				node := &DecisionNode{
					Feature:           feature,
					SplitValue:        30.0,
					ClassDistribution: map[interface{}]int{"yes": 5},
					Samples:           0, // Invalid: zero samples
					Confidence:        1.0,
					Children:          make(map[interface{}]Node),
				}
				node.Children["left"] = child
				return node
			},
			expectValid: false,
			description: "should fail validation with zero samples",
		},
		{
			name: "node with negative samples",
			setupNode: func() *DecisionNode {
				child, _ := NewLeafNode(map[interface{}]int{"yes": 3})
				node := &DecisionNode{
					Feature:           feature,
					SplitValue:        30.0,
					ClassDistribution: map[interface{}]int{"yes": 5},
					Samples:           -5, // Invalid: negative samples
					Confidence:        1.0,
					Children:          make(map[interface{}]Node),
				}
				node.Children["left"] = child
				return node
			},
			expectValid: false,
			description: "should fail validation with negative samples",
		},
		{
			name: "node with invalid child",
			setupNode: func() *DecisionNode {
				// Create an invalid child manually
				invalidChild := &LeafNode{
					Prediction:        nil, // This will cause validation to fail
					ClassDistribution: map[interface{}]int{"yes": 3},
					Samples:           3,
					Confidence:        1.0,
				}
				node := &DecisionNode{
					Feature:           feature,
					SplitValue:        30.0,
					ClassDistribution: map[interface{}]int{"yes": 5},
					Samples:           5,
					Confidence:        1.0,
					Children:          make(map[interface{}]Node),
				}
				node.Children["invalid"] = invalidChild
				return node
			},
			expectValid: false,
			description: "should fail validation with invalid child",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node := tt.setupNode()
			isValid := node.Validate()

			if tt.expectValid && !isValid {
				t.Errorf("expected node to be valid but validation failed (%s)", tt.description)
			}
			if !tt.expectValid && isValid {
				t.Errorf("expected node validation to fail but it passed (%s)", tt.description)
			}
			t.Logf("%s: validation result = %v", tt.description, isValid)
		})
	}
}

func TestDecisionNode_SetChild_AdvancedCases(t *testing.T) {
	// Test SetChild with more edge cases to increase coverage
	feature, err := NewFeature("test", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}

	parent, err := NewDecisionNode(feature, 30.0, map[interface{}]int{"yes": 10})
	if err != nil {
		t.Fatalf("unexpected error creating parent: %v", err)
	}

	validChild, err := NewLeafNode(map[interface{}]int{"test": 5})
	if err != nil {
		t.Fatalf("unexpected error creating child: %v", err)
	}

	tests := []struct {
		name        string
		key         interface{}
		child       Node
		description string
	}{
		{
			name:        "float key",
			key:         3.14,
			child:       validChild,
			description: "should handle float key",
		},
		{
			name:        "boolean key",
			key:         true,
			child:       validChild,
			description: "should handle boolean key",
		},
		{
			name:        "complex string key",
			key:         "complex/key:with-special_chars",
			child:       validChild,
			description: "should handle complex string key",
		},
		{
			name:        "nil key with valid child",
			key:         nil,
			child:       validChild,
			description: "should handle nil key",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parent.SetChild(tt.key, tt.child)

			// Verify the child was set (except for nil key case)
			if tt.key != nil {
				retrieved := parent.GetChild(tt.key)
				if retrieved != tt.child {
					t.Errorf("SetChild/GetChild failed for %s", tt.description)
				}
			}

			t.Logf("%s: success", tt.description)
		})
	}
}

func TestDecisionNode_Predict_ComplexEdgeCases(t *testing.T) {
	// Test more complex edge cases to increase Predict coverage
	feature, err := NewFeature("score", features.FloatFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}

	root, err := NewDecisionNode(feature, 75.5, map[interface{}]int{"pass": 6, "fail": 4})
	if err != nil {
		t.Fatalf("unexpected error creating root: %v", err)
	}

	// Create children with different data types
	leftChild, err := NewLeafNode(map[interface{}]int{"pass": 8, "fail": 1})
	if err != nil {
		t.Fatalf("unexpected error creating left child: %v", err)
	}
	rightChild, err := NewLeafNode(map[interface{}]int{"fail": 7, "pass": 2})
	if err != nil {
		t.Fatalf("unexpected error creating right child: %v", err)
	}

	root.SetLeftChild(leftChild)
	root.SetRightChild(rightChild)

	tests := []struct {
		name               string
		sample             map[string]interface{}
		expectedPrediction interface{}
		description        string
	}{
		{
			name:               "float value below threshold",
			sample:             map[string]interface{}{"score": 70.2},
			expectedPrediction: "pass",
			description:        "should go left with float comparison",
		},
		{
			name:               "float value above threshold",
			sample:             map[string]interface{}{"score": 80.7},
			expectedPrediction: "fail",
			description:        "should go right with float comparison",
		},
		{
			name:               "integer value converted to float",
			sample:             map[string]interface{}{"score": 70},
			expectedPrediction: "pass",
			description:        "should handle int to float conversion",
		},
		{
			name:               "string value that can't be converted",
			sample:             map[string]interface{}{"score": "invalid"},
			expectedPrediction: "pass", // should use majority class
			description:        "should use majority class for invalid numeric conversion",
		},
		{
			name:               "empty sample map",
			sample:             map[string]interface{}{},
			expectedPrediction: "pass", // should use majority class
			description:        "should use majority class for empty sample",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prediction, confidence, err := root.Predict(tt.sample)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if prediction != tt.expectedPrediction {
				t.Errorf("expected prediction %v, got %v (%s)", tt.expectedPrediction, prediction, tt.description)
			}
			t.Logf("%s: prediction=%v, confidence=%.3f", tt.description, prediction, confidence)
		})
	}
}

func TestDecisionNode_Validate_EdgeCases(t *testing.T) {
	// Test DecisionNode.Validate with edge cases to increase coverage
	feature, err := NewFeature("test", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}

	tests := []struct {
		name        string
		setupNode   func() *DecisionNode
		expectValid bool
		description string
	}{
		{
			name: "node with nil feature",
			setupNode: func() *DecisionNode {
				// Create node manually to bypass constructor validation
				return &DecisionNode{
					Feature:           nil, // This will cause validation to fail
					SplitValue:        30.0,
					ClassDistribution: map[interface{}]int{"yes": 5},
					Samples:           5,
					Confidence:        1.0,
					Children:          make(map[interface{}]Node),
				}
			},
			expectValid: false,
			description: "should fail validation with nil feature",
		},
		{
			name: "node with nil split value",
			setupNode: func() *DecisionNode {
				// Create node manually to bypass constructor validation
				return &DecisionNode{
					Feature:           feature,
					SplitValue:        nil, // This will cause validation to fail
					ClassDistribution: map[interface{}]int{"yes": 5},
					Samples:           5,
					Confidence:        1.0,
					Children:          make(map[interface{}]Node),
				}
			},
			expectValid: false,
			description: "should fail validation with nil split value",
		},
		{
			name: "node with invalid confidence",
			setupNode: func() *DecisionNode {
				// Create node manually to bypass constructor validation
				child, _ := NewLeafNode(map[interface{}]int{"yes": 3})
				node := &DecisionNode{
					Feature:           feature,
					SplitValue:        30.0,
					ClassDistribution: map[interface{}]int{"yes": 5},
					Samples:           5,
					Confidence:        1.5, // Invalid confidence > 1
					Children:          make(map[interface{}]Node),
				}
				node.Children["left"] = child
				return node
			},
			expectValid: false,
			description: "should fail validation with invalid confidence",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node := tt.setupNode()
			isValid := node.Validate()

			if tt.expectValid && !isValid {
				t.Errorf("expected node to be valid but validation failed (%s)", tt.description)
			}
			if !tt.expectValid && isValid {
				t.Errorf("expected node validation to fail but it passed (%s)", tt.description)
			}
			t.Logf("%s: validation result = %v", tt.description, isValid)
		})
	}
}

func TestFromSerializable_EdgeCases(t *testing.T) {
	// Test FromSerializable with edge cases to increase coverage
	tests := []struct {
		name         string
		serializable *SerializableNode
		expectError  bool
		description  string
	}{
		{
			name: "decision node with children",
			serializable: &SerializableNode{
				Type: "decision",
				Feature: &Feature{
					Name: "age",
					Type: features.IntegerFeature,
				},
				SplitValue:        30.0,
				ClassDistribution: map[string]int{"yes": 5, "no": 3},
				Samples:           8,
				Confidence:        0.625,
				Children: map[string]*SerializableNode{
					"left": {
						Type:              "leaf",
						Prediction:        "yes",
						ClassDistribution: map[string]int{"yes": 4, "no": 1},
						Samples:           5,
						Confidence:        0.8,
					},
					"right": {
						Type:              "leaf",
						Prediction:        "no",
						ClassDistribution: map[string]int{"yes": 1, "no": 2},
						Samples:           3,
						Confidence:        0.667,
					},
				},
			},
			expectError: false,
			description: "should successfully deserialize decision node with children",
		},
		{
			name: "leaf node with extra fields",
			serializable: &SerializableNode{
				Type:              "leaf",
				Prediction:        "approved",
				ClassDistribution: map[string]int{"approved": 7, "rejected": 3},
				Samples:           10,
				Confidence:        0.7,
				// Extra fields that should be ignored
				Feature:    nil,
				SplitValue: nil,
				Children:   nil,
			},
			expectError: false,
			description: "should successfully deserialize leaf node ignoring extra fields",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node, err := FromSerializable(tt.serializable)
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none (%s)", tt.description)
				}
				if node != nil {
					t.Errorf("expected nil node but got node (%s)", tt.description)
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v (%s)", err, tt.description)
				}
				if node == nil {
					t.Errorf("expected node but got nil (%s)", tt.description)
				} else {
					// Verify the node was properly constructed
					if node.GetSamples() != tt.serializable.Samples {
						t.Errorf("expected %d samples, got %d", tt.serializable.Samples, node.GetSamples())
					}
					if node.GetConfidence() != tt.serializable.Confidence {
						t.Errorf("expected confidence %.3f, got %.3f", tt.serializable.Confidence, node.GetConfidence())
					}
				}
			}
			t.Logf("%s: success", tt.description)
		})
	}
}

func TestDecisionNode_Predict_MissingChildren(t *testing.T) {
	// Test DecisionNode.Predict when children are missing (increase Predict coverage)
	feature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}

	root, err := NewDecisionNode(feature, 30.0, map[interface{}]int{"yes": 5, "no": 5})
	if err != nil {
		t.Fatalf("unexpected error creating root: %v", err)
	}

	// Only set left child, missing right child
	leftChild, err := NewLeafNode(map[interface{}]int{"yes": 8, "no": 2})
	if err != nil {
		t.Fatalf("unexpected error creating left child: %v", err)
	}
	root.SetLeftChild(leftChild)
	// Intentionally not setting right child

	tests := []struct {
		name               string
		sample             map[string]interface{}
		expectedPrediction interface{}
		description        string
	}{
		{
			name:               "goes left with child",
			sample:             map[string]interface{}{"age": 25},
			expectedPrediction: "yes",
			description:        "should use left child",
		},
		{
			name:               "goes right but no child",
			sample:             map[string]interface{}{"age": 35},
			expectedPrediction: "no", // should use majority class from root (tied, so first encountered)
			description:        "should use majority class when child missing",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prediction, confidence, err := root.Predict(tt.sample)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if prediction != tt.expectedPrediction {
				t.Errorf("expected prediction %v, got %v (%s)", tt.expectedPrediction, prediction, tt.description)
			}
			t.Logf("%s: prediction=%v, confidence=%.3f", tt.description, prediction, confidence)
		})
	}
}

func TestDecisionNode_GetMajorityClass_EdgeCases(t *testing.T) {
	// Test GetMajorityClass with tied classes (increase GetMajorityClass coverage)
	feature, err := NewFeature("test", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}

	tests := []struct {
		name         string
		distribution map[interface{}]int
		description  string
	}{
		{
			name:         "tied classes",
			distribution: map[interface{}]int{"A": 5, "B": 5},
			description:  "should return first encountered class in tie",
		},
		{
			name:         "three way tie",
			distribution: map[interface{}]int{"A": 3, "B": 3, "C": 3},
			description:  "should return first encountered class in three-way tie",
		},
		{
			name:         "single class",
			distribution: map[interface{}]int{"only": 10},
			description:  "should return the only class",
		},
		{
			name:         "clear majority",
			distribution: map[interface{}]int{"major": 8, "minor1": 1, "minor2": 1},
			description:  "should return clear majority class",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			node, err := NewDecisionNode(feature, 30.0, tt.distribution)
			if err != nil {
				t.Fatalf("unexpected error creating node: %v", err)
			}

			majorityClass := node.GetMajorityClass()
			if majorityClass == nil {
				t.Error("expected non-nil majority class")
			}

			// Verify the returned class is actually in the distribution
			found := false
			for class := range tt.distribution {
				if class == majorityClass {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("majority class %v not found in distribution", majorityClass)
			}

			t.Logf("%s: majority class is %v", tt.description, majorityClass)
		})
	}
}

// ====================
// Predict Tests
// ====================

func TestLeafNode_Predict(t *testing.T) {
	leaf, err := NewLeafNode(map[interface{}]int{"approved": 8, "rejected": 2})
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}

	sample := map[string]interface{}{"age": 25, "salary": 50000}
	prediction, confidence, err := leaf.Predict(sample)

	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
	if prediction != "approved" {
		t.Errorf("expected prediction 'approved', got %v", prediction)
	}
	if confidence != 0.8 {
		t.Errorf("expected confidence 0.8, got %.2f", confidence)
	}
}

func TestDecisionNode_Predict(t *testing.T) {
	// Create a simple tree: age <= 30 -> approved, age > 30 -> rejected
	feature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}
	root, err := NewDecisionNode(feature, 30.0, map[interface{}]int{"approved": 6, "rejected": 4})
	if err != nil {
		t.Fatalf("unexpected error creating root: %v", err)
	}

	// Add children
	leftChild, err := NewLeafNode(map[interface{}]int{"approved": 8, "rejected": 2})
	if err != nil {
		t.Fatalf("unexpected error creating left child: %v", err)
	}
	rightChild, err := NewLeafNode(map[interface{}]int{"rejected": 6, "approved": 1})
	if err != nil {
		t.Fatalf("unexpected error creating right child: %v", err)
	}

	root.SetLeftChild(leftChild)
	root.SetRightChild(rightChild)

	// Test prediction for young person (should go left -> approved)
	youngSample := map[string]interface{}{"age": 25}
	prediction, confidence, err := root.Predict(youngSample)
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
	if prediction != "approved" {
		t.Errorf("expected 'approved' for young person, got %v", prediction)
	}
	if confidence != 0.8 {
		t.Errorf("expected confidence 0.8, got %.2f", confidence)
	}

	// Test prediction for older person (should go right -> rejected)
	olderSample := map[string]interface{}{"age": 35}
	prediction, _, err = root.Predict(olderSample)
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
	if prediction != "rejected" {
		t.Errorf("expected 'rejected' for older person, got %v", prediction)
	}

	// Test missing feature (should use majority class)
	missingSample := map[string]interface{}{"salary": 50000}
	prediction, _, err = root.Predict(missingSample)
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
	if prediction != "approved" { // majority class at root
		t.Errorf("expected majority class 'approved' for missing feature, got %v", prediction)
	}
}

func TestDecisionNode_Predict_EdgeCases(t *testing.T) {
	// Test categorical feature with string split
	feature, err := NewFeature("category", features.StringFeature)
	if err != nil {
		t.Fatalf("unexpected error creating feature: %v", err)
	}

	root, err := NewDecisionNode(feature, "A", map[interface{}]int{"yes": 5, "no": 5})
	if err != nil {
		t.Fatalf("unexpected error creating root: %v", err)
	}

	leftChild, err := NewLeafNode(map[interface{}]int{"yes": 8, "no": 2})
	if err != nil {
		t.Fatalf("unexpected error creating left child: %v", err)
	}
	rightChild, err := NewLeafNode(map[interface{}]int{"no": 7, "yes": 1})
	if err != nil {
		t.Fatalf("unexpected error creating right child: %v", err)
	}

	// Set children using actual categorical values as keys (n-ary split)
	root.SetChild("A", leftChild)  // Category "A" goes to leftChild
	root.SetChild("B", rightChild) // Category "B" goes to rightChild

	tests := []struct {
		name               string
		sample             map[string]interface{}
		expectedPrediction interface{}
		usesMajorityClass  bool
	}{
		{
			name:               "categorical match",
			sample:             map[string]interface{}{"category": "A"},
			expectedPrediction: "yes", // goes to child "A"
			usesMajorityClass:  false,
		},
		{
			name:               "categorical match B",
			sample:             map[string]interface{}{"category": "B"},
			expectedPrediction: "no", // goes to child "B"
			usesMajorityClass:  false,
		},
		{
			name:               "categorical no child",
			sample:             map[string]interface{}{"category": "C"},
			expectedPrediction: "no", // no child for "C", uses majority class
			usesMajorityClass:  true,
		},
		{
			name:               "nil sample",
			sample:             nil,
			expectedPrediction: "no", // majority class from root (tied, so first encountered)
			usesMajorityClass:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prediction, confidence, err := root.Predict(tt.sample)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if prediction != tt.expectedPrediction {
				t.Errorf("expected prediction %v, got %v", tt.expectedPrediction, prediction)
			}
			if tt.usesMajorityClass && confidence != 0.5 {
				t.Errorf("expected majority class confidence 0.5, got %.3f", confidence)
			}
		})
	}
}

// ====================
// Performance Tests
// ====================

func BenchmarkLeafNodeCreation(b *testing.B) {
	distribution := map[interface{}]int{
		"class_A": 100,
		"class_B": 50,
		"class_C": 25,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := NewLeafNode(distribution)
		if err != nil {
			b.Fatalf("unexpected error: %v", err)
		}
	}
}

func BenchmarkDecisionNodeCreation(b *testing.B) {
	feature, err := NewFeature("test_feature", features.IntegerFeature)
	if err != nil {
		b.Fatalf("unexpected error creating feature: %v", err)
	}
	distribution := map[interface{}]int{
		"class_A": 100,
		"class_B": 50,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := NewDecisionNode(feature, 30.0, distribution)
		if err != nil {
			b.Fatalf("unexpected error: %v", err)
		}
	}
}

func BenchmarkSerialization(b *testing.B) {
	// Create a moderately complex tree
	feature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		b.Fatalf("unexpected error creating feature: %v", err)
	}
	root, err := NewDecisionNode(feature, 30.0, map[interface{}]int{"yes": 100, "no": 50})
	if err != nil {
		b.Fatalf("unexpected error creating root: %v", err)
	}

	leftChild, err := NewLeafNode(map[interface{}]int{"yes": 80, "no": 20})
	if err != nil {
		b.Fatalf("unexpected error creating left child: %v", err)
	}
	rightChild, err := NewLeafNode(map[interface{}]int{"no": 30, "yes": 20})
	if err != nil {
		b.Fatalf("unexpected error creating right child: %v", err)
	}

	root.SetLeftChild(leftChild)
	root.SetRightChild(rightChild)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		serializable := root.ToSerializable()
		_, err := json.Marshal(serializable)
		if err != nil {
			b.Fatalf("serialization failed: %v", err)
		}
	}
}

func BenchmarkDeserialization(b *testing.B) {
	// Create serialized data
	feature := &Feature{Name: "age", Type: features.IntegerFeature}
	serializable := &SerializableNode{
		Type:       "decision",
		Feature:    feature,
		SplitValue: 30.0,
		Children: map[string]*SerializableNode{
			"left": {
				Type:              "leaf",
				Prediction:        "approved",
				ClassDistribution: map[string]int{"approved": 80, "rejected": 20},
				Samples:           100,
				Confidence:        0.8,
			},
			"right": {
				Type:              "leaf",
				Prediction:        "rejected",
				ClassDistribution: map[string]int{"rejected": 30, "approved": 20},
				Samples:           50,
				Confidence:        0.6,
			},
		},
		ClassDistribution: map[string]int{"approved": 100, "rejected": 50},
		Samples:           150,
		Confidence:        0.667,
	}

	jsonData, _ := json.Marshal(serializable)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var deserializedSerializable SerializableNode
		err := json.Unmarshal(jsonData, &deserializedSerializable)
		if err != nil {
			b.Fatalf("JSON unmarshal failed: %v", err)
		}

		_, deserErr := FromSerializable(&deserializedSerializable)
		if deserErr != nil {
			b.Fatalf("unexpected error during deserialization: %v", deserErr)
		}
	}
}
