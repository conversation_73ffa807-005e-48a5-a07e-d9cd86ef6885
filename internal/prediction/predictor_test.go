package prediction

import (
	"testing"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/tree"
)

func TestNewPredictionService(t *testing.T) {
	service := NewPredictionService()
	if service == nil {
		t.Error("NewPredictionService should return a non-nil service")
	}
}

func TestPredictionService_PredictSimple(t *testing.T) {
	service := NewPredictionService()

	tests := []struct {
		name      string
		model     *tree.DecisionTree
		dataset   *dataset.Dataset[string]
		batchSize int
		wantErr   bool
		errMsg    string
	}{
		{
			name:      "nil model",
			model:     nil,
			dataset:   createTestDataset(t),
			batchSize: 1000,
			wantErr:   true,
			errMsg:    "decision tree model cannot be nil",
		},
		{
			name:      "nil dataset",
			model:     createTestDecisionTree(t),
			dataset:   nil,
			batchSize: 1000,
			wantErr:   true,
			errMsg:    "prediction dataset cannot be nil",
		},
		{
			name:      "valid prediction",
			model:     createTestDecisionTree(t),
			dataset:   createTestDataset(t),
			batchSize: 1000,
			wantErr:   false,
		},
		{
			name:      "small batch size",
			model:     createTestDecisionTree(t),
			dataset:   createTestDataset(t),
			batchSize: 1,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			predictions, err := service.PredictSimple(tt.model, tt.dataset, tt.batchSize)
			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if tt.errMsg != "" && err.Error() != tt.errMsg {
					t.Errorf("expected error message %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("expected no error but got: %v", err)
					return
				}
				if predictions == nil {
					t.Error("expected predictions but got nil")
					return
				}
				if len(predictions) == 0 {
					t.Error("expected non-empty predictions")
				}
			}
		})
	}
}

func TestPredictionService_PredictDetailed(t *testing.T) {
	service := NewPredictionService()

	tests := []struct {
		name      string
		model     *tree.DecisionTree
		dataset   *dataset.Dataset[string]
		batchSize int
		wantErr   bool
		errMsg    string
	}{
		{
			name:      "nil model",
			model:     nil,
			dataset:   createTestDataset(t),
			batchSize: 1000,
			wantErr:   true,
			errMsg:    "decision tree model cannot be nil",
		},
		{
			name:      "nil dataset",
			model:     createTestDecisionTree(t),
			dataset:   nil,
			batchSize: 1000,
			wantErr:   true,
			errMsg:    "prediction dataset cannot be nil",
		},
		{
			name:      "valid detailed prediction",
			model:     createTestDecisionTree(t),
			dataset:   createTestDataset(t),
			batchSize: 1000,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			predictions, err := service.PredictDetailed(tt.model, tt.dataset, tt.batchSize)
			if tt.wantErr {
				if err == nil {
					t.Error("expected error but got none")
					return
				}
				if tt.errMsg != "" && err.Error() != tt.errMsg {
					t.Errorf("expected error message %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("expected no error but got: %v", err)
					return
				}
				if predictions == nil {
					t.Error("expected predictions but got nil")
					return
				}
				if len(predictions) == 0 {
					t.Error("expected non-empty predictions")
					return
				}
				// Check that detailed predictions have required fields
				for i, pred := range predictions {
					if pred.Prediction == "" {
						t.Errorf("prediction %d has empty prediction", i)
					}
					if pred.Confidence < 0 || pred.Confidence > 1 {
						t.Errorf("prediction %d has invalid confidence: %f", i, pred.Confidence)
					}
					if len(pred.DecisionPath) == 0 {
						t.Errorf("prediction %d has empty decision path", i)
					}
					if pred.DecisionRule == "" {
						t.Errorf("prediction %d has empty decision rule", i)
					}
				}
			}
		})
	}
}

// Helper functions for creating test data
func createTestDataset(t *testing.T) *dataset.Dataset[string] {
	// Create a simple test dataset
	ds := dataset.NewDataset[string](100)

	// Add some test columns
	ages := []int64{25, 35, 45}
	ageNulls := []bool{false, false, false}
	ds.AddIntColumn("age", ages, ageNulls)

	incomes := []float64{30000.0, 60000.0, 80000.0}
	incomeNulls := []bool{false, false, false}
	ds.AddFloatColumn("income", incomes, incomeNulls)

	educations := []string{"high_school", "college", "graduate"}
	educationNulls := []bool{false, false, false}
	ds.AddStringColumn("education", educations, educationNulls)

	// Add target values
	ds.AddTarget("yes")
	ds.AddTarget("no")
	ds.AddTarget("yes")

	return ds
}

func createTestDecisionTree(t *testing.T) *tree.DecisionTree {
	// Create a simple test decision tree
	root, err := tree.NewLeafNode(map[interface{}]int{"yes": 10, "no": 0})
	if err != nil {
		t.Fatalf("failed to create leaf node: %v", err)
	}

	// Create features
	ageFeature, err := tree.NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatalf("failed to create age feature: %v", err)
	}
	incomeFeature, err := tree.NewFeature("income", features.FloatFeature)
	if err != nil {
		t.Fatalf("failed to create income feature: %v", err)
	}
	educationFeature, err := tree.NewFeature("education", features.StringFeature)
	if err != nil {
		t.Fatalf("failed to create education feature: %v", err)
	}

	featureList := []*tree.Feature{ageFeature, incomeFeature, educationFeature}
	classes := []string{"yes", "no"}
	metadata := tree.NewTreeMetadata("C4.5", 10, 2, "entropy", 3, "target")

	return tree.NewDecisionTree(root, featureList, classes, metadata)
}

func BenchmarkPredictionService_PredictSimple(b *testing.B) {
	service := NewPredictionService()
	model := createTestDecisionTree(&testing.T{})
	dataset := createTestDataset(&testing.T{})

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.PredictSimple(model, dataset, 1000)
		if err != nil {
			b.Fatalf("prediction failed: %v", err)
		}
	}
}

func BenchmarkPredictionService_PredictDetailed(b *testing.B) {
	service := NewPredictionService()
	model := createTestDecisionTree(&testing.T{})
	dataset := createTestDataset(&testing.T{})

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.PredictDetailed(model, dataset, 1000)
		if err != nil {
			b.Fatalf("prediction failed: %v", err)
		}
	}
}
