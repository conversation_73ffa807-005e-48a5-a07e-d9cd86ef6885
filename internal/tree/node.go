// Package tree provides node structures for Mulberri decision tree implementation.
//
// Architecture:
//   - node.go: Node interface and implementations (LeafNode, DecisionNode)
//   - SerializableNode: JSON serialization support
//   - Feature: Decision split metadata
//
// Design Principles:
//   - Nodes are stateless containers for decision logic
//   - Unified interface for polymorphic tree operations
//   - Binary splits for both numerical and categorical features
//   - Type-safe serialization with JSON round-trip support
//
// Memory Management:
//   - Nodes store only decision metadata, not training data
//   - Children stored in map for flexible access patterns
//   - Class distributions use interface{} for target type flexibility
//
// Example:
//
//	// Create leaf node from statistics
//	dist := map[interface{}]int{"approved": 15, "rejected": 5}
//	leaf := NewLeafNode(dist)
//
//	// Create decision node with split
//	feature := NewFeature("age", features.IntegerFeature)
//	decision := NewDecisionNode(feature, 30.0, dist)
//	decision.SetLeftChild(leftChild)
//	decision.SetRightChild(rightChild)
//
// Security: No sensitive data storage, validates node consistency.
// Performance: O(1) node operations, O(log depth) tree traversal.
// Dependencies: features package for FeatureType, logger for error handling.
package tree

import (
	"encoding/json"
	"fmt"
	"slices"
	"strings"

	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/utils/errors"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// ====================
// Constants for Binary Splits
// ====================

// Branch value constants for unified binary split representation.
const (
	LeftBranch  = "lte" // For <= threshold (numerical) or == value (categorical)
	RightBranch = "gt"  // For > threshold (numerical) or != value (categorical)
)

// ====================
// Core Interfaces and Types
// ====================

// Node represents any node in a decision tree with unified operations.
//
// All node types implement this interface for polymorphic tree operations.
// Provides statistical information, type checking, and serialization support.
//
// Constraints:
//   - ClassDistribution must have at least one entry for valid nodes
//   - Samples must be > 0 for meaningful statistics
//   - Confidence must be in range [0.0, 1.0]
//
// Relationships: Implemented by LeafNode and DecisionNode.
// Side effects: ToSerializable() creates new SerializableNode instances.
type Node interface {
	// IsLeaf returns true for terminal nodes, false for decision nodes
	IsLeaf() bool

	// GetSamples returns total number of training samples that reached this node
	GetSamples() int

	// GetClassDistribution returns map of target values to their occurrence counts
	GetClassDistribution() map[interface{}]int

	// GetMajorityClass returns most frequent target value at this node
	GetMajorityClass() interface{}

	// GetConfidence returns prediction confidence as ratio [0.0, 1.0]
	// Confidence = (majority class count) / (total samples at this node)
	// Measures prediction certainty, not dataset coverage
	GetConfidence() float64

	// Validate checks node consistency and returns validation status
	Validate() bool

	// GetNodeType returns string identifier for serialization ("leaf" or "decision")
	GetNodeType() string

	// ToSerializable converts node to JSON-compatible format
	ToSerializable() *SerializableNode

	// Predict returns prediction and confidence for given sample
	Predict(sample map[string]interface{}) (interface{}, float64, error)
}

// Feature represents decision tree feature metadata for split decisions.
//
// Stores feature identification and type information needed for split evaluation
// and prediction traversal. Links node decisions to dataset feature columns.
//
// Constraints:
//   - Name must match column name in training dataset
//   - Type must be valid FeatureType (IntegerFeature/FloatFeature/StringFeature)
//
// Relationships: Used by DecisionNode for split decisions.
// Side effects: None, immutable after creation.
type Feature struct {
	Name string               `json:"name"` // Feature column name from dataset
	Type features.FeatureType `json:"type"` // Internal feature type classification
}

// MarshalJSON implements custom JSON marshaling for Feature.
// Serializes the Type field as a human-readable string instead of numeric enum.
func (f *Feature) MarshalJSON() ([]byte, error) {
	return json.Marshal(struct {
		Name string `json:"name"`
		Type string `json:"type"`
	}{
		Name: f.Name,
		Type: f.Type.String(),
	})
}

// UnmarshalJSON implements custom JSON unmarshaling for Feature.
// Parses the Type field from human-readable string back to FeatureType enum.
func (f *Feature) UnmarshalJSON(data []byte) error {
	var temp struct {
		Name string `json:"name"`
		Type string `json:"type"`
	}

	if err := json.Unmarshal(data, &temp); err != nil {
		return err
	}

	f.Name = temp.Name
	f.Type = features.ParseFeatureType(temp.Type)
	return nil
}

// NewFeature creates a new Feature with validation.
//
// Args:
//   - name: Feature column name (must be non-empty after trimming)
//   - featureType: Type classification for split strategy selection
//
// Returns new Feature instance and error for invalid parameters.
// Constraints: name cannot be empty or whitespace-only.
// Performance: O(1) creation with basic validation.
// Relationships: Created during decision node construction.
// Side effects: None, returns error for invalid input.
//
// Example: feature, err := NewFeature("age", features.IntegerFeature)
func NewFeature(name string, featureType features.FeatureType) (*Feature, error) {
	if len(strings.TrimSpace(name)) == 0 {
		return nil, errors.LogErrorAndReturnSimple("feature name cannot be empty")
	}

	return &Feature{
		Name: name,
		Type: featureType,
	}, nil
}

// IsNumerical returns true if feature supports threshold-based splits.
//
// Returns true for IntegerFeature and FloatFeature, false otherwise.
// Performance: O(1) type classification check.
// Relationships: Used by split evaluation and prediction traversal.
// Side effects: None, read-only type checking.
func (f *Feature) IsNumerical() bool {
	return f.Type == features.IntegerFeature || f.Type == features.FloatFeature
}

// IsCategorical returns true if feature uses value-based splits.
//
// Returns true for StringFeature, false otherwise.
// Performance: O(1) type classification check.
// Relationships: Used by split evaluation and prediction traversal.
// Side effects: None, read-only type checking.
func (f *Feature) IsCategorical() bool {
	return f.Type == features.StringFeature
}

// ====================
// LeafNode Implementation
// ====================

// LeafNode represents terminal decision tree node with final predictions.
//
// Stores statistical summary of training samples that reached this node
// and provides prediction with confidence measure. Created when tree building
// stops due to purity, minimum samples, or maximum depth constraints.
//
// Constraints:
//   - Prediction must be non-nil and match majority class
//   - ClassDistribution must have at least one entry
//   - Samples must be > 0 for valid statistics
//   - Confidence must be in range [0.0, 1.0] representing prediction purity at this node
//
// Security: Contains training data statistics but no raw samples.
// Performance: O(1) access to prediction and statistics.
// Relationships: Implements Node interface, created from DatasetView analysis.
// Side effects: Stores immutable prediction state after construction.
type LeafNode struct {
	Prediction        interface{}         // Most common target value (majority class)
	ClassDistribution map[interface{}]int // Target value counts from training samples
	Samples           int                 // Total samples that reached this node
	Confidence        float64             // Prediction confidence [0.0, 1.0]: majority_class_count / total_samples_at_node
}

// NewLeafNode creates leaf node with automatic majority class and confidence calculation.
//
// Args:
//   - classDistribution: Map of target values to occurrence counts (must be non-empty)
//
// Returns new LeafNode with calculated prediction and confidence, or error if invalid.
// Constraints: classDistribution must have positive counts and at least one entry.
// Performance: O(k) where k = number of unique target classes.
// Relationships: Created during tree building when stopping criteria met.
// Side effects: None, returns error for invalid distribution.
//
// Example: leaf, err := NewLeafNode(map[interface{}]int{"approved": 8, "rejected": 2})
func NewLeafNode(classDistribution map[interface{}]int) (*LeafNode, error) {
	if len(classDistribution) == 0 {
		return nil, errors.LogErrorAndReturnSimple("leaf node requires non-empty class distribution")
	}

	// Calculate total samples and find majority class with consistent ordering
	totalSamples := 0
	var majorityClass interface{}
	maxCount := 0
	var tiedClasses []interface{}

	for class, count := range classDistribution {
		if count < 0 {
			return nil, errors.LogErrorAndReturnSimple(fmt.Sprintf("class count cannot be negative: %d", count))
		}
		totalSamples += count
		if count > maxCount {
			maxCount = count
			majorityClass = class
			tiedClasses = []interface{}{class}
		} else if count == maxCount {
			tiedClasses = append(tiedClasses, class)
		}
	}

	// Handle ties by selecting alphabetically first class
	if len(tiedClasses) > 1 {
		logger.Debug(fmt.Sprintf("found multiple classes with same count %d, selecting first alphabetically from: %v", maxCount, tiedClasses))
		// Sort tied classes alphabetically and select first
		var sortedClasses []string
		for _, class := range tiedClasses {
			sortedClasses = append(sortedClasses, fmt.Sprintf("%v", class))
		}
		slices.Sort(sortedClasses)
		majorityClass = sortedClasses[0]
	}

	if totalSamples == 0 {
		return nil, errors.LogErrorAndReturnSimple("leaf node cannot have zero samples")
	}

	// Calculate prediction confidence: ratio of majority class to total samples at this node
	// This measures prediction certainty (purity), not coverage of full dataset
	confidence := float64(maxCount) / float64(totalSamples)

	return &LeafNode{
		Prediction:        majorityClass,
		ClassDistribution: classDistribution,
		Samples:           totalSamples,
		Confidence:        confidence,
	}, nil
}

// Node interface implementation for LeafNode

// IsLeaf returns true as this is a terminal node.
func (l *LeafNode) IsLeaf() bool { return true }

// GetSamples returns number of training samples that reached this leaf.
func (l *LeafNode) GetSamples() int { return l.Samples }

// GetClassDistribution returns target class counts for this leaf.
func (l *LeafNode) GetClassDistribution() map[interface{}]int { return l.ClassDistribution }

// GetMajorityClass returns most frequent target value (prediction).
func (l *LeafNode) GetMajorityClass() interface{} { return l.Prediction }

// GetConfidence returns prediction confidence as ratio of majority to total samples at this node.
// Measures how pure/certain the prediction is, not what percentage of full dataset reaches here.
func (l *LeafNode) GetConfidence() float64 { return l.Confidence }

// GetNodeType returns "leaf" for serialization type identification.
func (l *LeafNode) GetNodeType() string { return "leaf" }

// Predict returns the leaf's prediction and confidence.
//
// Args:
//   - sample: Input sample (ignored for leaf nodes)
//
// Returns prediction, confidence, and nil error.
// Performance: O(1) - simply returns stored values.
// Side effects: None, read-only operation.
func (l *LeafNode) Predict(sample map[string]interface{}) (interface{}, float64, error) {
	return l.Prediction, l.Confidence, nil
}

// Validate checks node consistency and logs errors for invalid states.
//
// Validates prediction exists, positive sample count, and confidence in valid range.
// Performance: O(1) validation checks.
// Relationships: Called before serialization or tree validation.
// Side effects: Logs errors for invalid states, returns true for valid nodes.
func (l *LeafNode) Validate() bool {
	if l.Prediction == nil {
		logger.Error("leaf node must have a prediction")
		return false
	}
	if l.Samples <= 0 {
		errors.LogErrorAndReturnSimple(fmt.Sprintf("leaf node must have positive sample count, got: %d", l.Samples))
		return false
	}
	if l.Confidence < 0 || l.Confidence > 1 {
		errors.LogErrorAndReturnSimple(fmt.Sprintf("confidence must be between 0 and 1, got: %.3f", l.Confidence))
		return false
	}
	return true
}

// ToSerializable converts LeafNode to JSON-compatible SerializableNode format.
//
// Returns SerializableNode with string-keyed class distribution for JSON marshaling.
// Performance: O(k) where k = number of unique classes for distribution conversion.
// Relationships: Used by JSON serialization pipeline.
// Side effects: Creates new SerializableNode instance with converted distribution.
func (l *LeafNode) ToSerializable() *SerializableNode {
	// Convert interface{} keys to strings for JSON compatibility
	stringDist := make(map[string]int)
	for class, count := range l.ClassDistribution {
		stringDist[fmt.Sprintf("%v", class)] = count
	}

	return &SerializableNode{
		Type:              "leaf",
		Prediction:        l.Prediction,
		ClassDistribution: stringDist,
		Samples:           l.Samples,
		Confidence:        l.Confidence,
	}
}

// ====================
// DecisionNode Implementation
// ====================

// DecisionNode represents internal decision tree node with split logic and child references.
//
// Stores feature split information and maintains map of child nodes for different
// split outcomes. Handles both numerical (threshold-based) and categorical
// (value-based) splits using unified binary split approach.
//
// Constraints:
//   - Feature must be non-nil with valid name and type
//   - SplitValue must be appropriate for feature type (float64 for numerical, string for categorical)
//   - Children map must have at least one child for valid tree structure
//   - All child nodes must be valid Node implementations
//
// Security: Contains split logic but no raw training data.
// Performance: O(1) child access via map lookup, O(c) validation where c = child count.
// Relationships: Implements Node interface, references Feature and child Node instances.
// Side effects: Maintains child node references, calculates statistics from distribution.
type DecisionNode struct {
	Feature           *Feature             // Feature used for split decision
	SplitValue        interface{}          // Threshold (numerical) or category value (categorical)
	Children          map[interface{}]Node // Child nodes indexed by split outcome
	ClassDistribution map[interface{}]int  // Target distribution at this decision point
	Samples           int                  // Total samples that reached this node
	Confidence        float64              // Majority class confidence at this node: majority_count / total_samples_at_node
}

// NewDecisionNode creates decision node with feature split and statistical metadata.
//
// Args:
//   - feature: Feature definition for split (must have valid name and type)
//   - splitValue: Threshold for numerical or category for categorical splits
//   - classDistribution: Target class counts for samples reaching this node
//
// Returns new DecisionNode with calculated statistics, or error if invalid.
// Constraints: feature non-nil, classDistribution non-empty with positive counts.
// Performance: O(k) where k = number of unique classes for majority calculation.
// Relationships: Created during tree building with split information and metadata.
// Side effects: None, returns error for invalid inputs.
//
// Example: node, err := NewDecisionNode(ageFeature, 30.0, targetDistribution)
func NewDecisionNode(feature *Feature, splitValue interface{}, classDistribution map[interface{}]int) (*DecisionNode, error) {
	if feature == nil {
		return nil, errors.LogErrorAndReturnSimple("decision node requires a feature")
	}
	if splitValue == nil {
		return nil, errors.LogErrorAndReturnSimple("decision node requires a split value")
	}
	if len(classDistribution) == 0 {
		return nil, errors.LogErrorAndReturnSimple("decision node requires non-empty class distribution")
	}

	// Calculate statistics from class distribution
	totalSamples := 0
	maxCount := 0

	for _, count := range classDistribution {
		if count < 0 {
			return nil, errors.LogErrorAndReturnSimple(fmt.Sprintf("class count cannot be negative: %d", count))
		}
		totalSamples += count
		if count > maxCount {
			maxCount = count
		}
	}

	if totalSamples == 0 {
		return nil, errors.LogErrorAndReturnSimple("decision node cannot have zero samples")
	}

	// Calculate prediction confidence: ratio of majority class to total samples at this node
	// This measures prediction certainty (purity), not coverage of full dataset
	confidence := float64(maxCount) / float64(totalSamples)

	return &DecisionNode{
		Feature:           feature,
		SplitValue:        splitValue,
		Children:          make(map[interface{}]Node),
		ClassDistribution: classDistribution,
		Samples:           totalSamples,
		Confidence:        confidence,
	}, nil
}

// Node interface implementation for DecisionNode

// IsLeaf returns false as this is an internal decision node.
func (d *DecisionNode) IsLeaf() bool { return false }

// GetSamples returns number of training samples that reached this decision node.
func (d *DecisionNode) GetSamples() int { return d.Samples }

// GetClassDistribution returns target class counts at this decision point.
func (d *DecisionNode) GetClassDistribution() map[interface{}]int { return d.ClassDistribution }

// GetMajorityClass returns most frequent target value at this decision node.
//
// Recalculates from current class distribution to handle dynamic changes.
// Used as fallback prediction when tree traversal cannot continue.
// Performance: O(k log k) where k = number of unique classes (due to sorting for ties).
// Relationships: Used for fallback predictions when split navigation fails.
// Side effects: None, calculates from current distribution each call.
func (d *DecisionNode) GetMajorityClass() interface{} {
	maxCount := 0
	var majorityClass interface{}
	var tiedClasses []interface{}

	for class, count := range d.ClassDistribution {
		if count > maxCount {
			maxCount = count
			majorityClass = class
			tiedClasses = []interface{}{class}
		} else if count == maxCount {
			tiedClasses = append(tiedClasses, class)
		}
	}

	// Handle ties by selecting alphabetically first class
	if len(tiedClasses) > 1 {
		logger.Debug(fmt.Sprintf("found multiple classes with same count %d, selecting first alphabetically from: %v", maxCount, tiedClasses))
		// Sort tied classes alphabetically and select first
		var sortedClasses []string
		for _, class := range tiedClasses {
			sortedClasses = append(sortedClasses, fmt.Sprintf("%v", class))
		}
		slices.Sort(sortedClasses)
		majorityClass = sortedClasses[0]
	}

	return majorityClass
}

// GetConfidence returns majority class confidence at this decision node.
// Measures prediction purity: majority_class_count / total_samples_at_node.
func (d *DecisionNode) GetConfidence() float64 { return d.Confidence }

// GetNodeType returns "decision" for serialization type identification.
func (d *DecisionNode) GetNodeType() string { return "decision" }

// Predict traverses the tree to make a prediction for the given sample.
//
// Args:
//   - sample: Input sample with feature values
//
// Returns prediction, confidence, and error if traversal fails.
// Performance: O(log depth) for tree traversal.
// Side effects: None, read-only tree traversal.
func (d *DecisionNode) Predict(sample map[string]interface{}) (interface{}, float64, error) {
	// Get feature value from sample
	featureValue, exists := sample[d.Feature.Name]
	if !exists {
		logger.Debug(fmt.Sprintf("feature %s not found in sample, using majority class", d.Feature.Name))
		return d.GetMajorityClass(), d.GetConfidence(), nil
	}

	// Determine which child to traverse to
	var branchKey interface{}
	if d.Feature.IsNumerical() {
		// Numerical split: compare with threshold
		var numValue float64
		switch v := featureValue.(type) {
		case int:
			numValue = float64(v)
		case int64:
			numValue = float64(v)
		case float64:
			numValue = v
		case float32:
			numValue = float64(v)
		default:
			errors.LogErrorAndReturnSimple(fmt.Sprintf("invalid numerical value type for feature %s: %T", d.Feature.Name, featureValue))
			return d.GetMajorityClass(), d.GetConfidence(), nil
		}

		threshold, ok := d.SplitValue.(float64)
		if !ok {
			errors.LogErrorAndReturnSimple(fmt.Sprintf("invalid threshold type for numerical feature %s: %T", d.Feature.Name, d.SplitValue))
			return d.GetMajorityClass(), d.GetConfidence(), nil
		}

		if numValue <= threshold {
			branchKey = LeftBranch
		} else {
			branchKey = RightBranch
		}
	} else {
		// Categorical split: n-ary split using actual feature value as branch key
		branchKey = featureValue
	}

	// Get child node and recurse
	child := d.GetChild(branchKey)
	if child == nil {
		logger.Debug(fmt.Sprintf("no child found for branch %v, using majority class", branchKey))
		return d.GetMajorityClass(), d.GetConfidence(), nil
	}

	// Recurse to child
	return child.Predict(sample)
}

// Validate checks decision node consistency including feature, children, and metadata.
//
// Validates feature exists, at least one child present, and recursively validates children.
// Performance: O(c * validation_cost) where c = number of children.
// Relationships: Calls Validate() on all child nodes recursively.
// Side effects: Logs errors for invalid states, returns validation status.
func (d *DecisionNode) Validate() bool {
	if d.Feature == nil {
		logger.Error("decision node must have a feature")
		return false
	}
	if len(d.Children) == 0 {
		logger.Error("decision node must have at least one child")
		return false
	}
	if d.Samples <= 0 {
		errors.LogErrorAndReturnSimple(fmt.Sprintf("decision node must have positive sample count, got: %d", d.Samples))
		return false
	}
	if d.Confidence < 0 || d.Confidence > 1 {
		errors.LogErrorAndReturnSimple(fmt.Sprintf("confidence must be between 0 and 1, got: %.3f", d.Confidence))
		return false
	}

	// Recursively validate all children
	for childKey, child := range d.Children {
		if child == nil {
			errors.LogErrorAndReturnSimple(fmt.Sprintf("child node cannot be nil for key: %v", childKey))
			return false
		}
		if !child.Validate() {
			errors.LogErrorAndReturnSimple(fmt.Sprintf("child validation failed for key %v", childKey))
			return false
		}
	}

	return true
}

// ToSerializable converts DecisionNode to JSON-compatible SerializableNode format.
//
// Returns SerializableNode with string-keyed distributions and recursively serialized children.
// Performance: O(k + c * child_serialization) where k = classes, c = children.
// Relationships: Recursively calls ToSerializable() on all child nodes.
// Side effects: Creates new SerializableNode tree structure for entire subtree.
func (d *DecisionNode) ToSerializable() *SerializableNode {
	// Convert interface{} keys to strings for JSON compatibility
	stringDist := make(map[string]int)
	for class, count := range d.ClassDistribution {
		stringDist[fmt.Sprintf("%v", class)] = count
	}

	// Convert children to serializable format recursively
	serializedChildren := make(map[string]*SerializableNode)
	for branchValue, child := range d.Children {
		key := fmt.Sprintf("%v", branchValue)
		serializedChildren[key] = child.ToSerializable()
	}

	return &SerializableNode{
		Type:              "decision",
		Feature:           d.Feature,
		SplitValue:        d.SplitValue,
		Children:          serializedChildren,
		ClassDistribution: stringDist,
		Samples:           d.Samples,
		Confidence:        d.Confidence,
	}
}

// ====================
// Child Node Management for DecisionNode
// ====================

// SetChild sets child node for specified branch value with validation.
//
// Args:
//   - branchValue: Split outcome identifier (LeftBranch/RightBranch or category value)
//   - child: Child node to set (must be valid Node implementation)
//
// Constraints: child cannot be nil and must pass validation.
// Performance: O(1) map assignment plus O(validation) for child validation.
// Relationships: Links decision node to child nodes in tree structure.
// Side effects: Logs errors for invalid children, modifies children map on success.
//
// Example: node.SetChild(LeftBranch, leftChild)
func (d *DecisionNode) SetChild(branchValue interface{}, child Node) {
	if child == nil {
		logger.Error("cannot set nil child")
		return
	}
	if !child.Validate() {
		logger.Error("child validation failed")
		return
	}

	d.Children[branchValue] = child
}

// GetChild retrieves child node by branch value.
//
// Args:
//   - branchValue: Split outcome identifier to lookup
//
// Returns child Node or nil if branch value not found.
// Performance: O(1) map lookup.
// Relationships: Used during tree traversal and prediction.
// Side effects: None, read-only map access.
//
// Example: child := node.GetChild(LeftBranch)
func (d *DecisionNode) GetChild(branchValue interface{}) Node {
	return d.Children[branchValue]
}

// GetAllChildren returns slice of all child nodes for iteration.
//
// Returns slice containing all child nodes (order not guaranteed).
// Performance: O(c) where c = number of children to create slice.
// Relationships: Used for tree statistics and validation operations.
// Side effects: Creates new slice, original children map unchanged.
func (d *DecisionNode) GetAllChildren() []Node {
	children := make([]Node, 0, len(d.Children))
	for _, child := range d.Children {
		children = append(children, child)
	}
	return children
}

// Convenience methods for binary numerical splits

// SetLeftChild sets left child for binary splits (values <= threshold).
//
// Args:
//   - child: Node for samples satisfying <= condition
//
// Constraints: child must be valid Node implementation.
// Performance: O(1) plus child validation cost.
// Relationships: Convenience wrapper for SetChild(LeftBranch, child).
// Side effects: Logs errors for invalid children, updates children map on success.
func (d *DecisionNode) SetLeftChild(child Node) {
	d.SetChild(LeftBranch, child)
}

// SetRightChild sets right child for binary splits (values > threshold).
//
// Args:
//   - child: Node for samples satisfying > condition
//
// Constraints: child must be valid Node implementation.
// Performance: O(1) plus child validation cost.
// Relationships: Convenience wrapper for SetChild(RightBranch, child).
// Side effects: Logs errors for invalid children, updates children map on success.
func (d *DecisionNode) SetRightChild(child Node) {
	d.SetChild(RightBranch, child)
}

// GetLeftChild retrieves left child for binary splits.
//
// Returns Node for <= condition or nil if not set.
// Performance: O(1) map lookup.
// Relationships: Convenience wrapper for GetChild(LeftBranch).
// Side effects: None, read-only access.
func (d *DecisionNode) GetLeftChild() Node {
	return d.GetChild(LeftBranch)
}

// GetRightChild retrieves right child for binary splits.
//
// Returns Node for > condition or nil if not set.
// Performance: O(1) map lookup.
// Relationships: Convenience wrapper for GetChild(RightBranch).
// Side effects: None, read-only access.
func (d *DecisionNode) GetRightChild() Node {
	return d.GetChild(RightBranch)
}

// ====================
// Serialization Support
// ====================

// SerializableNode provides JSON-compatible representation of tree nodes.
//
// Handles conversion between typed Node interfaces and JSON-serializable format.
// Uses string keys for JSON compatibility while preserving tree structure and metadata.
//
// Constraints:
//   - Type must be "leaf" or "decision" for proper deserialization
//   - Feature required for decision nodes, Prediction required for leaf nodes
//   - ClassDistribution uses string keys for JSON marshaling compatibility
//
// Security: Contains tree structure and statistics but no raw training data.
// Performance: Memory usage scales with tree size and class cardinality.
// Relationships: Intermediate format between Node interfaces and JSON storage.
// Side effects: Immutable after creation, used only for serialization pipeline.
type SerializableNode struct {
	Type string `json:"type"` // Node type: "leaf" or "decision"

	// Leaf node fields
	Prediction interface{} `json:"prediction,omitempty"`

	// Decision node fields
	Feature    *Feature                     `json:"feature,omitempty"`
	SplitValue interface{}                  `json:"split_value,omitempty"`
	Children   map[string]*SerializableNode `json:"children,omitempty"`

	// Common fields for all node types
	ClassDistribution map[string]int `json:"class_distribution"`
	Samples           int            `json:"samples"`
	Confidence        float64        `json:"confidence"`
}

// ====================
// Deserialization Support
// ====================

// FromSerializable converts SerializableNode back to Node interface implementation.
//
// Args:
//   - s: SerializableNode from JSON unmarshaling (must be non-nil with valid type)
//
// Returns Node interface (LeafNode or DecisionNode) for valid input, or error if invalid.
// Constraints: s.Type must be "leaf" or "decision", required fields must be present.
// Performance: O(tree_size) for recursive child conversion.
// Relationships: Recursively converts entire subtree to Node implementations.
// Side effects: None, returns error for invalid serialization data.
//
// Example: node, err := FromSerializable(serializedNode)
func FromSerializable(s *SerializableNode) (Node, error) {
	if s == nil {
		logger.Error("serializable node cannot be nil")
		return nil, fmt.Errorf("serializable node cannot be nil")
	}

	// Convert string keys back to interface{} for class distribution
	classDist := make(map[interface{}]int)
	for class, count := range s.ClassDistribution {
		classDist[class] = count
	}

	switch s.Type {
	case "leaf":
		if s.Prediction == nil {
			logger.Error("leaf node must have prediction")
			return nil, fmt.Errorf("leaf node must have prediction")
		}

		leaf := &LeafNode{
			Prediction:        s.Prediction,
			ClassDistribution: classDist,
			Samples:           s.Samples,
			Confidence:        s.Confidence,
		}

		// Validate the reconstructed leaf node
		if !leaf.Validate() {
			logger.Error("invalid leaf node during deserialization")
			return nil, fmt.Errorf("invalid leaf node during deserialization")
		}

		return leaf, nil

	case "decision":
		if s.Feature == nil {
			logger.Error("decision node must have feature")
			return nil, fmt.Errorf("decision node must have feature")
		}
		if s.SplitValue == nil {
			logger.Error("decision node must have split value")
			return nil, fmt.Errorf("decision node must have split value")
		}

		// Recreate children recursively
		children := make(map[interface{}]Node)
		for branchKey, childSerializable := range s.Children {
			child, err := FromSerializable(childSerializable)
			if err != nil {
				return nil, fmt.Errorf("failed to deserialize child %v: %w", branchKey, err)
			}
			children[branchKey] = child
		}

		decision := &DecisionNode{
			Feature:           s.Feature,
			SplitValue:        s.SplitValue,
			Children:          children,
			ClassDistribution: classDist,
			Samples:           s.Samples,
			Confidence:        s.Confidence,
		}

		// Validate the reconstructed decision node
		if !decision.Validate() {
			logger.Error("invalid decision node during deserialization")
			return nil, fmt.Errorf("invalid decision node during deserialization")
		}

		return decision, nil

	default:
		return nil, errors.LogErrorAndReturnSimple(fmt.Sprintf("unknown node type: %s", s.Type))
	}
}

// ====================
// Tree Utility Functions
// ====================

// CountNodes counts total nodes in tree rooted at given node.
//
// Args:
//   - root: Root node of tree or subtree (can be leaf or decision node)
//
// Returns total count of nodes in tree including root.
// Performance: O(n) where n = total nodes in tree.
// Relationships: Used for tree statistics and metadata calculation.
// Side effects: None, read-only tree traversal.
//
// Example: totalNodes := CountNodes(treeRoot)
func CountNodes(root Node) int {
	if root == nil {
		return 0
	}
	if root.IsLeaf() {
		return 1
	}

	// Use type switch for safe node type handling
	switch n := root.(type) {
	case *DecisionNode:
		count := 1 // Count this decision node
		for _, child := range n.Children {
			count += CountNodes(child)
		}
		return count
	default:
		// Unknown node type that's not a leaf - count as 1
		return 1
	}
}

// CountLeaves counts leaf nodes in tree rooted at given node.
//
// Args:
//   - root: Root node of tree or subtree (can be leaf or decision node)
//
// Returns count of leaf nodes in tree.
// Performance: O(n) where n = total nodes in tree.
// Relationships: Used for tree statistics and complexity measurement.
// Side effects: None, read-only tree traversal.
//
// Example: leafCount := CountLeaves(treeRoot)
func CountLeaves(root Node) int {
	if root == nil {
		return 0
	}
	if root.IsLeaf() {
		return 1
	}

	count := 0
	decisionNode := root.(*DecisionNode)
	for _, child := range decisionNode.Children {
		count += CountLeaves(child)
	}
	return count
}
