#!/bin/bash

# Mulberri Profile Analysis and Report Generator
# This script analyzes pprof files and generates comprehensive reports

set -e

PROFILES_DIR="./profiles"
REPORT_DIR="./profile_reports"
TIMESTAMP=$(date +"%Y%m%d-%H%M%S")

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Severity levels
CRITICAL_SERIOUS="🔴 CRITICAL & SERIOUS"
CRITICAL_NOT_SERIOUS="🟡 CRITICAL BUT NOT SERIOUS"
OKAY="✅ OKAY"

echo "=== Mulberri Profile Analysis Report Generator ==="
echo "Timestamp: $(date)"
echo

# Create report directory
mkdir -p "$REPORT_DIR"
REPORT_FILE="$REPORT_DIR/analysis_report_$TIMESTAMP.md"

# Function to analyze CPU profile
analyze_cpu_profile() {
    local profile_file="$1"
    local operation="$2"
    
    echo "## CPU Profile Analysis: $operation" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    # Get basic stats
    local duration=$(go tool pprof -text "$profile_file" | grep "Duration:" | awk '{print $2}')
    local total_samples=$(go tool pprof -text "$profile_file" | grep "Total samples" | awk '{print $4}')
    
    echo "- **Duration**: $duration" >> "$REPORT_FILE"
    echo "- **Total Samples**: $total_samples" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    # Get top functions
    echo "### Top CPU Consumers" >> "$REPORT_FILE"
    echo '```' >> "$REPORT_FILE"
    go tool pprof -text "$profile_file" | head -20 >> "$REPORT_FILE"
    echo '```' >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    # Analysis
    echo "### Analysis" >> "$REPORT_FILE"
    
    # Check for high flat% functions
    local high_flat=$(go tool pprof -text "$profile_file" | awk 'NR>6 && $2 ~ /[0-9]+\.[0-9]+%/ && $2+0 > 20 {print $0}')
    if [ -n "$high_flat" ]; then
        echo "**$CRITICAL_SERIOUS**: Functions consuming >20% CPU time:" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "$high_flat" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "**Action Required**: Optimize these hot paths immediately." >> "$REPORT_FILE"
        echo >> "$REPORT_FILE"
    fi
    
    # Check for JSON/serialization overhead
    local json_overhead=$(go tool pprof -text "$profile_file" | grep -E "(json|marshal|encode)" | head -5)
    if [ -n "$json_overhead" ]; then
        echo "**$CRITICAL_NOT_SERIOUS**: JSON/Serialization overhead detected:" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "$json_overhead" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "**Recommendation**: Consider binary serialization for large models or frequent operations." >> "$REPORT_FILE"
        echo >> "$REPORT_FILE"
    fi
    
    # Check for logging overhead
    local logging_overhead=$(go tool pprof -text "$profile_file" | grep -E "(logger|log|zap)" | head -3)
    if [ -n "$logging_overhead" ]; then
        echo "**$CRITICAL_NOT_SERIOUS**: Logging overhead detected:" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "$logging_overhead" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "**Recommendation**: Reduce log verbosity in production or use async logging." >> "$REPORT_FILE"
        echo >> "$REPORT_FILE"
    fi
    
    # Check for core algorithm performance
    local algo_funcs=$(go tool pprof -text "$profile_file" | grep -E "(BuildTree|buildNode|findBestSplit|training)" | head -5)
    if [ -n "$algo_funcs" ]; then
        echo "**$OKAY**: Core algorithm functions detected:" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "$algo_funcs" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "**Status**: Normal algorithm execution. Monitor for performance regressions." >> "$REPORT_FILE"
        echo >> "$REPORT_FILE"
    fi
}

# Function to analyze memory profile
analyze_memory_profile() {
    local profile_file="$1"
    local operation="$2"
    
    echo "## Memory Profile Analysis: $operation" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    # Get top allocators
    echo "### Top Memory Allocators" >> "$REPORT_FILE"
    echo '```' >> "$REPORT_FILE"
    go tool pprof -text "$profile_file" | head -20 >> "$REPORT_FILE"
    echo '```' >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    # Analysis
    echo "### Analysis" >> "$REPORT_FILE"
    
    # Check for large single allocations
    local large_allocs=$(go tool pprof -text "$profile_file" | awk 'NR>4 && $1 ~ /[0-9]+\.[0-9]+MB/ {print $0}')
    if [ -n "$large_allocs" ]; then
        echo "**$CRITICAL_SERIOUS**: Large memory allocations detected (>1MB):" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "$large_allocs" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "**Action Required**: Investigate memory usage patterns and implement streaming/chunking." >> "$REPORT_FILE"
        echo >> "$REPORT_FILE"
    fi
    
    # Check for profiling overhead
    local profiling_overhead=$(go tool pprof -text "$profile_file" | grep -E "(pprof|StartCPUProfile)" | head -3)
    if [ -n "$profiling_overhead" ]; then
        echo "**$OKAY**: Profiling overhead detected:" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "$profiling_overhead" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "**Status**: Expected overhead from profiling. Disable in production." >> "$REPORT_FILE"
        echo >> "$REPORT_FILE"
    fi
    
    # Check for sync pool usage
    local pool_usage=$(go tool pprof -text "$profile_file" | grep -E "(sync.*Pool|pool)" | head -3)
    if [ -n "$pool_usage" ]; then
        echo "**$CRITICAL_NOT_SERIOUS**: Memory pool allocations:" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "$pool_usage" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "**Recommendation**: Monitor pool efficiency and consider tuning pool sizes." >> "$REPORT_FILE"
        echo >> "$REPORT_FILE"
    fi
    
    # Check for logging memory usage
    local log_memory=$(go tool pprof -text "$profile_file" | grep -E "(logger|log|zap)" | head -3)
    if [ -n "$log_memory" ]; then
        echo "**$CRITICAL_NOT_SERIOUS**: Logging memory usage:" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "$log_memory" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "**Recommendation**: Use structured logging with buffer pools and consider log sampling." >> "$REPORT_FILE"
        echo >> "$REPORT_FILE"
    fi
}

# Function to generate recommendations
generate_recommendations() {
    echo "## Recommendations & Action Items" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    echo "### Immediate Actions (Critical & Serious)" >> "$REPORT_FILE"
    echo "1. **Profile with larger datasets** to identify real bottlenecks" >> "$REPORT_FILE"
    echo "2. **Implement benchmarks** for core algorithms with various dataset sizes" >> "$REPORT_FILE"
    echo "3. **Monitor memory growth** with large datasets (>10k samples)" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    echo "### Medium-term Improvements (Critical but Not Serious)" >> "$REPORT_FILE"
    echo "1. **Optimize JSON serialization**: Consider binary formats for large models" >> "$REPORT_FILE"
    echo "2. **Reduce logging overhead**: Implement log levels and async logging" >> "$REPORT_FILE"
    echo "3. **Memory pool tuning**: Optimize buffer pool sizes based on usage patterns" >> "$REPORT_FILE"
    echo "4. **Implement streaming**: For large dataset processing to reduce memory footprint" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    echo "### Long-term Optimizations" >> "$REPORT_FILE"
    echo "1. **Algorithm optimization**: Profile with real-world datasets" >> "$REPORT_FILE"
    echo "2. **Parallel processing**: Implement concurrent tree building for large datasets" >> "$REPORT_FILE"
    echo "3. **Memory-mapped files**: For very large datasets that don't fit in memory" >> "$REPORT_FILE"
    echo "4. **Caching strategies**: Implement intelligent caching for repeated operations" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
}

# Function to generate performance baseline
generate_baseline() {
    echo "## Performance Baseline" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    echo "Based on current profiling with small dataset (30 samples):" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    echo "| Metric | Training | Prediction |" >> "$REPORT_FILE"
    echo "|--------|----------|------------|" >> "$REPORT_FILE"
    
    # Extract metrics from profiles
    local train_duration=$(go tool pprof -text profiles/train-cpu-*.prof 2>/dev/null | grep "Duration:" | awk '{print $2}' | head -1)
    local predict_duration=$(go tool pprof -text profiles/predict-cpu-*.prof 2>/dev/null | grep "Duration:" | awk '{print $2}' | head -1)
    
    echo "| Duration | ${train_duration:-N/A} | ${predict_duration:-N/A} |" >> "$REPORT_FILE"
    echo "| Memory Usage | ~3.3MB | ~1MB |" >> "$REPORT_FILE"
    echo "| Dataset Size | 30 samples | 15 samples |" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    echo "**Note**: These metrics are for a small test dataset. Performance characteristics will change significantly with larger datasets." >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
}

# Main analysis function
main() {
    if [ ! -d "$PROFILES_DIR" ]; then
        echo "❌ Profiles directory not found: $PROFILES_DIR"
        echo "Run profiling first with: ./scripts/profile_example.sh"
        exit 1
    fi
    
    # Initialize report
    echo "# Mulberri Performance Analysis Report" > "$REPORT_FILE"
    echo "Generated: $(date)" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    echo "## Executive Summary" >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    echo "This report analyzes CPU and memory profiles from Mulberri decision tree operations." >> "$REPORT_FILE"
    echo "Findings are categorized by severity and actionability." >> "$REPORT_FILE"
    echo >> "$REPORT_FILE"
    
    # Analyze each profile type
    echo "🔍 Analyzing CPU profiles..."
    for cpu_profile in "$PROFILES_DIR"/train-cpu-*.prof; do
        if [ -f "$cpu_profile" ]; then
            analyze_cpu_profile "$cpu_profile" "Training"
        fi
    done
    
    for cpu_profile in "$PROFILES_DIR"/predict-cpu-*.prof; do
        if [ -f "$cpu_profile" ]; then
            analyze_cpu_profile "$cpu_profile" "Prediction"
        fi
    done
    
    echo "🔍 Analyzing memory profiles..."
    for mem_profile in "$PROFILES_DIR"/train-mem-*.prof; do
        if [ -f "$mem_profile" ]; then
            analyze_memory_profile "$mem_profile" "Training"
        fi
    done
    
    for mem_profile in "$PROFILES_DIR"/predict-mem-*.prof; do
        if [ -f "$mem_profile" ]; then
            analyze_memory_profile "$mem_profile" "Prediction"
        fi
    done
    
    # Generate additional sections
    generate_baseline
    generate_recommendations
    
    echo "✅ Analysis complete!"
    echo "📄 Report saved to: $REPORT_FILE"
    echo
    echo "📊 Quick summary:"
    echo -e "${GREEN}$OKAY${NC}: Normal operation, monitoring recommended"
    echo -e "${YELLOW}$CRITICAL_NOT_SERIOUS${NC}: Optimization opportunities identified"
    echo -e "${RED}$CRITICAL_SERIOUS${NC}: Immediate attention required"
    echo
    echo "🔗 View full report: cat $REPORT_FILE"
}

# Run main function
main "$@"
