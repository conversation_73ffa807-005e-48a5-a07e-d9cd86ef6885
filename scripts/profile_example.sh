#!/bin/bash

# Mu<PERSON>berri Profiling Example Script
# This script demonstrates how to use the profiling features in Mulberri

set -e

echo "=== Mulberri Profiling Example ==="
echo

# Build the project
echo "1. Building Mulberri..."
go build -o bin/mulberri ./cmd/mulberri
echo "✓ Build completed"
echo

# Clean up any existing profiles
echo "2. Cleaning up existing profiles..."
rm -rf profiles/
echo "✓ Profiles directory cleaned"
echo

# Train with profiling enabled
echo "3. Training with CPU and memory profiling..."
./bin/mulberri train \
    -i examples/loan_approval.csv \
    -t approved \
    -o example_model.dt \
    -f examples/loan_approval_features.yaml \
    --cpu-profile \
    --mem-profile \
    --verbose
echo "✓ Training completed with profiling"
echo

# Predict with profiling enabled
echo "4. Predicting with CPU and memory profiling..."
./bin/mulberri predict \
    -i examples/loan_approval_predict.csv \
    -m example_model.dt \
    -o example_predictions.csv \
    --cpu-profile \
    --mem-profile \
    --verbose
echo "✓ Prediction completed with profiling"
echo

# Show generated profile files
echo "5. Generated profile files:"
ls -la profiles/
echo

# Analyze CPU profile for training
echo "6. Analyzing training CPU profile..."
echo "Top functions by CPU usage:"
go tool pprof -top profiles/train-cpu-*.prof | head -15
echo

# Analyze memory profile for training
echo "7. Analyzing training memory profile..."
echo "Top functions by memory allocation:"
go tool pprof -top profiles/train-mem-*.prof | head -15
echo

# Show available pprof commands
echo "8. Available pprof analysis commands:"
echo "   Interactive analysis:"
echo "     go tool pprof profiles/train-cpu-*.prof"
echo "   Web interface (requires graphviz):"
echo "     go tool pprof -http=:8080 profiles/train-cpu-*.prof"
echo "   Generate PNG graph:"
echo "     go tool pprof -png profiles/train-cpu-*.prof > cpu-profile.png"
echo "   Compare profiles:"
echo "     go tool pprof -base=old.prof new.prof"
echo

echo "=== Profiling Example Complete ==="
echo "Profile files are available in the ./profiles/ directory"
echo "Use 'go tool pprof' commands to analyze them further"
