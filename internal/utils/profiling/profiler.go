// Package profiling provides file-based CPU and memory profiling utilities for Mulberri.
//
// This package enables performance profiling by writing profile data to files
// that can be analyzed later using go tool pprof. Supports both CPU and memory
// profiling with configurable output directories and automatic cleanup.
//
// Example usage:
//
//	profiler := profiling.NewProfiler("./profiles")
//	if err := profiler.StartCPUProfile("train-cpu.prof"); err != nil {
//	    log.Fatal(err)
//	}
//	defer profiler.StopCPUProfile()
//
//	// Your application logic here
//
//	if err := profiler.WriteMemProfile("train-mem.prof"); err != nil {
//	    log.Error(err)
//	}
package profiling

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"runtime/pprof"
	"time"

	"github.com/berrijam/mulberri/internal/utils/logger"
)

// Profiler manages file-based CPU and memory profiling.
//
// Provides methods to start/stop CPU profiling and capture memory profiles
// with automatic file management and error handling.
type Profiler struct {
	outputDir   string
	cpuFile     *os.File
	cpuProfiling bool
}

// NewProfiler creates a new profiler instance with the specified output directory.
//
// Args:
//   - outputDir: Directory where profile files will be written
//
// Returns:
//   - *Profiler: Configured profiler instance
//
// The output directory will be created if it doesn't exist.
func NewProfiler(outputDir string) *Profiler {
	return &Profiler{
		outputDir:    outputDir,
		cpuProfiling: false,
	}
}

// ensureOutputDir creates the output directory if it doesn't exist.
//
// Returns:
//   - error: Any error encountered during directory creation
func (p *Profiler) ensureOutputDir() error {
	if err := os.MkdirAll(p.outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create profile output directory %s: %w", p.outputDir, err)
	}
	return nil
}

// StartCPUProfile begins CPU profiling and writes data to the specified file.
//
// Args:
//   - filename: Name of the CPU profile file (will be created in outputDir)
//
// Returns:
//   - error: Any error encountered during profile setup
//
// Must call StopCPUProfile() to properly close the profile file.
// Only one CPU profile can be active at a time.
func (p *Profiler) StartCPUProfile(filename string) error {
	if p.cpuProfiling {
		return fmt.Errorf("CPU profiling already active")
	}

	if err := p.ensureOutputDir(); err != nil {
		return err
	}

	filepath := filepath.Join(p.outputDir, filename)
	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("failed to create CPU profile file %s: %w", filepath, err)
	}

	if err := pprof.StartCPUProfile(file); err != nil {
		file.Close()
		return fmt.Errorf("failed to start CPU profiling: %w", err)
	}

	p.cpuFile = file
	p.cpuProfiling = true

	logger.Info(fmt.Sprintf("CPU profiling started, writing to %s", filepath))
	return nil
}

// StopCPUProfile stops CPU profiling and closes the profile file.
//
// Should be called in a defer statement after StartCPUProfile().
// Safe to call multiple times or when no profiling is active.
func (p *Profiler) StopCPUProfile() {
	if !p.cpuProfiling {
		return
	}

	pprof.StopCPUProfile()
	if p.cpuFile != nil {
		p.cpuFile.Close()
		logger.Info(fmt.Sprintf("CPU profiling stopped, data written to %s", p.cpuFile.Name()))
		p.cpuFile = nil
	}
	p.cpuProfiling = false
}

// WriteMemProfile captures a memory profile and writes it to the specified file.
//
// Args:
//   - filename: Name of the memory profile file (will be created in outputDir)
//
// Returns:
//   - error: Any error encountered during profile capture
//
// Forces garbage collection before capturing the profile for more accurate results.
// Can be called multiple times during program execution.
func (p *Profiler) WriteMemProfile(filename string) error {
	if err := p.ensureOutputDir(); err != nil {
		return err
	}

	filepath := filepath.Join(p.outputDir, filename)
	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("failed to create memory profile file %s: %w", filepath, err)
	}
	defer file.Close()

	// Force garbage collection for more accurate memory profiling
	runtime.GC()

	if err := pprof.WriteHeapProfile(file); err != nil {
		return fmt.Errorf("failed to write memory profile: %w", err)
	}

	logger.Info(fmt.Sprintf("Memory profile written to %s", filepath))
	return nil
}

// WriteGoroutineProfile captures a goroutine profile and writes it to the specified file.
//
// Args:
//   - filename: Name of the goroutine profile file (will be created in outputDir)
//
// Returns:
//   - error: Any error encountered during profile capture
//
// Useful for debugging goroutine leaks and concurrency issues.
func (p *Profiler) WriteGoroutineProfile(filename string) error {
	if err := p.ensureOutputDir(); err != nil {
		return err
	}

	filepath := filepath.Join(p.outputDir, filename)
	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("failed to create goroutine profile file %s: %w", filepath, err)
	}
	defer file.Close()

	profile := pprof.Lookup("goroutine")
	if profile == nil {
		return fmt.Errorf("goroutine profile not available")
	}

	if err := profile.WriteTo(file, 0); err != nil {
		return fmt.Errorf("failed to write goroutine profile: %w", err)
	}

	logger.Info(fmt.Sprintf("Goroutine profile written to %s", filepath))
	return nil
}

// GenerateTimestampedFilename creates a filename with timestamp for unique profile files.
//
// Args:
//   - operation: Operation name (e.g., "train", "predict")
//   - profileType: Type of profile (e.g., "cpu", "mem", "goroutine")
//
// Returns:
//   - string: Timestamped filename
//
// Example: "train-cpu-20240917-143052.prof"
func GenerateTimestampedFilename(operation, profileType string) string {
	timestamp := time.Now().Format("20060102-150405")
	return fmt.Sprintf("%s-%s-%s.prof", operation, profileType, timestamp)
}

// GetProfileOutputDir returns the default profile output directory.
//
// Returns:
//   - string: Default directory path for profile files
func GetProfileOutputDir() string {
	return "./profiles"
}
