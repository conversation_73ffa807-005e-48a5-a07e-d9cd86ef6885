{"features": [{"name": "age", "type": "integer"}, {"name": "education", "type": "string"}, {"name": "income", "type": "integer"}], "classes": ["no", "yes"], "metadata": {"version": "binary-only", "created_at": "2025-09-11T05:28:47.769488477+03:00", "algorithm": "C4.5", "max_depth": 10, "min_samples": 2, "criterion": "entropy", "total_nodes": 13, "leaf_nodes": 7, "training_samples": 30, "target_column": "approved"}, "root": {"type": "decision", "feature": {"name": "income", "type": "float"}, "split_value": 54000, "children": {"left": {"type": "decision", "feature": {"name": "education", "type": "string"}, "split_value": "college", "children": {"left": {"type": "decision", "feature": {"name": "age", "type": "float"}, "split_value": 32, "children": {"left": {"type": "decision", "feature": {"name": "income", "type": "float"}, "split_value": 49000, "children": {"left": {"type": "decision", "feature": {"name": "income", "type": "float"}, "split_value": 41500, "children": {"left": {"type": "decision", "feature": {"name": "age", "type": "float"}, "split_value": 27, "children": {"left": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 2}, "samples": 2, "confidence": 1}, "right": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"no": 2, "yes": 1}, "samples": 3, "confidence": 0.6666666666666666}, "right": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 3}, "samples": 3, "confidence": 1}}, "class_distribution": {"no": 5, "yes": 1}, "samples": 6, "confidence": 0.8333333333333334}, "right": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 1}, "samples": 1, "confidence": 1}}, "class_distribution": {"no": 5, "yes": 2}, "samples": 7, "confidence": 0.7142857142857143}, "right": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 3}, "samples": 3, "confidence": 1}}, "class_distribution": {"no": 5, "yes": 5}, "samples": 10, "confidence": 0.5}, "right": {"type": "leaf", "prediction": "no", "class_distribution": {"no": 9}, "samples": 9, "confidence": 1}}, "class_distribution": {"no": 14, "yes": 5}, "samples": 19, "confidence": 0.7368421052631579}, "right": {"type": "leaf", "prediction": "yes", "class_distribution": {"yes": 11}, "samples": 11, "confidence": 1}}, "class_distribution": {"no": 14, "yes": 16}, "samples": 30, "confidence": 0.5333333333333333}}