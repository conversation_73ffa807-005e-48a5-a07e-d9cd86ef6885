package cli

import (
	"log"
	"strings"
)

// TrainingConfig holds configuration specific to training operations.
//
// Contains all parameters needed for training decision trees including
// file paths, algorithm parameters, and feature specifications.
// Initialized with defaults from config package.
type TrainingConfig struct {
	// File paths
	InputFile       string // CSV file with training data
	TargetCol       string // Name of target column
	OutputFile      string // Path to save trained model
	FeatureInfoFile string // Feature metadata YAML file

	// Algorithm parameters
	MaxDepth        int    // Maximum tree depth
	MinSamplesSplit int    // Minimum samples to split node
	Criterion       string // Splitting criterion (entropy only)
	// Options
	Verbose bool // Enable detailed output
}

// PredictionConfig holds configuration specific to prediction operations.
//
// Contains parameters needed for making predictions with trained models
// including input data, model file, and output specifications.
// Consolidated from internal/config/prediction.go to eliminate redundancy.
type PredictionConfig struct {
	// File paths
	InputFile  string // CSV file with data to predict
	ModelFile  string // Trained model file (.dt format)
	OutputFile string // Path to save predictions

	// Output configuration
	IncludeHeaders bool // Include column headers in output (default: true)

	// Processing options
	BatchSize int  // Batch size for processing large datasets (default: 1000)
	Verbose   bool // Enable detailed output and logging (default: false)

	// Output options
	DetailedOutput bool // Enable detailed prediction output with decision paths and reasoning (default: false)

}

// NewPredictionConfig creates a new prediction configuration with default values.
//
// This constructor initializes a prediction configuration with sensible defaults
// that can be overridden based on user input or specific requirements.
//
// Returns:
//   - *PredictionConfig: Configuration instance with default values set
//
// Default values follow the requirements specification and best practices
// for prediction operations in the Mulberri decision tree system.
func NewPredictionConfig() *PredictionConfig {
	return &PredictionConfig{
		InputFile:  "",
		ModelFile:  "",
		OutputFile: "",

		IncludeHeaders: true,

		// Processing defaults
		BatchSize: 1000,
		Verbose:   false,

		// Detailed output defaults
		DetailedOutput: false,
	}
}

// Validate performs comprehensive validation for training configuration.
//
// Checks file existence, parameter ranges, and required fields.
// Side effects: Exits on first validation error encountered for faster feedback.
func (c *TrainingConfig) Validate() {
	validateInputFile(c.InputFile)

	if strings.TrimSpace(c.TargetCol) == "" {
		log.Fatal("target column is required")
	}

	if strings.TrimSpace(c.OutputFile) == "" {
		log.Fatal("output file is required")
	}

	if c.MaxDepth <= 0 {
		log.Fatalf("max depth must be positive: %d", c.MaxDepth)
	}

	if c.MinSamplesSplit < 2 {
		log.Fatalf("min samples split must be >= 2: %d", c.MinSamplesSplit)
	}

	criterion := strings.TrimSpace(c.Criterion)
	if criterion != "entropy" {
		log.Fatalf("only entropy criterion supported, got: %s", c.Criterion)
	}

	// Validate required feature info file
	if strings.TrimSpace(c.FeatureInfoFile) == "" {
		log.Fatal("feature info file is required")
	}
	validateYAMLFile(c.FeatureInfoFile)
}

// Validate performs comprehensive validation for prediction configuration.
//
// Checks file existence and required parameters for prediction workflow.
// Uses existing CLI validation helpers for file validation and adds
// configuration consistency checks.
// Side effects: Exits on first validation error encountered for faster feedback.
func (c *PredictionConfig) Validate() {
	validateInputFile(c.InputFile)

	if strings.TrimSpace(c.ModelFile) == "" {
		log.Fatal("model file is required")
	}

	validateModelFile(c.ModelFile)

	if strings.TrimSpace(c.OutputFile) == "" {
		log.Fatal("output file is required")
	}

}
