// Package csv provides CSV writing functionality for prediction results.
// This module handles writing prediction data to CSV files with proper
// formatting and error handling using interface-based design for DRY compliance.
//
// Architecture:
// - CSVRecord interface: Unified record representation
// - CSVWriteConfig: Configuration for write operations
// - Unified writeCSV function: Single implementation for all record types
// - Type-specific record implementations: SimpleRecord, DetailedRecord
package csv

import (
	"encoding/csv"
	"fmt"
	"os"
	"strings"

	"github.com/berrijam/mulberri/internal/types"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// CSVWriteConfig holds configuration for CSV writing operations.
//
// Args:
//   - OutputPath: Path where the CSV file should be created
//   - IncludeHeaders: Whether to include column headers in output
//   - RecordType: Type of records being written ("simple" or "detailed") for logging
//
// Performance: Configuration struct, O(1) access
// Side effects: None
type CSVWriteConfig struct {
	OutputPath     string // Path where the CSV file should be created
	IncludeHeaders bool   // Whether to include column headers in output
	RecordType     string // Type of records being written for logging purposes
}

// CSVRecord represents a single record that can be written to CSV.
// This interface enables unified CSV writing for different record types.
//
// Performance: Interface dispatch, O(1) method calls
// Side effects: None (implementations may vary)
type CSVRecord interface {
	// ToCSVRecord converts the record to a slice of strings for CSV writing
	ToCSVRecord() []string

	// GetHeaders returns the column headers for this record type
	GetHeaders() []string
}

// SimpleRecord wraps a simple prediction string for CSV output.
//
// Args:
//   - Prediction: The prediction value as a string
//
// Performance: O(1) struct access
// Side effects: None
type SimpleRecord struct {
	Prediction string // The prediction value as a string
}

// ToCSVRecord converts SimpleRecord to CSV format.
//
// Args: None
//
// Returns:
//   - []string: Single-element slice containing the prediction
//
// Performance: O(1) slice creation
// Side effects: None
func (s SimpleRecord) ToCSVRecord() []string {
	return []string{s.Prediction}
}

// GetHeaders returns headers for simple prediction CSV.
//
// Args: None
//
// Returns:
//   - []string: Single-element slice containing "prediction" header
//
// Performance: O(1) slice creation
// Side effects: None
func (s SimpleRecord) GetHeaders() []string {
	return []string{"prediction"}
}

// DetailedRecord wraps a detailed prediction for CSV output.
//
// Args:
//   - Index: Row index for the prediction
//   - Prediction: Detailed prediction data structure
//
// Performance: O(1) struct access
// Side effects: None
type DetailedRecord struct {
	Index      int                       // Row index for the prediction
	Prediction *types.DetailedPrediction // Detailed prediction data structure
}

// ToCSVRecord converts DetailedRecord to CSV format.
//
// Args: None
//
// Returns:
//   - []string: Formatted CSV record with all detailed prediction fields
//
// Performance: O(n) where n is number of features/classes in maps
// Side effects: None
func (d DetailedRecord) ToCSVRecord() []string {
	predictionValue := d.Prediction.Prediction
	if d.Prediction.Error != "" {
		predictionValue = "ERROR"
	}

	return []string{
		fmt.Sprintf("%d", d.Index),
		predictionValue,
		fmt.Sprintf("%.4f", d.Prediction.Confidence),
		d.Prediction.DecisionRule,
		strings.Join(d.Prediction.DecisionPath, " → "),
		formatInputFeatures(d.Prediction.InputFeatures),
		formatClassDistribution(d.Prediction.ClassDistribution),
		d.Prediction.Error,
	}
}

// GetHeaders returns headers for detailed prediction CSV.
//
// Args: None
//
// Returns:
//   - []string: Slice containing all detailed prediction column headers
//
// Performance: O(1) slice creation
// Side effects: None
func (d DetailedRecord) GetHeaders() []string {
	return []string{
		"row_index",
		"prediction",
		"confidence",
		"decision_rule",
		"decision_path",
		"input_features",
		"class_distribution",
		"error",
	}
}

// writeCSV is the unified CSV writing function that handles all record types.
//
// Args:
//   - records: Slice of CSVRecord interfaces to write
//   - config: Configuration for the write operation
//
// Returns:
//   - error: Error if file creation or writing fails
//
// Performance: O(n) where n is number of records
// Side effects: Creates CSV file at config.OutputPath with record data
func writeCSV(records []CSVRecord, config CSVWriteConfig) error {
	logger.Debug(fmt.Sprintf("Writing %d %s predictions to CSV: %s",
		len(records), config.RecordType, config.OutputPath))

	// Create file and writer
	writer, cleanup, err := createCSVWriter(config.OutputPath)
	if err != nil {
		return fmt.Errorf("failed to create CSV writer: %w", err)
	}
	defer cleanup()

	// Write headers if requested
	if config.IncludeHeaders {
		var headers []string
		if len(records) > 0 {
			headers = records[0].GetHeaders()
		} else {
			// For empty records, determine headers based on record type
			switch config.RecordType {
			case "simple":
				headers = SimpleRecord{}.GetHeaders()
			case "detailed":
				headers = DetailedRecord{}.GetHeaders()
			default:
				headers = []string{} // fallback for unknown types
			}
		}

		if len(headers) > 0 {
			if err := writer.Write(headers); err != nil {
				return fmt.Errorf("failed to write headers: %w", err)
			}
		}
	}

	// Write records
	for i, record := range records {
		if err := writer.Write(record.ToCSVRecord()); err != nil {
			return fmt.Errorf("failed to write record %d: %w", i, err)
		}
	}

	logger.Info(fmt.Sprintf("Successfully wrote %d %s predictions to %s",
		len(records), config.RecordType, config.OutputPath))
	return nil
}

// createCSVWriter creates a CSV writer and returns a cleanup function.
//
// Args:
//   - outputPath: Path where the CSV file should be created
//
// Returns:
//   - *csv.Writer: CSV writer instance
//   - func(): Cleanup function to close file and flush writer
//   - error: Error if file creation fails
//
// Performance: O(1) file system operation
// Side effects: Creates file at outputPath
func createCSVWriter(outputPath string) (*csv.Writer, func(), error) {
	file, err := os.Create(outputPath)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create output file: %w", err)
	}

	writer := csv.NewWriter(file)

	cleanup := func() {
		writer.Flush()
		if err := file.Close(); err != nil {
			logger.Error(fmt.Sprintf("Failed to close file %s: %v", outputPath, err))
		}
	}

	return writer, cleanup, nil
}

// WriteSimpleCSV writes simple predictions using the unified interface approach.
//
// Args:
//   - predictions: Slice of prediction strings to write
//   - outputPath: Path where the CSV file should be created
//   - includeHeaders: Whether to include column headers in the output
//
// Returns:
//   - error: Error if file creation or writing fails
//
// Performance: O(n) where n is number of predictions
// Side effects: Creates CSV file at outputPath with prediction data
func WriteSimpleCSV(predictions []string, outputPath string, includeHeaders bool) error {
	records := make([]CSVRecord, len(predictions))
	for i, prediction := range predictions {
		records[i] = SimpleRecord{Prediction: prediction}
	}

	config := CSVWriteConfig{
		OutputPath:     outputPath,
		IncludeHeaders: includeHeaders,
		RecordType:     "simple",
	}

	return writeCSV(records, config)
}

// WriteDetailedCSV writes detailed predictions using the unified interface approach.
//
// Args:
//   - predictions: Slice of DetailedPrediction structs to write
//   - outputPath: Path where the CSV file should be created
//   - includeHeaders: Whether to include column headers in the output
//
// Returns:
//   - error: Error if file creation or writing fails
//
// Performance: O(n) where n is number of predictions
// Side effects: Creates CSV file at outputPath with detailed prediction data
func WriteDetailedCSV(predictions []*types.DetailedPrediction, outputPath string, includeHeaders bool) error {
	records := make([]CSVRecord, len(predictions))
	for i, prediction := range predictions {
		records[i] = DetailedRecord{
			Index:      i,
			Prediction: prediction,
		}
	}

	config := CSVWriteConfig{
		OutputPath:     outputPath,
		IncludeHeaders: includeHeaders,
		RecordType:     "detailed",
	}

	return writeCSV(records, config)
}

// formatInputFeatures formats input features as a JSON-like string.
//
// Args:
//   - features: Map of feature names to values
//
// Returns:
//   - string: Formatted representation like "{feature1:value1, feature2:value2}"
//
// Performance: O(n) where n is number of features
// Side effects: None
func formatInputFeatures(features map[string]interface{}) string {
	if len(features) == 0 {
		return "{}"
	}

	var parts []string
	for key, value := range features {
		parts = append(parts, fmt.Sprintf("%s:%v", key, value))
	}
	return "{" + strings.Join(parts, ", ") + "}"
}

// formatClassDistribution formats class distribution as a JSON-like string.
//
// Args:
//   - distribution: Map of class names to counts
//
// Returns:
//   - string: Formatted representation like "{class1:count1, class2:count2}"
//
// Performance: O(n) where n is number of classes
// Side effects: None
func formatClassDistribution(distribution map[string]int) string {
	if len(distribution) == 0 {
		return "{}"
	}

	var parts []string
	for class, count := range distribution {
		parts = append(parts, fmt.Sprintf("%s:%d", class, count))
	}
	return "{" + strings.Join(parts, ", ") + "}"
}
