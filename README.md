# Mulberri
Efficient, production-grade decision tree implementation written in Go.

## Highlights
- Optimized for speed and memory
- Designed for offline training and fast real-time inference
- Provides interpretable and parsable trees
- Unit test coverage and benchmarks

## Usage

### Training a Model
```bash
go run cmd/mulberri/main.go train \
  --features examples/loan_approval_features.yaml \
  --input examples/loan_approval.csv \
  --target approved \
  --output examples/loan_approval_model.dt \
  --verbose
```

### Making Predictions

#### Simple Predictions
```bash
go run cmd/mulberri/main.go predict \
  --input examples/loan_approval_predict.csv \
  --model examples/loan_approval_model.dt \
  --output examples/predictions_simple.csv
```

#### Detailed Predictions
```bash
go run cmd/mulberri/main.go predict \
  --input examples/loan_approval_predict.csv \
  --model examples/loan_approval_model.dt \
  --output examples/predictions_detailed.csv \
  --detailed
```

## Prediction Output Columns

When using the `--detailed` flag, the prediction output includes the following columns:

| Column | Description | Example |
|--------|-------------|---------|
| **row_index** | Zero-based index of the input row | `0`, `1`, `2` |
| **prediction** | Final prediction value from the decision tree | `yes`, `no`, `approved` |
| **confidence** | Prediction confidence (0.0-1.0) based on leaf node purity | `1.0000`, `0.8500` |
| **decision_rule** | Human-readable IF-THEN rule describing the decision path | `IF income <= 54000 AND education != college THEN no` |
| **decision_path** | Step-by-step path through the tree with node details | `income <= 54000 → education != college → LEAF[no] (confidence: 1.000)` |
| **input_features** | Original input features as JSON | `{"income":32000, "education":"high_school", "age":27}` |
| **class_distribution** | Training sample distribution at the reached leaf node | `{no:9}`, `{yes:8, no:2}` |
| **error** | Error message if prediction failed, empty if successful | `""` or `"feature 'age' not found"` |

### Understanding Confidence
- **1.0000**: Perfect confidence - all training samples at this leaf had the same target value
- **0.8500**: 85% confidence - e.g., 17 out of 20 training samples had the predicted class
- **0.5000**: Low confidence - indicates uncertainty, often from balanced leaf nodes

### Understanding Decision Paths
The decision path shows exactly how the tree made its decision:
- `income <= 54000`: Numerical comparison (went left because income was ≤ threshold)
- `education != college`: Categorical comparison (went right because education was not "college")
- `LEAF[no]`: Reached a leaf node with prediction "no"

## Structure
- `/cmd/mulberri`: Entry point
- `/internal/core`: Core decision tree logic
- `/test`: Integration tests
- `/scripts`: Helper scripts: run tests, generate coverage, linting, etc.


## Access
Feature branches → `Creators`  
Main branch → Merge requires `Founders` approval