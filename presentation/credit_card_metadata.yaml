limit_bal:
  type: numeric
  handle_as: float
  min: 10000.0
  max: 1000000.0
sex:
  type: nominal
  handle_as: string
  values:
  - Female
  - Male
education:
  type: nominal
  handle_as: string
  values:
  - Graduate
  - High_School
  - Other
  - University
  - Unknown
marriage:
  type: nominal
  handle_as: string
  values:
  - Married
  - Other
  - Single
age:
  type: numeric
  handle_as: float
  min: 21.0
  max: 79.0
pay_0:
  type: nominal
  handle_as: string
  values:
  - Delay_1_Months
  - Delay_2_Months
  - Delay_3_Months
  - Delay_4_Months
  - Delay_5_Months
  - Delay_6_Months
  - Delay_7_Months
  - Delay_8_Months
  - No_Consumption
  - Paid_In_Full
  - Revolving_Credit
pay_2:
  type: nominal
  handle_as: string
  values:
  - Delay_1_Months
  - Delay_2_Months
  - Delay_3_Months
  - Delay_4_Months
  - Delay_5_Months
  - Delay_6_Months
  - Delay_7_Months
  - Delay_8_Months
  - No_Consumption
  - Paid_In_Full
  - Revolving_Credit
pay_3:
  type: nominal
  handle_as: string
  values:
  - Delay_1_Months
  - Delay_2_Months
  - Delay_3_Months
  - Delay_4_Months
  - Delay_5_Months
  - Delay_6_Months
  - Delay_7_Months
  - Delay_8_Months
  - No_Consumption
  - Paid_In_Full
  - Revolving_Credit
pay_4:
  type: nominal
  handle_as: string
  values:
  - Delay_1_Months
  - Delay_2_Months
  - Delay_3_Months
  - Delay_4_Months
  - Delay_5_Months
  - Delay_6_Months
  - Delay_7_Months
  - Delay_8_Months
  - No_Consumption
  - Paid_In_Full
  - Revolving_Credit
pay_5:
  type: nominal
  handle_as: string
  values:
  - Delay_2_Months
  - Delay_3_Months
  - Delay_4_Months
  - Delay_5_Months
  - Delay_6_Months
  - Delay_7_Months
  - Delay_8_Months
  - No_Consumption
  - Paid_In_Full
  - Revolving_Credit
pay_6:
  type: nominal
  handle_as: string
  values:
  - Delay_2_Months
  - Delay_3_Months
  - Delay_4_Months
  - Delay_5_Months
  - Delay_6_Months
  - Delay_7_Months
  - Delay_8_Months
  - No_Consumption
  - Paid_In_Full
  - Revolving_Credit
bill_amt1:
  type: numeric
  handle_as: float
  min: -165580.0
  max: 964511.0
bill_amt2:
  type: numeric
  handle_as: float
  min: -69777.0
  max: 983931.0
bill_amt3:
  type: numeric
  handle_as: float
  min: -157264.0
  max: 1664089.0
bill_amt4:
  type: numeric
  handle_as: float
  min: -170000.0
  max: 891586.0
bill_amt5:
  type: numeric
  handle_as: float
  min: -81334.0
  max: 927171.0
bill_amt6:
  type: numeric
  handle_as: float
  min: -339603.0
  max: 961664.0
pay_amt1:
  type: numeric
  handle_as: float
  min: 0.0
  max: 873552.0
pay_amt2:
  type: numeric
  handle_as: float
  min: 0.0
  max: 1684259.0
pay_amt3:
  type: numeric
  handle_as: float
  min: 0.0
  max: 896040.0
pay_amt4:
  type: numeric
  handle_as: float
  min: 0.0
  max: 621000.0
pay_amt5:
  type: numeric
  handle_as: float
  min: 0.0
  max: 426529.0
pay_amt6:
  type: numeric
  handle_as: float
  min: 0.0
  max: 528666.0
default_payment_next_month:
  type: nominal
  handle_as: string
  values:
  - 'No'
  - 'Yes'
