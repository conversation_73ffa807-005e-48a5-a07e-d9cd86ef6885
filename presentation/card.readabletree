id="See5/C5.0 2.07 GPL Edition 2025-09-19"
entries="1"

pay_0 in {Delay_3_Months,Delay_4_Months,Delay_7_Months}: Yes (312/79)
pay_0 in {Delay_5_Months,Delay_8_Months,Paid_In_Full,
:         Revolving_Credit}: No (16342/2277)
pay_0 = Delay_6_Months:
:...age <= 31: Yes (5)
:   age > 31: No (5/1)
pay_0 = Delay_1_Months:
:...pay_2 in {Delay_1_Months,Delay_6_Months,Delay_8_Months,Paid_In_Full,
:   :         Revolving_Credit}: No (538/112)
:   pay_2 in {Delay_3_Months,Delay_7_Months}: Yes (85/34)
:   pay_2 = Delay_5_Months:
:   :...pay_amt2 <= 500: Yes (3)
:   :   pay_amt2 > 500: No (2)
:   pay_2 = Delay_4_Months:
:   :...marriage = Other: Yes (2)
:   :   marriage = Married:
:   :   :...pay_amt1 <= 500: Yes (8/3)
:   :   :   pay_amt1 > 500: No (2)
:   :   marriage = Single:
:   :   :...age <= 22: Yes (3)
:   :       age > 22: No (13/2)
:   pay_2 = Delay_2_Months:
:   :...pay_5 in {Delay_5_Months,Delay_6_Months,Delay_7_Months,
:   :   :         Delay_8_Months}: No (0)
:   :   pay_5 in {Paid_In_Full,Revolving_Credit}: No (875/323)
:   :   pay_5 = Delay_3_Months:
:   :   :...pay_amt4 <= 483: Yes (19/5)
:   :   :   pay_amt4 > 483: No (3)
:   :   pay_5 = Delay_4_Months:
:   :   :...bill_amt1 <= 13488: No (2)
:   :   :   bill_amt1 > 13488: Yes (4)
:   :   pay_5 = No_Consumption:
:   :   :...education in {Other,Unknown}: No (0)
:   :   :   education in {Graduate,University}: No (28/8)
:   :   :   education = High_School: Yes (10/3)
:   :   pay_5 = Delay_2_Months:
:   :   :...pay_6 in {Delay_5_Months,Delay_6_Months,Delay_7_Months,
:   :       :         Delay_8_Months}: Yes (0)
:   :       pay_6 in {Delay_2_Months,Delay_4_Months}: Yes (279/122)
:   :       pay_6 = No_Consumption: No (5/1)
:   :       pay_6 = Delay_3_Months:
:   :       :...sex = Female: No (4/1)
:   :       :   sex = Male: Yes (5)
:   :       pay_6 = Paid_In_Full:
:   :       :...bill_amt3 <= 4991: Yes (2)
:   :       :   bill_amt3 > 4991: No (2)
:   :       pay_6 = Revolving_Credit:
:   :       :...limit_bal <= 20000: Yes (23/7)
:   :           limit_bal > 20000: No (88/32)
:   pay_2 = No_Consumption:
:   :...age <= 56: No (963/261)
:       age > 56:
:       :...pay_amt2 > 724: No (4)
:           pay_amt2 <= 724:
:           :...pay_amt6 <= 9215: Yes (26/4)
:               pay_amt6 > 9215: No (2)
pay_0 = Delay_2_Months:
:...bill_amt1 > 2290:
:   :...pay_amt5 <= 21615: Yes (1930/537)
:   :   pay_amt5 > 21615: No (29/10)
:   bill_amt1 <= 2290:
:   :...pay_3 in {Delay_1_Months,Delay_8_Months}: No (0)
:       pay_3 in {Delay_4_Months,Delay_6_Months,
:       :         Revolving_Credit}: Yes (28/10)
:       pay_3 in {Delay_5_Months,Delay_7_Months,No_Consumption,
:       :         Paid_In_Full}: No (104/31)
:       pay_3 = Delay_3_Months:
:       :...pay_6 in {Delay_2_Months,Delay_4_Months,Delay_5_Months,
:       :   :         Delay_6_Months,Delay_7_Months,Delay_8_Months,
:       :   :         No_Consumption,Revolving_Credit}: Yes (0)
:       :   pay_6 = Delay_3_Months: Yes (9/1)
:       :   pay_6 = Paid_In_Full: No (4)
:       pay_3 = Delay_2_Months:
:       :...marriage = Other: Yes (0)
:           marriage = Married: Yes (15/2)
:           marriage = Single:
:           :...education in {Other,Unknown}: Yes (0)
:               education = Graduate: Yes (8/1)
:               education = High_School: No (2/1)
:               education = University:
:               :...age <= 39: No (7)
:                   age > 39: Yes (2)
pay_0 = No_Consumption:
:...pay_amt1 > 55: No (1473/121)
    pay_amt1 <= 55:
    :...limit_bal <= 350000: No (594/112)
        limit_bal > 350000:
        :...pay_amt6 > 75: No (50/7)
            pay_amt6 <= 75:
            :...pay_5 in {Delay_3_Months,Delay_4_Months,Delay_5_Months,
                :         Delay_6_Months,Delay_7_Months,Delay_8_Months,
                :         Revolving_Credit}: No (0)
                pay_5 in {Delay_2_Months,Paid_In_Full}: Yes (3)
                pay_5 = No_Consumption:
                :...bill_amt4 > 458: No (6)
                    bill_amt4 <= 458:
                    :...pay_amt4 > 1047: Yes (5)
                        pay_amt4 <= 1047:
                        :...limit_bal <= 480000: No (57/22)
                            limit_bal > 480000: Yes (10/1)