// Package errors provides centralized error handling utilities to eliminate code duplication.
//
// This package consolidates common error patterns found throughout the codebase:
// - Error logging with consistent formatting
// - Fatal error handling with context
// - Test error handling patterns
// - File validation error patterns
//
// Design Principles:
// - DRY compliance: Single implementation for common error patterns
// - Consistent error formatting across the application
// - Centralized logging behavior for easier maintenance
// - Type-safe error handling with proper context
//
// Usage Examples:
//   errors.LogErrorAndReturn("failed to process data", err)
//   errors.LogFatalWithContext("configuration", "invalid feature type", featureType)
//   errors.TestFatalf(t, "failed to create builder", err)
package errors

import (
	"fmt"
	"testing"

	"github.com/berrijam/mulberri/internal/utils/logger"
)

// LogErrorAndReturn logs an error with context and returns a formatted error.
//
// Args:
//   - context: Description of what operation failed
//   - err: The underlying error that occurred
//
// Returns:
//   - error: Formatted error with context for return to caller
//
// Performance: O(1) logging operation
// Side effects: Logs error message at ERROR level
func LogErrorAndReturn(context string, err error) error {
	message := fmt.Sprintf("%s: %v", context, err)
	logger.Error(message)
	return fmt.Errorf("%s: %w", context, err)
}

// LogErrorWithMessage logs an error with a custom message and returns a formatted error.
//
// Args:
//   - message: Custom error message
//   - err: The underlying error that occurred
//
// Returns:
//   - error: Formatted error with message for return to caller
//
// Performance: O(1) logging operation
// Side effects: Logs error message at ERROR level
func LogErrorWithMessage(message string, err error) error {
	fullMessage := fmt.Sprintf("%s: %v", message, err)
	logger.Error(fullMessage)
	return fmt.Errorf("%s: %w", message, err)
}

// LogErrorAndReturnSimple logs an error message and returns it as an error.
//
// Args:
//   - message: Error message to log and return
//
// Returns:
//   - error: Error with the provided message
//
// Performance: O(1) logging operation
// Side effects: Logs error message at ERROR level
func LogErrorAndReturnSimple(message string) error {
	logger.Error(message)
	return fmt.Errorf(message)
}

// LogFatalWithContext logs a fatal error with context and terminates the application.
//
// Args:
//   - context: Description of what operation failed
//   - message: Specific error message
//   - value: Value that caused the error (optional)
//
// Returns: Never returns (calls logger.Fatal)
// Performance: O(1) logging operation
// Side effects: Logs fatal message and terminates application
func LogFatalWithContext(context, message string, value interface{}) {
	if value != nil {
		logger.Fatal(fmt.Sprintf("%s: %s: %v", context, message, value))
	} else {
		logger.Fatal(fmt.Sprintf("%s: %s", context, message))
	}
}

// LogFatalSimple logs a fatal error message and terminates the application.
//
// Args:
//   - message: Fatal error message
//
// Returns: Never returns (calls logger.Fatal)
// Performance: O(1) logging operation
// Side effects: Logs fatal message and terminates application
func LogFatalSimple(message string) {
	logger.Fatal(message)
}

// LogFatalWithValue logs a fatal error with a formatted value and terminates the application.
//
// Args:
//   - message: Error message template
//   - value: Value to include in the message
//
// Returns: Never returns (calls logger.Fatal)
// Performance: O(1) logging operation
// Side effects: Logs fatal message and terminates application
func LogFatalWithValue(message string, value interface{}) {
	logger.Fatal(fmt.Sprintf(message, value))
}

// TestFatalf provides consistent test error handling with context.
//
// Args:
//   - t: Testing instance
//   - context: Description of what operation failed
//   - err: The error that occurred
//
// Returns: Never returns (calls t.Fatalf)
// Performance: O(1) test failure operation
// Side effects: Fails test with formatted message
func TestFatalf(t *testing.T, context string, err error) {
	t.Helper()
	t.Fatalf("%s: %v", context, err)
}

// TestFatalfWithMessage provides consistent test error handling with custom message.
//
// Args:
//   - t: Testing instance
//   - message: Custom error message
//   - args: Arguments for message formatting
//
// Returns: Never returns (calls t.Fatalf)
// Performance: O(1) test failure operation
// Side effects: Fails test with formatted message
func TestFatalfWithMessage(t *testing.T, message string, args ...interface{}) {
	t.Helper()
	t.Fatalf(message, args...)
}

// ValidationError represents a validation error with context.
type ValidationError struct {
	Context string
	Field   string
	Value   interface{}
	Message string
}

// Error implements the error interface for ValidationError.
func (ve ValidationError) Error() string {
	if ve.Value != nil {
		return fmt.Sprintf("%s validation failed for %s: %s (value: %v)", 
			ve.Context, ve.Field, ve.Message, ve.Value)
	}
	return fmt.Sprintf("%s validation failed for %s: %s", 
		ve.Context, ve.Field, ve.Message)
}

// LogValidationFatal logs a validation error and terminates the application.
//
// Args:
//   - context: Validation context (e.g., "feature configuration")
//   - field: Field that failed validation
//   - value: Value that failed validation
//   - message: Specific validation error message
//
// Returns: Never returns (calls logger.Fatal)
// Performance: O(1) logging operation
// Side effects: Logs fatal validation error and terminates application
func LogValidationFatal(context, field string, value interface{}, message string) {
	err := ValidationError{
		Context: context,
		Field:   field,
		Value:   value,
		Message: message,
	}
	logger.Fatal(err.Error())
}
