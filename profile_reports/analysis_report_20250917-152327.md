# Mulberri Performance Analysis Report
Generated: Wed Sep 17 03:23:27 PM EAT 2025

## Executive Summary

This report analyzes CPU and memory profiles from Mulberri decision tree operations.
Findings are categorized by severity and actionability.

## CPU Profile Analysis: Training

- **Duration**: 200.82ms,
- **Total Samples**: samples

### Top CPU Consumers
```
File: mulberri
Type: cpu
Time: Sep 17, 2025 at 3:16pm (EAT)
Duration: 200.82ms, Total samples = 20ms ( 9.96%)
Showing nodes accounting for 20ms, 100% of 20ms total
      flat  flat%   sum%        cum   cum%
      10ms 50.00% 50.00%       10ms 50.00%  runtime.step
      10ms 50.00%   100%       10ms 50.00%  time.Time.appendFormatRFC3339
         0     0%   100%       10ms 50.00%  encoding/json.(*encodeState).marshal
         0     0%   100%       10ms 50.00%  encoding/json.(*encodeState).reflectValue
         0     0%   100%       10ms 50.00%  encoding/json.Marshal
         0     0%   100%       10ms 50.00%  encoding/json.MarshalIndent
         0     0%   100%       10ms 50.00%  encoding/json.addrMarshalerEncoder
         0     0%   100%       10ms 50.00%  encoding/json.condAddrEncoder.encode
         0     0%   100%       10ms 50.00%  encoding/json.marshalerEncoder
         0     0%   100%       10ms 50.00%  encoding/json.ptrEncoder.encode
         0     0%   100%       10ms 50.00%  encoding/json.structEncoder.encode
         0     0%   100%       20ms   100%  github.com/berrijam/mulberri/internal/io/cli.Execute
         0     0%   100%       20ms   100%  github.com/berrijam/mulberri/internal/io/cli.NewTrainCommand.func1
         0     0%   100%       20ms   100%  github.com/berrijam/mulberri/internal/io/cli.runTraining
```

### Analysis
**🔴 CRITICAL & SERIOUS**: Functions consuming >20% CPU time:
```
      10ms 50.00% 50.00%       10ms 50.00%  runtime.step
      10ms 50.00%   100%       10ms 50.00%  time.Time.appendFormatRFC3339
```
**Action Required**: Optimize these hot paths immediately.

**🟡 CRITICAL BUT NOT SERIOUS**: JSON/Serialization overhead detected:
```
         0     0%   100%       10ms 50.00%  encoding/json.(*encodeState).marshal
         0     0%   100%       10ms 50.00%  encoding/json.(*encodeState).reflectValue
         0     0%   100%       10ms 50.00%  encoding/json.Marshal
         0     0%   100%       10ms 50.00%  encoding/json.MarshalIndent
         0     0%   100%       10ms 50.00%  encoding/json.addrMarshalerEncoder
```
**Recommendation**: Consider binary serialization for large models or frequent operations.

**🟡 CRITICAL BUT NOT SERIOUS**: Logging overhead detected:
```
         0     0%   100%       10ms 50.00%  github.com/berrijam/mulberri/internal/utils/logger.(*ZapLogger).Debug
         0     0%   100%       10ms 50.00%  github.com/berrijam/mulberri/internal/utils/logger.(*customFileEncoder).EncodeEntry
         0     0%   100%       10ms 50.00%  github.com/berrijam/mulberri/internal/utils/logger.Debug
```
**Recommendation**: Reduce log verbosity in production or use async logging.

**✅ OKAY**: Core algorithm functions detected:
```
         0     0%   100%       10ms 50.00%  github.com/berrijam/mulberri/internal/training.(*TreeBuilder).BuildTree
         0     0%   100%       10ms 50.00%  github.com/berrijam/mulberri/internal/training.(*TreeBuilder).buildNode
         0     0%   100%       10ms 50.00%  github.com/berrijam/mulberri/internal/training.findBestSplit
```
**Status**: Normal algorithm execution. Monitor for performance regressions.

## CPU Profile Analysis: Prediction

- **Duration**: 201.45ms,
- **Total Samples**: samples

### Top CPU Consumers
```
File: mulberri
Type: cpu
Time: Sep 17, 2025 at 3:16pm (EAT)
Duration: 201.45ms, Total samples = 0 
Showing nodes accounting for 0, 0% of 0 total
      flat  flat%   sum%        cum   cum%
```

### Analysis
## Memory Profile Analysis: Training

### Top Memory Allocators
```
File: mulberri
Type: inuse_space
Time: Sep 17, 2025 at 3:16pm (EAT)
Showing nodes accounting for 3300.08kB, 100% of 3300.08kB total
      flat  flat%   sum%        cum   cum%
 1762.94kB 53.42% 53.42%  1762.94kB 53.42%  runtime/pprof.StartCPUProfile
 1025.12kB 31.06% 84.48%  1025.12kB 31.06%  sync.(*Pool).pinSlow
  512.02kB 15.52%   100%   512.02kB 15.52%  go.uber.org/zap/internal/pool.New[go.shape.*uint8] (inline)
         0     0%   100%  3300.08kB   100%  github.com/berrijam/mulberri/internal/io/cli.Execute
         0     0%   100%  3300.08kB   100%  github.com/berrijam/mulberri/internal/io/cli.NewTrainCommand.func1
         0     0%   100%  3300.08kB   100%  github.com/berrijam/mulberri/internal/io/cli.runTraining
         0     0%   100%  1537.15kB 46.58%  github.com/berrijam/mulberri/internal/training.(*TreeBuilder).BuildTree
         0     0%   100%  1537.15kB 46.58%  github.com/berrijam/mulberri/internal/training.(*TreeBuilder).buildNode
         0     0%   100%  1025.12kB 31.06%  github.com/berrijam/mulberri/internal/training.findBestSplit
         0     0%   100%  1537.15kB 46.58%  github.com/berrijam/mulberri/internal/utils/logger.(*ZapLogger).Debug
         0     0%   100%  1537.15kB 46.58%  github.com/berrijam/mulberri/internal/utils/logger.(*customFileEncoder).EncodeEntry
         0     0%   100%  1537.15kB 46.58%  github.com/berrijam/mulberri/internal/utils/logger.Debug
         0     0%   100%  1762.94kB 53.42%  github.com/berrijam/mulberri/internal/utils/profiling.(*Profiler).StartCPUProfile
         0     0%   100%  3300.08kB   100%  github.com/spf13/cobra.(*Command).Execute (inline)
         0     0%   100%  3300.08kB   100%  github.com/spf13/cobra.(*Command).ExecuteC
```

### Analysis
**✅ OKAY**: Profiling overhead detected:
```
 1762.94kB 53.42% 53.42%  1762.94kB 53.42%  runtime/pprof.StartCPUProfile
         0     0%   100%  1762.94kB 53.42%  github.com/berrijam/mulberri/internal/utils/profiling.(*Profiler).StartCPUProfile
```
**Status**: Expected overhead from profiling. Disable in production.

**🟡 CRITICAL BUT NOT SERIOUS**: Memory pool allocations:
```
 1025.12kB 31.06% 84.48%  1025.12kB 31.06%  sync.(*Pool).pinSlow
  512.02kB 15.52%   100%   512.02kB 15.52%  go.uber.org/zap/internal/pool.New[go.shape.*uint8] (inline)
         0     0%   100%  1025.12kB 31.06%  go.uber.org/zap/internal/pool.(*Pool[go.shape.*uint8]).Get (inline)
```
**Recommendation**: Monitor pool efficiency and consider tuning pool sizes.

**🟡 CRITICAL BUT NOT SERIOUS**: Logging memory usage:
```
  512.02kB 15.52%   100%   512.02kB 15.52%  go.uber.org/zap/internal/pool.New[go.shape.*uint8] (inline)
         0     0%   100%  1537.15kB 46.58%  github.com/berrijam/mulberri/internal/utils/logger.(*ZapLogger).Debug
         0     0%   100%  1537.15kB 46.58%  github.com/berrijam/mulberri/internal/utils/logger.(*customFileEncoder).EncodeEntry
```
**Recommendation**: Use structured logging with buffer pools and consider log sampling.

## Memory Profile Analysis: Prediction

### Top Memory Allocators
```
File: mulberri
Type: inuse_space
Time: Sep 17, 2025 at 3:16pm (EAT)
Showing nodes accounting for 1.16MB, 100% of 1.16MB total
      flat  flat%   sum%        cum   cum%
    1.16MB   100%   100%     1.16MB   100%  runtime/pprof.StartCPUProfile
         0     0%   100%     1.16MB   100%  github.com/berrijam/mulberri/internal/io/cli.Execute
         0     0%   100%     1.16MB   100%  github.com/berrijam/mulberri/internal/io/cli.NewPredictCommand.func1
         0     0%   100%     1.16MB   100%  github.com/berrijam/mulberri/internal/io/cli.runPrediction
         0     0%   100%     1.16MB   100%  github.com/berrijam/mulberri/internal/utils/profiling.(*Profiler).StartCPUProfile
         0     0%   100%     1.16MB   100%  github.com/spf13/cobra.(*Command).Execute (inline)
         0     0%   100%     1.16MB   100%  github.com/spf13/cobra.(*Command).ExecuteC
         0     0%   100%     1.16MB   100%  github.com/spf13/cobra.(*Command).execute
         0     0%   100%     1.16MB   100%  main.main
         0     0%   100%     1.16MB   100%  runtime.main
```

### Analysis
**🔴 CRITICAL & SERIOUS**: Large memory allocations detected (>1MB):
```
    1.16MB   100%   100%     1.16MB   100%  runtime/pprof.StartCPUProfile
```
**Action Required**: Investigate memory usage patterns and implement streaming/chunking.

**✅ OKAY**: Profiling overhead detected:
```
    1.16MB   100%   100%     1.16MB   100%  runtime/pprof.StartCPUProfile
         0     0%   100%     1.16MB   100%  github.com/berrijam/mulberri/internal/utils/profiling.(*Profiler).StartCPUProfile
```
**Status**: Expected overhead from profiling. Disable in production.

## Performance Baseline

Based on current profiling with small dataset (30 samples):

| Metric | Training | Prediction |
|--------|----------|------------|
| Duration | 200.82ms, | 201.45ms, |
| Memory Usage | ~3.3MB | ~1MB |
| Dataset Size | 30 samples | 15 samples |

**Note**: These metrics are for a small test dataset. Performance characteristics will change significantly with larger datasets.

## Recommendations & Action Items

### Immediate Actions (Critical & Serious)
1. **Profile with larger datasets** to identify real bottlenecks
2. **Implement benchmarks** for core algorithms with various dataset sizes
3. **Monitor memory growth** with large datasets (>10k samples)

### Medium-term Improvements (Critical but Not Serious)
1. **Optimize JSON serialization**: Consider binary formats for large models
2. **Reduce logging overhead**: Implement log levels and async logging
3. **Memory pool tuning**: Optimize buffer pool sizes based on usage patterns
4. **Implement streaming**: For large dataset processing to reduce memory footprint

### Long-term Optimizations
1. **Algorithm optimization**: Profile with real-world datasets
2. **Parallel processing**: Implement concurrent tree building for large datasets
3. **Memory-mapped files**: For very large datasets that don't fit in memory
4. **Caching strategies**: Implement intelligent caching for repeated operations

