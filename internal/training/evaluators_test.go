package training

import (
	"math"
	"testing"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
)

// buildTestDataset creates a small dataset for split evaluation tests
func buildTestDataset() *dataset.Dataset[string] {
	d := dataset.NewDataset[string](0)

	// Features
	d.AddIntColumn("age", []int64{25, 30, 35, 40}, []bool{false, false, false, false})
	d.AddStringColumn("education", []string{"college", "high_school", "college", "graduate"}, []bool{false, false, false, false})

	// Targets: 1x "A", 3x "B"
	d.AddTarget("A")
	d.AddTarget("B")
	d.AddTarget("B")
	d.AddTarget("B")

	return d
}

func TestEvaluateFeatureSplits_Numerical(t *testing.T) {
	d := buildTestDataset()
	view := d.<PERSON><PERSON><PERSON>iew([]int{0, 1, 2, 3})

	res := EvaluateFeatureSplits(view, d, "age")
	if res == nil {
		t.Fatalf("expected non-nil result")
	}

	if res.FeatureName != "age" || res.FeatureType != features.IntegerFeature {
		t.Fatalf("unexpected feature metadata: %s %v", res.FeatureName, res.FeatureType)
	}

	// Base impurity for targets [A,B,B,B]
	if math.Abs(res.BaseImpurity-0.8112781244591328) > 1e-9 {
		t.Errorf("unexpected base impurity: %v", res.BaseImpurity)
	}

	if res.BestCandidate == nil {
		t.Fatalf("expected a best candidate for age")
	}

	if res.BestCandidate.Type != NumericalSplit {
		t.Fatalf("expected numerical split, got %v", res.BestCandidate.Type)
	}

	if res.BestCandidate.Threshold == nil || math.Abs(*res.BestCandidate.Threshold-27.5) > 1e-6 {
		t.Errorf("expected threshold ~27.5, got %v", res.BestCandidate.Threshold)
	}

	if res.BestCandidate.Gain <= 0 {
		t.Errorf("expected positive gain, got %v", res.BestCandidate.Gain)
	}
}

func TestEvaluateFeatureSplits_Categorical(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	res := EvaluateFeatureSplits(view, d, "education")
	if res == nil {
		t.Fatalf("expected non-nil result")
	}

	if res.FeatureType != features.StringFeature {
		t.Fatalf("expected string feature type, got %v", res.FeatureType)
	}

	if res.BestCandidate == nil || res.BestCandidate.Type != CategoricalSplit {
		t.Fatalf("expected n-ary categorical best candidate")
	}

	// N-ary split should have ChildPartitions instead of single Value
	if res.BestCandidate.ChildPartitions == nil {
		t.Errorf("expected ChildPartitions for n-ary split")
	}

	// Should have multiple partitions for different education values
	if len(res.BestCandidate.ChildPartitions) < 2 {
		t.Errorf("expected at least 2 child partitions, got %d", len(res.BestCandidate.ChildPartitions))
	}

	if res.BestCandidate.Gain <= 0 {
		t.Errorf("expected positive gain, got %v", res.BestCandidate.Gain)
	}
}

func TestEvaluateAllFeatureSplits_SelectsBest(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	// Include one missing feature to exercise skip path
	res := EvaluateAllFeatureSplits(view, d, []string{"missing", "age", "education"})
	if res == nil || res.BestCandidate == nil {
		t.Fatalf("expected a best overall result")
	}

	// Age split should have higher gain than education for this dataset
	if res.FeatureName != "age" {
		t.Errorf("expected best feature 'age', got %s", res.FeatureName)
	}
}

func TestGetSplitDescriptionAndExtractHelpers(t *testing.T) {
	thr := 10.0
	num := &SplitCandidate{FeatureName: "age", Type: NumericalSplit, Threshold: &thr}
	if desc := GetSplitDescription(num); desc != "age <= 10.000" {
		t.Errorf("unexpected numerical desc: %s", desc)
	}

	cat := &SplitCandidate{FeatureName: "education", Type: CategoricalSplit, Value: "college"}
	if desc := GetSplitDescription(cat); desc != "education n-ary split" {
		t.Errorf("unexpected categorical desc: %s", desc)
	}

	if desc := GetSplitDescription(nil); desc != "no split" {
		t.Errorf("unexpected nil desc: %s", desc)
	}

	// extract helpers
	i := int64(7)
	if v := extractNumericalValue(&i); v == nil || *v != 7.0 {
		t.Errorf("extractNumericalValue int64 failed: %v", v)
	}
	f := 3.25
	if v := extractNumericalValue(&f); v == nil || *v != 3.25 {
		t.Errorf("extractNumericalValue float64 failed: %v", v)
	}
	if v := extractNumericalValue("nope"); v != nil {
		t.Errorf("extractNumericalValue should be nil for non-numeric, got %v", v)
	}

	// Note: extractStringValue tests removed as function is no longer used
	// Binary categorical splits are now handled as n-ary splits
}

func TestEvaluateFeatureSplits_InvalidFeature(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	if res := EvaluateFeatureSplits(view, d, "does_not_exist"); res != nil {
		t.Errorf("expected nil result for missing feature, got %+v", res)
	}
}

func TestEvaluateAllFeatureSplits_NoneValid(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	if res := EvaluateAllFeatureSplits(view, d, []string{"missing1", "missing2"}); res != nil {
		t.Errorf("expected nil when no features are valid, got %+v", res)
	}
}

func TestCategoricalSplitEvaluator(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	// Test n-ary categorical split evaluation
	res := EvaluateFeatureSplits(view, d, "education") // Now always uses n-ary splits
	if res == nil {
		t.Fatalf("expected non-nil result for n-ary evaluation")
	}

	if res.FeatureType != features.StringFeature {
		t.Errorf("expected StringFeature, got %v", res.FeatureType)
	}

	if res.BestCandidate == nil {
		t.Fatalf("expected non-nil best candidate")
	}

	// N-ary split should have CategoricalSplit type
	if res.BestCandidate.Type != CategoricalSplit {
		t.Errorf("expected CategoricalSplit, got %v", res.BestCandidate.Type)
	}

	// Should have ChildPartitions populated
	if res.BestCandidate.ChildPartitions == nil {
		t.Errorf("expected non-nil ChildPartitions for n-ary split")
	}

	// Should have multiple partitions (more than 2 for n-ary)
	if len(res.BestCandidate.ChildPartitions) < 2 {
		t.Errorf("expected at least 2 child partitions, got %d", len(res.BestCandidate.ChildPartitions))
	}

	// Gain should be positive
	if res.BestCandidate.Gain <= 0 {
		t.Errorf("expected positive gain, got %v", res.BestCandidate.Gain)
	}
}

func TestApplyNArySplitToView(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	// Get n-ary split candidate
	res := EvaluateFeatureSplits(view, d, "education")
	if res == nil || res.BestCandidate == nil {
		t.Fatalf("need an n-ary split candidate")
	}

	// Apply n-ary split
	childViews, err := res.BestCandidate.ApplySplit(view)
	if err != nil {
		t.Fatalf("failed to apply split: %v", err)
	}
	if childViews == nil {
		t.Fatalf("expected non-nil child views map")
	}

	// Should have multiple child views
	if len(childViews) < 2 {
		t.Errorf("expected at least 2 child views, got %d", len(childViews))
	}

	// Total samples across children should equal parent
	totalChildSamples := 0
	for _, childView := range childViews {
		totalChildSamples += childView.GetSize()
	}

	if totalChildSamples != view.GetSize() {
		t.Errorf("child samples (%d) should equal parent samples (%d)", totalChildSamples, view.GetSize())
	}

	// Each child view should be non-empty
	for value, childView := range childViews {
		if childView.GetSize() == 0 {
			t.Errorf("child view for value %v should not be empty", value)
		}
	}
}

func TestNArySplitProperties(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	// Test n-ary splits (now the only option)
	res := EvaluateFeatureSplits(view, d, "education") // Now always uses n-ary splits

	if res == nil {
		t.Fatalf("n-ary evaluation should succeed")
	}

	// Should find splits
	if res.BestCandidate == nil {
		t.Fatalf("evaluation should find split candidates")
	}

	// Should be n-ary categorical split
	if res.BestCandidate.Type != CategoricalSplit {
		t.Errorf("split should be CategoricalSplit, got %v", res.BestCandidate.Type)
	}

	// N-ary should have ChildPartitions
	if res.BestCandidate.ChildPartitions == nil {
		t.Errorf("n-ary split should have ChildPartitions")
	}

	// Should have positive gain
	if res.BestCandidate.Gain <= 0 {
		t.Errorf("split should have positive gain, got %v", res.BestCandidate.Gain)
	}
}
