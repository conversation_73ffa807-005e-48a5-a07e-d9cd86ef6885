package tree

import (
	"encoding/json"
	"strings"
	"testing"
	"time"

	"github.com/berrijam/mulberri/internal/data/features"
)

// ====================
// TreeMetadata Tests
// ====================

func TestNewTreeMetadata(t *testing.T) {
	tests := []struct {
		name            string
		algorithm       string
		maxDepth        int
		minSamples      int
		criterion       string
		trainingSamples int
		expectNil       bool
	}{
		{
			name:            "valid metadata",
			algorithm:       "C4.5",
			maxDepth:        10,
			minSamples:      20,
			criterion:       "entropy",
			trainingSamples: 1000,
			expectNil:       false,
		},
		{
			name:            "valid with different criterion",
			algorithm:       "C4.5",
			maxDepth:        15,
			minSamples:      5,
			criterion:       "gini",
			trainingSamples: 500,
			expectNil:       false,
		},
		{
			name:            "empty algorithm",
			algorithm:       "",
			maxDepth:        10,
			minSamples:      20,
			criterion:       "entropy",
			trainingSamples: 1000,
			expectNil:       true,
		},
		{
			name:            "zero max depth",
			algorithm:       "C4.5",
			maxDepth:        0,
			minSamples:      20,
			criterion:       "entropy",
			trainingSamples: 1000,
			expectNil:       true,
		},
		{
			name:            "negative min samples",
			algorithm:       "C4.5",
			maxDepth:        10,
			minSamples:      -5,
			criterion:       "entropy",
			trainingSamples: 1000,
			expectNil:       true,
		},
		{
			name:            "negative training samples",
			algorithm:       "C4.5",
			maxDepth:        10,
			minSamples:      20,
			criterion:       "entropy",
			trainingSamples: -100,
			expectNil:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			metadata := NewTreeMetadata(tt.algorithm, tt.maxDepth, tt.minSamples, tt.criterion, tt.trainingSamples, "target")

			if tt.expectNil {
				if metadata != nil {
					t.Error("expected nil metadata but got valid metadata")
				}
				return
			}

			if metadata == nil {
				t.Error("expected valid metadata but got nil")
				return
			}

			if metadata.Algorithm != tt.algorithm {
				t.Errorf("expected algorithm %s, got %s", tt.algorithm, metadata.Algorithm)
			}
			if metadata.MaxDepth != tt.maxDepth {
				t.Errorf("expected max depth %d, got %d", tt.maxDepth, metadata.MaxDepth)
			}
			if metadata.MinSamples != tt.minSamples {
				t.Errorf("expected min samples %d, got %d", tt.minSamples, metadata.MinSamples)
			}
			if metadata.Criterion != tt.criterion {
				t.Errorf("expected criterion %s, got %s", tt.criterion, metadata.Criterion)
			}
			if metadata.TrainingSamples != tt.trainingSamples {
				t.Errorf("expected training samples %d, got %d", tt.trainingSamples, metadata.TrainingSamples)
			}

			// Check timestamp is reasonable (within last few seconds)
			timeDiff := time.Since(metadata.CreatedAt)
			if timeDiff < 0 || timeDiff > time.Minute {
				t.Errorf("created timestamp seems invalid: %v", metadata.CreatedAt)
			}

			// Initial statistics should be zero
			if metadata.TotalNodes != 0 {
				t.Errorf("expected initial total nodes 0, got %d", metadata.TotalNodes)
			}
			if metadata.LeafNodes != 0 {
				t.Errorf("expected initial leaf nodes 0, got %d", metadata.LeafNodes)
			}
		})
	}
}

// ====================
// DecisionTree Constructor Tests
// ====================

func TestNewDecisionTree(t *testing.T) {
	// Create test components
	validFeatures := []*Feature{
		{Name: "age", Type: features.IntegerFeature},
		{Name: "salary", Type: features.FloatFeature},
	}
	validClasses := []string{"yes", "no"}
	validMetadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 100, "target")

	if validMetadata == nil {
		t.Fatal("failed to create valid metadata for testing")
	}

	validRoot, err := NewLeafNode(map[interface{}]int{"yes": 60, "no": 40})
	if err != nil {
		t.Fatal("failed to create valid root node for testing:", err)
	}

	tests := []struct {
		name      string
		root      Node
		features  []*Feature
		classes   []string
		metadata  *TreeMetadata
		expectNil bool
	}{
		{
			name:      "valid decision tree",
			root:      validRoot,
			features:  validFeatures,
			classes:   validClasses,
			metadata:  validMetadata,
			expectNil: false,
		},
		{
			name:      "nil root",
			root:      nil,
			features:  validFeatures,
			classes:   validClasses,
			metadata:  validMetadata,
			expectNil: true,
		},
		{
			name:      "empty features",
			root:      validRoot,
			features:  []*Feature{},
			classes:   validClasses,
			metadata:  validMetadata,
			expectNil: true,
		},
		{
			name:      "empty classes",
			root:      validRoot,
			features:  validFeatures,
			classes:   []string{},
			metadata:  validMetadata,
			expectNil: true,
		},
		{
			name:      "nil metadata",
			root:      validRoot,
			features:  validFeatures,
			classes:   validClasses,
			metadata:  nil,
			expectNil: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tree := NewDecisionTree(tt.root, tt.features, tt.classes, tt.metadata)

			if tt.expectNil {
				if tree != nil {
					t.Error("expected nil tree but got valid tree")
				}
				return
			}

			if tree == nil {
				t.Error("expected valid tree but got nil")
				return
			}

			if tree.Root != tt.root {
				t.Error("root node mismatch")
			}
			if len(tree.Features) != len(tt.features) {
				t.Errorf("expected %d features, got %d", len(tt.features), len(tree.Features))
			}
			if len(tree.Classes) != len(tt.classes) {
				t.Errorf("expected %d classes, got %d", len(tt.classes), len(tree.Classes))
			}

			// Check that statistics were calculated
			if tree.Metadata.TotalNodes == 0 {
				t.Error("expected statistics to be calculated, but total nodes is 0")
			}
			if tree.Metadata.LeafNodes == 0 {
				t.Error("expected statistics to be calculated, but leaf nodes is 0")
			}
		})
	}
}

// ====================
// Tree Statistics Tests
// ====================

func TestDecisionTree_GetDepth(t *testing.T) {
	tests := []struct {
		name          string
		setupTree     func() *DecisionTree
		expectedDepth int
	}{
		{
			name: "single leaf tree",
			setupTree: func() *DecisionTree {
				root, err := NewLeafNode(map[interface{}]int{"yes": 5})
				if err != nil {
					return nil
				}
				features := []*Feature{{Name: "test", Type: features.IntegerFeature}}
				metadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 5, "target")
				return NewDecisionTree(root, features, []string{"yes"}, metadata)
			},
			expectedDepth: 0,
		},
		{
			name: "two level tree",
			setupTree: func() *DecisionTree {
				// Create decision node with leaf children
				feature, err := NewFeature("age", features.IntegerFeature)
				if err != nil {
					return nil
				}
				root, err := NewDecisionNode(feature, 30.0, map[interface{}]int{"yes": 6, "no": 4})
				if err != nil {
					return nil
				}

				leftChild, err := NewLeafNode(map[interface{}]int{"yes": 4, "no": 1})
				if err != nil {
					return nil
				}
				rightChild, err := NewLeafNode(map[interface{}]int{"yes": 2, "no": 3})
				if err != nil {
					return nil
				}

				root.SetLeftChild(leftChild)
				root.SetRightChild(rightChild)

				features := []*Feature{feature}
				metadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 10, "target")
				return NewDecisionTree(root, features, []string{"yes", "no"}, metadata)
			},
			expectedDepth: 1,
		},
		{
			name: "three level tree",
			setupTree: func() *DecisionTree {
				// Root decision node
				ageFeature, err := NewFeature("age", features.IntegerFeature)
				if err != nil {
					return nil
				}
				deptFeature, err := NewFeature("dept", features.StringFeature)
				if err != nil {
					return nil
				}

				root, err := NewDecisionNode(ageFeature, 30.0, map[interface{}]int{"yes": 10, "no": 8})
				if err != nil {
					return nil
				}

				// Left child: another decision node
				leftDecision, err := NewDecisionNode(deptFeature, "Engineering", map[interface{}]int{"yes": 6, "no": 2})
				if err != nil {
					return nil
				}
				leftLeft, err := NewLeafNode(map[interface{}]int{"yes": 5, "no": 1})
				if err != nil {
					return nil
				}
				leftRight, err := NewLeafNode(map[interface{}]int{"yes": 1, "no": 1})
				if err != nil {
					return nil
				}

				leftDecision.SetLeftChild(leftLeft)
				leftDecision.SetRightChild(leftRight)

				// Right child: leaf node
				rightLeaf, err := NewLeafNode(map[interface{}]int{"yes": 4, "no": 6})
				if err != nil {
					return nil
				}

				root.SetLeftChild(leftDecision)
				root.SetRightChild(rightLeaf)

				features := []*Feature{ageFeature, deptFeature}
				metadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 18, "target")
				return NewDecisionTree(root, features, []string{"yes", "no"}, metadata)
			},
			expectedDepth: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tree := tt.setupTree()
			if tree == nil {
				t.Fatal("failed to setup test tree")
			}

			depth := tree.GetDepth()
			if depth != tt.expectedDepth {
				t.Errorf("expected depth %d, got %d", tt.expectedDepth, depth)
			}
		})
	}
}

func TestDecisionTree_UpdateStatistics(t *testing.T) {
	// Create a tree
	feature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatal("failed to create feature:", err)
	}

	root, err := NewDecisionNode(feature, 30.0, map[interface{}]int{"yes": 10})
	if err != nil {
		t.Fatal("failed to create root:", err)
	}

	leftChild, err := NewLeafNode(map[interface{}]int{"yes": 6})
	if err != nil {
		t.Fatal("failed to create left child:", err)
	}
	rightChild, err := NewLeafNode(map[interface{}]int{"yes": 4})
	if err != nil {
		t.Fatal("failed to create right child:", err)
	}

	root.SetLeftChild(leftChild)
	root.SetRightChild(rightChild)

	metadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 10, "target")
	if metadata == nil {
		t.Fatal("failed to create metadata")
	}

	tree := NewDecisionTree(root, []*Feature{feature}, []string{"yes"}, metadata)
	if tree == nil {
		t.Fatal("failed to create tree")
	}

	// Verify initial statistics
	if tree.Metadata.TotalNodes != 3 {
		t.Errorf("expected 3 total nodes initially, got %d", tree.Metadata.TotalNodes)
	}
	if tree.Metadata.LeafNodes != 2 {
		t.Errorf("expected 2 leaf nodes initially, got %d", tree.Metadata.LeafNodes)
	}

	// Manually modify tree (simulate tree modification)
	// Add another child to left node (this would normally not happen after construction)
	newChild, err := NewLeafNode(map[interface{}]int{"no": 3})
	if err != nil {
		t.Fatal("failed to create new child:", err)
	}

	// For testing, we'll create a new decision node to replace left child
	newDecision, err := NewDecisionNode(feature, 25.0, map[interface{}]int{"yes": 6})
	if err != nil {
		t.Fatal("failed to create new decision:", err)
	}
	newDecision.SetLeftChild(leftChild)
	newDecision.SetRightChild(newChild)
	root.SetLeftChild(newDecision)

	// Update statistics
	tree.updateStatistics()

	// Check updated statistics
	expectedTotal := 5  // root + newDecision + leftChild + newChild + rightChild
	expectedLeaves := 3 // leftChild + newChild + rightChild

	if tree.Metadata.TotalNodes != expectedTotal {
		t.Errorf("expected %d total nodes after update, got %d", expectedTotal, tree.Metadata.TotalNodes)
	}
	if tree.Metadata.LeafNodes != expectedLeaves {
		t.Errorf("expected %d leaf nodes after update, got %d", expectedLeaves, tree.Metadata.LeafNodes)
	}
}

// ====================
// Prediction Tests
// ====================

func TestDecisionTree_Predict(t *testing.T) {
	// Create a test tree: age <= 30 split
	ageFeature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatal("failed to create age feature:", err)
	}

	root, err := NewDecisionNode(ageFeature, 30.0, map[interface{}]int{"approved": 6, "rejected": 4})
	if err != nil {
		t.Fatal("failed to create root node:", err)
	}

	leftChild, err := NewLeafNode(map[interface{}]int{"approved": 5, "rejected": 1}) // Young -> mostly approved
	if err != nil {
		t.Fatal("failed to create left child:", err)
	}
	rightChild, err := NewLeafNode(map[interface{}]int{"approved": 1, "rejected": 3}) // Old -> mostly rejected
	if err != nil {
		t.Fatal("failed to create right child:", err)
	}

	root.SetLeftChild(leftChild)
	root.SetRightChild(rightChild)

	features := []*Feature{ageFeature}
	classes := []string{"approved", "rejected"}
	metadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 10, "target")
	tree := NewDecisionTree(root, features, classes, metadata)

	if tree == nil {
		t.Fatal("failed to create decision tree")
	}

	tests := []struct {
		name               string
		instance           map[string]interface{}
		expectedPrediction interface{}
	}{
		{
			name:               "young person",
			instance:           map[string]interface{}{"age": 25},
			expectedPrediction: "approved",
		},
		{
			name:               "exactly 30",
			instance:           map[string]interface{}{"age": 30},
			expectedPrediction: "approved", // <= 30 goes left
		},
		{
			name:               "older person",
			instance:           map[string]interface{}{"age": 35},
			expectedPrediction: "rejected",
		},
		{
			name:               "very young",
			instance:           map[string]interface{}{"age": 18},
			expectedPrediction: "approved",
		},
		{
			name:               "very old",
			instance:           map[string]interface{}{"age": 65},
			expectedPrediction: "rejected",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prediction := tree.Predict(tt.instance)
			if prediction != tt.expectedPrediction {
				t.Errorf("expected prediction %v, got %v", tt.expectedPrediction, prediction)
			}
		})
	}
}

func TestDecisionTree_Predict_NilRoot(t *testing.T) {
	// Create tree with nil root (invalid tree)
	features := []*Feature{{Name: "test", Type: features.IntegerFeature}}
	metadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 10, "target")

	// Create tree manually with nil root to test error case
	tree := &DecisionTree{
		Root:     nil,
		Features: features,
		Classes:  []string{"yes", "no"},
		Metadata: metadata,
	}

	prediction := tree.Predict(map[string]interface{}{"test": 5})
	if prediction != nil {
		t.Errorf("expected nil prediction for nil root, got %v", prediction)
	}
}

// ====================
// Tree Validation Tests
// ====================

func TestDecisionTree_Validate(t *testing.T) {
	validFeature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatal("failed to create valid feature:", err)
	}
	validRoot, err := NewLeafNode(map[interface{}]int{"yes": 5})
	if err != nil {
		t.Fatal("failed to create valid root:", err)
	}
	validMetadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 5, "target")

	tests := []struct {
		name        string
		setupTree   func() *DecisionTree
		expectValid bool
	}{
		{
			name: "valid tree",
			setupTree: func() *DecisionTree {
				return &DecisionTree{
					Root:     validRoot,
					Features: []*Feature{validFeature},
					Classes:  []string{"yes", "no"},
					Metadata: validMetadata,
				}
			},
			expectValid: true,
		},
		{
			name: "nil root",
			setupTree: func() *DecisionTree {
				return &DecisionTree{
					Root:     nil,
					Features: []*Feature{validFeature},
					Classes:  []string{"yes", "no"},
					Metadata: validMetadata,
				}
			},
			expectValid: false,
		},
		{
			name: "empty features",
			setupTree: func() *DecisionTree {
				return &DecisionTree{
					Root:     validRoot,
					Features: []*Feature{},
					Classes:  []string{"yes", "no"},
					Metadata: validMetadata,
				}
			},
			expectValid: false,
		},
		{
			name: "empty classes",
			setupTree: func() *DecisionTree {
				return &DecisionTree{
					Root:     validRoot,
					Features: []*Feature{validFeature},
					Classes:  []string{},
					Metadata: validMetadata,
				}
			},
			expectValid: false,
		},
		{
			name: "nil metadata",
			setupTree: func() *DecisionTree {
				return &DecisionTree{
					Root:     validRoot,
					Features: []*Feature{validFeature},
					Classes:  []string{"yes", "no"},
					Metadata: nil,
				}
			},
			expectValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tree := tt.setupTree()
			isValid := tree.Validate()

			if tt.expectValid && !isValid {
				t.Error("expected tree to be valid but validation failed")
			}
			if !tt.expectValid && isValid {
				t.Error("expected tree validation to fail but it passed")
			}
		})
	}
}

func TestDecisionTree_Validate_RootValidationFailure(t *testing.T) {
	// Test Validate with root that fails validation (increase Validate coverage)
	validFeature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatal("failed to create valid feature:", err)
	}
	validMetadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 5, "target")

	// Create an invalid root node manually (bypassing constructor validation)
	invalidRoot := &LeafNode{
		Prediction:        nil, // This will cause validation to fail
		ClassDistribution: map[interface{}]int{"yes": 5},
		Samples:           5,
		Confidence:        1.0,
	}

	tree := &DecisionTree{
		Root:     invalidRoot,
		Features: []*Feature{validFeature},
		Classes:  []string{"yes", "no"},
		Metadata: validMetadata,
	}

	isValid := tree.Validate()
	if isValid {
		t.Error("expected tree validation to fail due to invalid root, but it passed")
	}
}

// ====================
// JSON Serialization Tests
// ====================

func TestDecisionTree_JSONSerialization(t *testing.T) {
	// Create test tree
	ageFeature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatal("failed to create age feature:", err)
	}
	salaryFeature, err := NewFeature("salary", features.FloatFeature)
	if err != nil {
		t.Fatal("failed to create salary feature:", err)
	}

	root, err := NewDecisionNode(ageFeature, 30.0, map[interface{}]int{"approved": 8, "rejected": 2})
	if err != nil {
		t.Fatal("failed to create root:", err)
	}

	leftChild, err := NewLeafNode(map[interface{}]int{"approved": 6, "rejected": 1})
	if err != nil {
		t.Fatal("failed to create left child:", err)
	}
	rightChild, err := NewLeafNode(map[interface{}]int{"approved": 2, "rejected": 1})
	if err != nil {
		t.Fatal("failed to create right child:", err)
	}

	root.SetLeftChild(leftChild)
	root.SetRightChild(rightChild)

	features := []*Feature{ageFeature, salaryFeature}
	classes := []string{"approved", "rejected"}
	metadata := NewTreeMetadata("C4.5", 15, 10, "entropy", 100, "target")
	originalTree := NewDecisionTree(root, features, classes, metadata)

	if originalTree == nil {
		t.Fatal("failed to create original tree")
	}

	// Test JSON marshaling
	jsonData, err := json.MarshalIndent(originalTree, "", "  ")
	if err != nil {
		t.Fatalf("failed to marshal tree to JSON: %v", err)
	}

	// Verify JSON contains expected fields
	jsonString := string(jsonData)
	expectedFields := []string{
		`"features"`,
		`"classes"`,
		`"metadata"`,
		`"root"`,
		`"algorithm": "C4.5"`,
		`"max_depth": 15`,
		`"type": "decision"`,
		`"split_value": 30`,
	}

	for _, field := range expectedFields {
		if !strings.Contains(jsonString, field) {
			t.Errorf("JSON missing expected field: %s", field)
		}
	}

	// Test JSON unmarshaling
	var reconstructedTree DecisionTree
	err = json.Unmarshal(jsonData, &reconstructedTree)
	if err != nil {
		t.Fatalf("failed to unmarshal tree from JSON: %v", err)
	}

	// Validate reconstructed tree
	if !reconstructedTree.Validate() {
		t.Error("reconstructed tree failed validation")
	}

	// Check tree structure preservation
	if reconstructedTree.Root == nil {
		t.Error("reconstructed tree has nil root")
	}
	if len(reconstructedTree.Features) != len(originalTree.Features) {
		t.Errorf("feature count mismatch: expected %d, got %d",
			len(originalTree.Features), len(reconstructedTree.Features))
	}
	if len(reconstructedTree.Classes) != len(originalTree.Classes) {
		t.Errorf("class count mismatch: expected %d, got %d",
			len(originalTree.Classes), len(reconstructedTree.Classes))
	}

	// Check metadata preservation
	if reconstructedTree.Metadata.Algorithm != originalTree.Metadata.Algorithm {
		t.Errorf("algorithm mismatch: expected %s, got %s",
			originalTree.Metadata.Algorithm, reconstructedTree.Metadata.Algorithm)
	}
	if reconstructedTree.Metadata.MaxDepth != originalTree.Metadata.MaxDepth {
		t.Errorf("max depth mismatch: expected %d, got %d",
			originalTree.Metadata.MaxDepth, reconstructedTree.Metadata.MaxDepth)
	}

	// Test predictions match
	testInstance := map[string]interface{}{"age": 25, "salary": 50000}
	originalPrediction := originalTree.Predict(testInstance)
	reconstructedPrediction := reconstructedTree.Predict(testInstance)

	if originalPrediction != reconstructedPrediction {
		t.Errorf("prediction mismatch: original=%v, reconstructed=%v",
			originalPrediction, reconstructedPrediction)
	}
}

// ====================
// File Persistence Tests
// ====================
// NOTE: File persistence tests have been moved to internal/io/persistence package
// to maintain proper separation of concerns and avoid import cycles.

/*

func TestDecisionTree_FilePersistence(t *testing.T) {
	// Create test tree
	feature, err := NewFeature("test", features.IntegerFeature)
	if err != nil {
		t.Fatal("failed to create feature:", err)
	}

	root, err := NewLeafNode(map[interface{}]int{"class_A": 10, "class_B": 5})
	if err != nil {
		t.Fatal("failed to create root:", err)
	}

	metadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 15, "target")
	if metadata == nil {
		t.Fatal("failed to create metadata")
	}

	originalTree := NewDecisionTree(root, []*Feature{feature},
		[]string{"class_A", "class_B"}, metadata)
	if originalTree == nil {
		t.Fatal("failed to create tree")
	}

	// Test file save
	filename := "test_tree.json"
	defer os.Remove(filename) // Cleanup

	err = originalTree.SaveTreeToFile(filename)
	if err != nil {
		t.Fatalf("failed to save tree to file: %v", err)
	}

	// Verify file exists
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		t.Error("tree file was not created")
	}

	// Test file load
	loadedTree := LoadTreeFromFile(filename)
	if loadedTree == nil {
		t.Fatal("failed to load tree from file")
	}

	// Validate loaded tree
	if !loadedTree.Validate() {
		t.Error("loaded tree failed validation")
	}

	// Check structure preservation
	if loadedTree.Metadata.Algorithm != originalTree.Metadata.Algorithm {
		t.Error("algorithm not preserved in file persistence")
	}
	if len(loadedTree.Features) != len(originalTree.Features) {
		t.Error("features not preserved in file persistence")
	}
	if len(loadedTree.Classes) != len(originalTree.Classes) {
		t.Error("classes not preserved in file persistence")
	}

	// Test predictions match
	testInstance := map[string]interface{}{"test": 5}
	originalPrediction := originalTree.Predict(testInstance)
	loadedPrediction := loadedTree.Predict(testInstance)

	if originalPrediction != loadedPrediction {
		t.Errorf("prediction mismatch after file persistence: original=%v, loaded=%v",
			originalPrediction, loadedPrediction)
	}
}

func TestDecisionTree_SaveTreeToFile_Errors(t *testing.T) {
	validTree := createValidTestTree()
	if validTree == nil {
		t.Fatal("failed to create valid tree for error testing")
	}

	tests := []struct {
		name     string
		filename string
		tree     *DecisionTree
	}{
		{
			name:     "empty filename",
			filename: "",
			tree:     validTree,
		},
		{
			name:     "invalid tree",
			filename: "test.json",
			tree:     &DecisionTree{}, // Invalid tree
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.tree.SaveTreeToFile(tt.filename)
			if err == nil {
				t.Error("expected error but save succeeded")
			}
		})
	}
}

func TestDecisionTree_SaveTreeToFile_MarshalError(t *testing.T) {
	// Test SaveTreeToFile with tree that causes marshal error (increase SaveTreeToFile coverage)
	tree := createValidTestTree()
	if tree == nil {
		t.Fatal("failed to create valid tree for error testing")
	}

	// Create a tree with a problematic root that might cause marshal issues
	// We'll create a tree with circular reference by setting a child as its own parent
	feature, err := NewFeature("test", features.IntegerFeature)
	if err != nil {
		t.Fatal("failed to create feature:", err)
	}

	root, err := NewDecisionNode(feature, 30.0, map[interface{}]int{"yes": 5})
	if err != nil {
		t.Fatal("failed to create root:", err)
	}

	// This should work fine - the marshal error test is more complex to trigger
	// but we can at least test the path through SaveTreeToFile
	tree.Root = root

	filename := "test_marshal.json"
	defer func() {
		// Clean up regardless of test outcome
		_ = os.Remove(filename)
	}()

	err = tree.SaveTreeToFile(filename)
	if err != nil {
		// This is actually expected to work, but we're testing the error path
		t.Logf("SaveTreeToFile returned error (may be expected): %v", err)
	}
}

func TestLoadTreeFromFile_Errors(t *testing.T) {
	tests := []struct {
		name     string
		filename string
	}{
		{
			name:     "empty filename",
			filename: "",
		},
		{
			name:     "nonexistent file",
			filename: "nonexistent.json",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tree := LoadTreeFromFile(tt.filename)
			if tree != nil {
				t.Error("expected nil tree but load succeeded")
			}
		})
	}

	// Test invalid JSON file
	invalidJSON := "test_invalid.json"
	defer os.Remove(invalidJSON)

	err := os.WriteFile(invalidJSON, []byte("invalid json content"), 0644)
	if err != nil {
		t.Fatalf("failed to create invalid JSON file: %v", err)
	}

	tree := LoadTreeFromFile(invalidJSON)
	if tree != nil {
		t.Error("expected nil tree for invalid JSON but load succeeded")
	}
}

func TestLoadTreeFromFile_ValidationFailure(t *testing.T) {
	// Test LoadTreeFromFile with JSON that unmarshals but fails validation
	invalidTreeJSON := "test_invalid_tree.json"
	defer os.Remove(invalidTreeJSON)

	// Create JSON for a tree that will fail validation
	invalidTreeData := `{
		"features": [],
		"classes": [],
		"metadata": {
			"version": "1.0",
			"created_at": "2023-01-01T00:00:00Z",
			"algorithm": "C4.5",
			"max_depth": 10,
			"min_samples": 20,
			"criterion": "entropy",
			"total_nodes": 0,
			"leaf_nodes": 0,
			"training_samples": 0
		},
		"root": null
	}`

	err := os.WriteFile(invalidTreeJSON, []byte(invalidTreeData), 0644)
	if err != nil {
		t.Fatalf("failed to create invalid tree JSON file: %v", err)
	}

	tree := LoadTreeFromFile(invalidTreeJSON)
	if tree != nil {
		t.Error("expected nil tree for invalid tree structure but load succeeded")
	}
}
*/

// ====================
// Utility Method Tests
// ====================

func TestDecisionTree_GetFeature(t *testing.T) {
	ageFeature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatal("failed to create age feature:", err)
	}
	salaryFeature, err := NewFeature("salary", features.FloatFeature)
	if err != nil {
		t.Fatal("failed to create salary feature:", err)
	}

	tree := createValidTestTree()
	if tree == nil {
		t.Fatal("failed to create test tree")
	}

	// Override features for this test
	tree.Features = []*Feature{ageFeature, salaryFeature}

	tests := []struct {
		name         string
		featureName  string
		expectFound  bool
		expectedType features.FeatureType
	}{
		{
			name:         "existing feature",
			featureName:  "age",
			expectFound:  true,
			expectedType: features.IntegerFeature,
		},
		{
			name:         "another existing feature",
			featureName:  "salary",
			expectFound:  true,
			expectedType: features.FloatFeature,
		},
		{
			name:        "nonexistent feature",
			featureName: "nonexistent",
			expectFound: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			feature := tree.GetFeature(tt.featureName)

			if tt.expectFound {
				if feature == nil {
					t.Error("expected to find feature but got nil")
					return
				}
				if feature.Name != tt.featureName {
					t.Errorf("expected feature name %s, got %s", tt.featureName, feature.Name)
				}
				if feature.Type != tt.expectedType {
					t.Errorf("expected feature type %v, got %v", tt.expectedType, feature.Type)
				}
			} else {
				if feature != nil {
					t.Error("expected nil feature but found feature")
				}
			}
		})
	}
}

func TestDecisionTree_HasClass(t *testing.T) {
	tree := createValidTestTree()
	if tree == nil {
		t.Fatal("failed to create test tree")
	}

	tests := []struct {
		name        string
		class       string
		expectFound bool
	}{
		{
			name:        "existing class",
			class:       "yes",
			expectFound: true,
		},
		{
			name:        "another existing class",
			class:       "no",
			expectFound: true,
		},
		{
			name:        "nonexistent class",
			class:       "unknown",
			expectFound: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hasClass := tree.HasClass(tt.class)
			if hasClass != tt.expectFound {
				t.Errorf("expected HasClass(%s) = %v, got %v", tt.class, tt.expectFound, hasClass)
			}
		})
	}
}

func TestDecisionTree_GetFeatureNames(t *testing.T) {
	ageFeature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatal("failed to create age feature:", err)
	}
	salaryFeature, err := NewFeature("salary", features.FloatFeature)
	if err != nil {
		t.Fatal("failed to create salary feature:", err)
	}
	deptFeature, err := NewFeature("department", features.StringFeature)
	if err != nil {
		t.Fatal("failed to create department feature:", err)
	}

	tree := createValidTestTree()
	if tree == nil {
		t.Fatal("failed to create test tree")
	}

	tree.Features = []*Feature{ageFeature, salaryFeature, deptFeature}

	names := tree.GetFeatureNames()
	expectedNames := []string{"age", "salary", "department"}

	if len(names) != len(expectedNames) {
		t.Errorf("expected %d feature names, got %d", len(expectedNames), len(names))
	}

	for i, expectedName := range expectedNames {
		if i >= len(names) || names[i] != expectedName {
			t.Errorf("expected feature name at index %d to be %s, got %s", i, expectedName, names[i])
		}
	}
}

/*
func TestDecisionTree_SaveTreeToFile_WriteError(t *testing.T) {
	// Test SaveTreeToFile with file write error (increase SaveTreeToFile coverage)
	validTree := createValidTestTree()
	if validTree == nil {
		t.Fatal("failed to create valid tree for error testing")
	}

	// Try to save to a directory that doesn't exist or is read-only
	invalidPath := "/nonexistent/directory/test.json"
	err := validTree.SaveTreeToFile(invalidPath)
	if err == nil {
		t.Error("expected error when saving to invalid path, but got none")
	}
	t.Logf("Expected error for invalid path: %v", err)
}
*/

/*
func TestLoadTreeFromFile_ReadError(t *testing.T) {
	// Test LoadTreeFromFile with file read error (increase LoadTreeFromFile coverage)

	// Create a directory with the same name as the file we want to read
	dirName := "test_directory_not_file"
	err := os.Mkdir(dirName, 0755)
	if err != nil {
		t.Fatalf("failed to create directory: %v", err)
	}
	defer os.RemoveAll(dirName)

	// Try to load a directory as if it were a file
	tree := LoadTreeFromFile(dirName)
	if tree != nil {
		t.Error("expected nil tree when trying to read directory as file, but got tree")
	}
}
*/

func TestNewDecisionTree_AdvancedValidation(t *testing.T) {
	// Test NewDecisionTree with more edge cases to increase coverage
	validFeature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatal("failed to create valid feature:", err)
	}
	validRoot, err := NewLeafNode(map[interface{}]int{"yes": 5})
	if err != nil {
		t.Fatal("failed to create valid root:", err)
	}
	validMetadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 5, "target")

	tests := []struct {
		name        string
		root        Node
		features    []*Feature
		classes     []string
		metadata    *TreeMetadata
		expectError bool
		description string
	}{
		{
			name:        "nil features slice",
			root:        validRoot,
			features:    nil,
			classes:     []string{"yes", "no"},
			metadata:    validMetadata,
			expectError: true,
			description: "should fail with nil features slice",
		},
		{
			name:        "nil classes slice",
			root:        validRoot,
			features:    []*Feature{validFeature},
			classes:     nil,
			metadata:    validMetadata,
			expectError: true,
			description: "should fail with nil classes slice",
		},
		{
			name:        "features with nil feature",
			root:        validRoot,
			features:    []*Feature{validFeature, nil},
			classes:     []string{"yes", "no"},
			metadata:    validMetadata,
			expectError: false, // This might not be caught by NewDecisionTree
			description: "should handle features slice with nil feature",
		},
		{
			name:        "classes with empty string",
			root:        validRoot,
			features:    []*Feature{validFeature},
			classes:     []string{"yes", "", "no"},
			metadata:    validMetadata,
			expectError: false, // This might not be caught by NewDecisionTree
			description: "should handle classes with empty string",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tree := NewDecisionTree(tt.root, tt.features, tt.classes, tt.metadata)

			if tt.expectError {
				if tree != nil {
					t.Errorf("expected nil tree but got tree (%s)", tt.description)
				}
			} else {
				if tree == nil {
					t.Errorf("expected tree but got nil (%s)", tt.description)
				}
			}
			t.Logf("%s: success", tt.description)
		})
	}
}

/*
func TestSaveTreeToFile_MarshalEdgeCases(t *testing.T) {
	// Test SaveTreeToFile with edge cases to increase coverage
	validTree := createValidTestTree()
	if validTree == nil {
		t.Fatal("failed to create valid tree for testing")
	}

	// Test with a very long filename to potentially trigger different error paths
	longFilename := "test_" + strings.Repeat("a", 200) + ".json"
	defer func() {
		// Clean up regardless of test outcome
		_ = os.Remove(longFilename)
	}()

	err := validTree.SaveTreeToFile(longFilename)
	if err != nil {
		t.Logf("SaveTreeToFile with long filename returned error (may be expected): %v", err)
	} else {
		t.Logf("SaveTreeToFile with long filename succeeded")
		// Verify file was created
		if _, statErr := os.Stat(longFilename); statErr != nil {
			t.Errorf("file was not created despite success: %v", statErr)
		}
	}
}
*/

/*
func TestLoadTreeFromFile_CorruptedJSON(t *testing.T) {
	// Test LoadTreeFromFile with various corrupted JSON scenarios
	tests := []struct {
		name        string
		jsonContent string
		description string
	}{
		{
			name:        "truncated_json",
			jsonContent: `{"features": [], "classes": [], "metadata": {"version": "1.0"`,
			description: "should handle truncated JSON",
		},
		{
			name:        "json_with_invalid_unicode",
			jsonContent: `{"features": [], "classes": [], "metadata": {"version": "\uDEAD"}}`,
			description: "should handle JSON with invalid unicode",
		},
		{
			name:        "json_with_circular_reference_simulation",
			jsonContent: `{"features": [], "classes": [], "root": {"type": "leaf", "prediction": null}}`,
			description: "should handle JSON that would create invalid tree structure",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filename := "test_" + tt.name + ".json"
			defer os.Remove(filename)

			err := os.WriteFile(filename, []byte(tt.jsonContent), 0644)
			if err != nil {
				t.Fatalf("failed to create test file: %v", err)
			}

			tree := LoadTreeFromFile(filename)
			if tree != nil {
				t.Errorf("expected nil tree for corrupted JSON (%s), but got tree", tt.description)
			}
			t.Logf("%s: correctly returned nil", tt.description)
		})
	}
}
*/

func TestDecisionTree_Predict_EdgeCases(t *testing.T) {
	// Test DecisionTree.Predict with edge cases to increase coverage
	tests := []struct {
		name        string
		setupTree   func() *DecisionTree
		sample      map[string]interface{}
		expectError bool
		description string
	}{
		{
			name: "tree with nil root",
			setupTree: func() *DecisionTree {
				return &DecisionTree{
					Root:     nil,
					Features: []*Feature{{Name: "test", Type: features.IntegerFeature}},
					Classes:  []string{"yes", "no"},
					Metadata: NewTreeMetadata("C4.5", 10, 20, "entropy", 5, "target"),
				}
			},
			sample:      map[string]interface{}{"test": 5},
			expectError: true,
			description: "should fail prediction with nil root",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tree := tt.setupTree()
			prediction := tree.Predict(tt.sample)

			if tt.expectError {
				if prediction != nil {
					t.Errorf("expected nil prediction but got %v (%s)", prediction, tt.description)
				}
			} else {
				if prediction == nil {
					t.Errorf("expected prediction but got nil (%s)", tt.description)
				}
			}
			t.Logf("%s: success", tt.description)
		})
	}
}

func TestNewTreeMetadata_EdgeCases(t *testing.T) {
	// Test NewTreeMetadata with edge cases to increase coverage
	tests := []struct {
		name            string
		algorithm       string
		maxDepth        int
		minSamples      int
		criterion       string
		trainingSamples int
		expectNil       bool
		description     string
	}{
		{
			name:            "empty algorithm",
			algorithm:       "",
			maxDepth:        10,
			minSamples:      20,
			criterion:       "entropy",
			trainingSamples: 100,
			expectNil:       true,
			description:     "should return nil for empty algorithm",
		},
		{
			name:            "zero max depth",
			algorithm:       "C4.5",
			maxDepth:        0,
			minSamples:      20,
			criterion:       "entropy",
			trainingSamples: 100,
			expectNil:       true,
			description:     "should return nil for zero max depth",
		},
		{
			name:            "negative min samples",
			algorithm:       "C4.5",
			maxDepth:        10,
			minSamples:      -5,
			criterion:       "entropy",
			trainingSamples: 100,
			expectNil:       true,
			description:     "should return nil for negative min samples",
		},
		{
			name:            "negative training samples",
			algorithm:       "C4.5",
			maxDepth:        10,
			minSamples:      20,
			criterion:       "entropy",
			trainingSamples: -50,
			expectNil:       true,
			description:     "should return nil for negative training samples",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			metadata := NewTreeMetadata(tt.algorithm, tt.maxDepth, tt.minSamples, tt.criterion, tt.trainingSamples, "target")

			if tt.expectNil {
				if metadata != nil {
					t.Errorf("expected nil metadata but got metadata (%s)", tt.description)
				}
			} else {
				if metadata == nil {
					t.Errorf("expected metadata but got nil (%s)", tt.description)
				}
			}
			t.Logf("%s: success", tt.description)
		})
	}
}

func TestNewDecisionTree_ComprehensiveValidation(t *testing.T) {
	// Test NewDecisionTree with comprehensive validation to increase coverage
	validFeature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		t.Fatal("failed to create valid feature:", err)
	}
	validRoot, err := NewLeafNode(map[interface{}]int{"yes": 5})
	if err != nil {
		t.Fatal("failed to create valid root:", err)
	}
	validMetadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 5, "target")

	tests := []struct {
		name        string
		root        Node
		features    []*Feature
		classes     []string
		metadata    *TreeMetadata
		expectNil   bool
		description string
	}{
		{
			name:        "nil root",
			root:        nil,
			features:    []*Feature{validFeature},
			classes:     []string{"yes", "no"},
			metadata:    validMetadata,
			expectNil:   true,
			description: "should return nil for nil root",
		},
		{
			name:        "empty features",
			root:        validRoot,
			features:    []*Feature{},
			classes:     []string{"yes", "no"},
			metadata:    validMetadata,
			expectNil:   true,
			description: "should return nil for empty features",
		},
		{
			name:        "empty classes",
			root:        validRoot,
			features:    []*Feature{validFeature},
			classes:     []string{},
			metadata:    validMetadata,
			expectNil:   true,
			description: "should return nil for empty classes",
		},
		{
			name:        "nil metadata",
			root:        validRoot,
			features:    []*Feature{validFeature},
			classes:     []string{"yes", "no"},
			metadata:    nil,
			expectNil:   true,
			description: "should return nil for nil metadata",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tree := NewDecisionTree(tt.root, tt.features, tt.classes, tt.metadata)

			if tt.expectNil {
				if tree != nil {
					t.Errorf("expected nil tree but got tree (%s)", tt.description)
				}
			} else {
				if tree == nil {
					t.Errorf("expected tree but got nil (%s)", tt.description)
				}
			}
			t.Logf("%s: success", tt.description)
		})
	}
}

func TestDecisionTree_GetTreeSummary(t *testing.T) {
	tree := createValidTestTree()
	if tree == nil {
		t.Fatal("failed to create test tree")
	}

	summary := tree.GetTreeSummary()

	// Check that summary contains expected information
	expectedStrings := []string{
		"Decision Tree Summary:",
		"Algorithm: C4.5",
		"Features: ",
		"Classes: ",
		"Total Nodes: ",
		"Leaf Nodes: ",
		"Tree Depth: ",
		"Training Samples: ",
	}

	for _, expected := range expectedStrings {
		if !strings.Contains(summary, expected) {
			t.Errorf("tree summary missing expected string: %s", expected)
		}
	}
}

func TestDecisionTree_GetTreeSummary_EmptyTree(t *testing.T) {
	// Test with nil root
	tree := &DecisionTree{Root: nil}
	summary := tree.GetTreeSummary()

	if summary != "Empty Decision Tree" {
		t.Errorf("expected 'Empty Decision Tree', got: %s", summary)
	}
}

func TestDecisionTree_UpdateStatistics_NilRoot(t *testing.T) {
	// Test updateStatistics with nil root (increase updateStatistics coverage)
	tree := &DecisionTree{
		Root:     nil,
		Features: []*Feature{{Name: "test", Type: features.IntegerFeature}},
		Classes:  []string{"yes", "no"},
		Metadata: &TreeMetadata{},
	}

	tree.updateStatistics()

	if tree.Metadata.TotalNodes != 0 {
		t.Errorf("expected 0 total nodes for nil root, got %d", tree.Metadata.TotalNodes)
	}
	if tree.Metadata.LeafNodes != 0 {
		t.Errorf("expected 0 leaf nodes for nil root, got %d", tree.Metadata.LeafNodes)
	}
}

func TestDecisionTree_UnmarshalJSON_ErrorCases(t *testing.T) {
	// Test UnmarshalJSON error cases to increase coverage
	tests := []struct {
		name     string
		jsonData string
	}{
		{
			name:     "invalid JSON",
			jsonData: `{"invalid": json}`,
		},
		{
			name:     "tree with invalid root",
			jsonData: `{"root": {"type": "invalid"}, "features": [], "classes": [], "metadata": {}}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var tree DecisionTree
			err := tree.UnmarshalJSON([]byte(tt.jsonData))
			if err == nil {
				t.Error("expected error for invalid JSON, but got none")
			}
		})
	}
}

// ====================
// Helper Functions for Tests
// ====================

func createValidTestTree() *DecisionTree {
	feature, err := NewFeature("test", features.IntegerFeature)
	if err != nil {
		return nil
	}

	root, err := NewLeafNode(map[interface{}]int{"yes": 6, "no": 4})
	if err != nil {
		return nil
	}

	metadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 10, "target")
	if metadata == nil {
		return nil
	}

	return NewDecisionTree(root, []*Feature{feature}, []string{"yes", "no"}, metadata)
}

// ====================
// Version Analysis Tests
// ====================

// ====================
// Performance Tests
// ====================

func BenchmarkDecisionTree_Predict(b *testing.B) {
	// Create a moderately deep tree for benchmarking
	ageFeature, err := NewFeature("age", features.IntegerFeature)
	if err != nil {
		b.Fatal("failed to create feature:", err)
	}

	// Create 3-level tree
	root, err := NewDecisionNode(ageFeature, 50.0, map[interface{}]int{"approved": 100, "rejected": 50})
	if err != nil {
		b.Fatal("failed to create root:", err)
	}

	leftDecision, err := NewDecisionNode(ageFeature, 30.0, map[interface{}]int{"approved": 80, "rejected": 20})
	if err != nil {
		b.Fatal("failed to create left decision:", err)
	}
	rightDecision, err := NewDecisionNode(ageFeature, 60.0, map[interface{}]int{"approved": 20, "rejected": 30})
	if err != nil {
		b.Fatal("failed to create right decision:", err)
	}

	// Add leaf children
	ll, err := NewLeafNode(map[interface{}]int{"approved": 60, "rejected": 5})
	if err != nil {
		b.Fatal("failed to create ll leaf:", err)
	}
	lr, err := NewLeafNode(map[interface{}]int{"approved": 20, "rejected": 15})
	if err != nil {
		b.Fatal("failed to create lr leaf:", err)
	}
	rl, err := NewLeafNode(map[interface{}]int{"approved": 15, "rejected": 10})
	if err != nil {
		b.Fatal("failed to create rl leaf:", err)
	}
	rr, err := NewLeafNode(map[interface{}]int{"rejected": 20, "approved": 5})
	if err != nil {
		b.Fatal("failed to create rr leaf:", err)
	}

	leftDecision.SetLeftChild(ll)
	leftDecision.SetRightChild(lr)
	rightDecision.SetLeftChild(rl)
	rightDecision.SetRightChild(rr)

	root.SetLeftChild(leftDecision)
	root.SetRightChild(rightDecision)

	metadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 150, "target")
	tree := NewDecisionTree(root, []*Feature{ageFeature}, []string{"approved", "rejected"}, metadata)

	if tree == nil {
		b.Fatal("failed to create tree")
	}

	instance := map[string]interface{}{"age": 25}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		tree.Predict(instance)
	}
}

func BenchmarkDecisionTree_JSONSerialization(b *testing.B) {
	tree := createValidTestTree()
	if tree == nil {
		b.Fatal("failed to create test tree")
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := json.Marshal(tree)
		if err != nil {
			b.Fatalf("serialization failed: %v", err)
		}
	}
}
