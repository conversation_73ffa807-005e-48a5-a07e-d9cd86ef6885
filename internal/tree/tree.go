// Package tree provides DecisionTree container for Mulberri decision tree implementation.
//
// Architecture:
//   - tree.go: DecisionTree container with metadata and persistence
//   - TreeMetadata: Training parameters and tree statistics
//   - JSON serialization: Complete tree save/load functionality
//
// Design Principles:
//   - DecisionTree acts as container for root node and metadata
//   - Automatic statistics calculation and maintenance
//   - JSON persistence with complete tree structure preservation
//   - Immutable metadata after construction with update capabilities
//
// Memory Management:
//   - Tree stores single root node reference plus lightweight metadata
//   - Node tree structure handled by Node implementations
//   - Metadata cached and updated on demand for performance
//
// Example:
//
//	// Create tree with trained root node
//	metadata := NewTreeMetadata("C4.5", 10, 20)
//	tree := NewDecisionTree(rootNode, features, classes, metadata)
//
//	// Save tree to file
//	tree.SaveTreeToFile("model.json")
//
//	// Load tree from file
//	loadedTree := LoadTreeFromFile("model.json")
//
// Security: No sensitive data storage, validates tree consistency.
// Performance: O(1) tree operations, O(log depth) prediction traversal.
// Dependencies: Node interface, features package, logger, JSON marshaling.
package tree

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/berrijam/mulberri/internal/utils/errors"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// ====================
// Tree Metadata
// ====================

// TreeMetadata contains comprehensive information about decision tree training and structure.
//
// Stores training parameters, algorithm details, tree statistics, and creation timestamp.
// Provides complete context for tree evaluation, debugging, and model management.
//
// Constraints:
//   - Version must be non-empty for tracking compatibility
//   - Algorithm should specify C4.5 or future algorithm variants
//   - MaxDepth and MinSamples must be positive for valid training constraints
//   - Node counts automatically calculated and maintained for accuracy
//
// Security: Contains training configuration but no sensitive training data.
// Performance: Lightweight metadata structure with O(1) access to all fields.
// Relationships: Used by DecisionTree for complete model documentation.
// Side effects: Immutable after creation, statistics updated via tree operations.
type TreeMetadata struct {
	CreatedAt       time.Time `json:"created_at"`       // Tree creation timestamp
	Algorithm       string    `json:"algorithm"`        // Training algorithm used (C4.5)
	MaxDepth        int       `json:"max_depth"`        // Maximum tree depth constraint from training
	MinSamples      int       `json:"min_samples"`      // Minimum samples required for split from training
	Criterion       string    `json:"criterion"`        // Split criterion used (entropy, gini)
	TotalNodes      int       `json:"total_nodes"`      // Total nodes in tree (calculated)
	LeafNodes       int       `json:"leaf_nodes"`       // Number of leaf nodes (calculated)
	TrainingSamples int       `json:"training_samples"` // Number of training instances used
	TargetColumn    string    `json:"target_column"`    // Name of target column from training data
}

// ====================
// TreeMetadata Constructor
// ====================

// NewTreeMetadata creates TreeMetadata with specified training parameters.
//
// Args:
//   - algorithm: Training algorithm identifier (typically "C4.5")
//   - maxDepth: Maximum tree depth constraint used during training
//   - minSamples: Minimum samples required for node splitting during training
//   - criterion: Split criterion used for node evaluation (entropy, gini)
//   - trainingSamples: Number of training instances used to build tree
//   - targetColumn: Name of target column from training data
//
// Returns new TreeMetadata with current timestamp and default statistics.
// Constraints: algorithm non-empty, maxDepth/minSamples positive, trainingSamples >= 0.
// Performance: O(1) creation with timestamp generation.
// Relationships: Created during tree construction with training configuration.
// Side effects: Records current timestamp, initializes statistics to zero for later update.
//
// Example: metadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 1000, "approved")
func NewTreeMetadata(algorithm string, maxDepth int, minSamples int, criterion string, trainingSamples int, targetColumn string) *TreeMetadata {
	if algorithm == "" {
		logger.Error("algorithm cannot be empty")
		return nil
	}
	if maxDepth <= 0 {
		logger.Error("maxDepth must be positive")
		return nil
	}
	if minSamples <= 0 {
		logger.Error("minSamples must be positive")
		return nil
	}
	if trainingSamples < 0 {
		logger.Error("trainingSamples cannot be negative")
		return nil
	}

	return &TreeMetadata{
		CreatedAt:       time.Now(),
		Algorithm:       algorithm,
		MaxDepth:        maxDepth,
		MinSamples:      minSamples,
		Criterion:       criterion,
		TotalNodes:      0, // Will be calculated by tree
		LeafNodes:       0, // Will be calculated by tree
		TrainingSamples: trainingSamples,
		TargetColumn:    targetColumn,
	}
}

// ====================
// DecisionTree Container
// ====================

// DecisionTree represents complete decision tree with root node and comprehensive metadata.
//
// Acts as container for trained tree structure with feature information, target classes,
// and training metadata. Provides tree-level operations including persistence, validation,
// and statistics management.
//
// Constraints:
//   - Root must be valid Node implementation for functional tree
//   - Features must match those used in training dataset
//   - Classes must represent all possible target values
//   - Metadata must contain valid training configuration and statistics
//
// Security: Contains tree structure and metadata but no raw training data.
// Performance: O(1) access to tree components, O(tree_size) for persistence operations.
// Relationships: Contains root Node, Feature definitions, and TreeMetadata.
// Side effects: Maintains tree statistics, handles JSON serialization lifecycle.
type DecisionTree struct {
	Root     Node          `json:"-"`        // Root node (not directly serialized)
	Features []*Feature    `json:"features"` // Feature definitions used in tree
	Classes  []string      `json:"classes"`  // Target class values
	Metadata *TreeMetadata `json:"metadata"` // Training parameters and tree statistics

	// Serialization support
	SerializableRoot *SerializableNode `json:"root"` // For JSON serialization
}

// NewDecisionTree creates DecisionTree with root node and metadata.
//
// Args:
//   - root: Trained root node of decision tree (must be valid Node implementation)
//   - features: Feature definitions used in training (must match training dataset)
//   - classes: Target class values from training data
//   - metadata: Training configuration and parameters
//
// Returns new DecisionTree with calculated statistics or nil for invalid input.
// Constraints: root must be valid, features/classes non-empty, metadata valid.
// Performance: O(tree_size) for initial statistics calculation.
// Relationships: Combines trained node structure with training metadata.
// Side effects: Calculates and caches tree statistics in metadata.
//
// Example: tree := NewDecisionTree(rootNode, featureList, classList, metadata)
func NewDecisionTree(root Node, features []*Feature, classes []string, metadata *TreeMetadata) *DecisionTree {
	if root == nil {
		logger.Error("decision tree requires a root node")
		return nil
	}
	if len(features) == 0 {
		logger.Error("decision tree requires feature definitions")
		return nil
	}
	if len(classes) == 0 {
		logger.Error("decision tree requires target classes")
		return nil
	}
	if metadata == nil {
		logger.Error("decision tree requires metadata")
		return nil
	}
	if !root.Validate() {
		logger.Error("root node validation failed")
		return nil
	}

	tree := &DecisionTree{
		Root:     root,
		Features: features,
		Classes:  classes,
		Metadata: metadata,
	}

	// Calculate and update tree statistics
	tree.updateStatistics()

	return tree
}

// updateStatistics calculates current tree statistics and updates metadata.
//
// Traverses entire tree to count total nodes, leaf nodes, and calculate tree depth.
// Updates metadata fields with current statistics for accurate tree information.
// Performance: O(tree_size) for complete tree traversal and counting.
// Relationships: Called during tree construction and after tree modifications.
// Side effects: Modifies metadata statistics fields with current tree state.
func (dt *DecisionTree) updateStatistics() {
	if dt.Root == nil {
		dt.Metadata.TotalNodes = 0
		dt.Metadata.LeafNodes = 0
		return
	}

	dt.Metadata.TotalNodes = CountNodes(dt.Root)
	dt.Metadata.LeafNodes = CountLeaves(dt.Root)
}

// GetDepth calculates maximum depth of decision tree.
//
// Returns maximum path length from root to any leaf node.
// Performance: O(tree_size) for complete depth-first traversal.
// Relationships: Uses recursive tree structure for depth calculation.
// Side effects: None, read-only tree traversal operation.
//
// Example: depth := tree.GetDepth()
func (dt *DecisionTree) GetDepth() int {
	return calculateDepth(dt.Root)
}

// calculateDepth recursively calculates tree depth from given node.
//
// Args:
//   - node: Current node in tree traversal (can be leaf or decision node)
//
// Returns maximum depth from this node to any leaf in subtree.
// Performance: O(subtree_size) for recursive traversal.
// Relationships: Helper function for GetDepth, used recursively.
// Side effects: None, read-only recursive tree traversal.
func calculateDepth(node Node) int {
	if node == nil || node.IsLeaf() {
		return 0
	}

	// Use type switch for safe node type handling
	switch n := node.(type) {
	case *DecisionNode:
		maxChildDepth := 0
		for _, child := range n.Children {
			childDepth := calculateDepth(child)
			if childDepth > maxChildDepth {
				maxChildDepth = childDepth
			}
		}
		return maxChildDepth + 1
	default:
		// Unknown node type that's not a leaf - treat as leaf
		return 0
	}
}

// DetailedPredictionResult contains comprehensive prediction information.
//
// This structure provides detailed information about how a prediction was made,
// including the decision path, confidence, and class distribution for transparency.
type DetailedPredictionResult struct {
	// Prediction is the final predicted class
	Prediction interface{}

	// Confidence indicates the confidence level of the prediction (0.0 to 1.0)
	Confidence float64

	// DecisionPath contains the sequence of decisions made during tree traversal
	DecisionPath []string

	// DecisionRule contains the complete rule in human-readable format
	DecisionRule string

	// ClassDistribution shows the class distribution at the leaf node
	ClassDistribution map[interface{}]int

	// LeafNode is the final leaf node reached during traversal
	LeafNode Node
}

// Predict makes prediction for single instance using tree traversal.
//
// Args:
//   - instance: Feature values for prediction (map of feature_name -> value)
//
// Returns predicted class value by traversing tree from root to leaf.
// Constraints: instance must contain values for features used in tree splits.
// Performance: O(tree_depth) for single prediction via tree traversal.
// Relationships: Delegates to root node's Predict method for actual traversal.
// Side effects: None, read-only tree traversal for prediction.
//
// Example: prediction := tree.Predict(map[string]interface{}{"age": 25, "salary": 50000})
func (dt *DecisionTree) Predict(instance map[string]interface{}) interface{} {
	if dt.Root == nil {
		logger.Error("cannot predict with nil root node")
		return nil
	}

	prediction, _, _ := dt.Root.Predict(instance)
	return prediction
}

// PredictDetailed makes detailed prediction for single instance with decision path tracking.
//
// Args:
//   - instance: Feature values for prediction (map of feature_name -> value)
//
// Returns detailed prediction result including path, confidence, and class distribution.
// Constraints: instance must contain values for features used in tree splits.
// Performance: O(tree_depth) for single prediction via tree traversal.
// Relationships: Uses predictWithPath for detailed traversal tracking.
// Side effects: None, read-only tree traversal for prediction.
//
// Example: result := tree.PredictDetailed(map[string]interface{}{"age": 25, "salary": 50000})
func (dt *DecisionTree) PredictDetailed(instance map[string]interface{}) *DetailedPredictionResult {
	if dt.Root == nil {
		logger.Error("cannot predict with nil root node")
		return dt.createErrorPredictionResult(nil, []string{}, "ERROR: nil root node", "ERROR: nil root node")
	}

	return dt.predictWithPath(dt.Root, instance, []string{})
}

// predictWithPath recursively traverses the tree while tracking the decision path.
//
// Args:
//   - node: Current node in the traversal
//   - instance: Feature values for prediction
//   - path: Current decision path (accumulated during traversal)
//
// Returns detailed prediction result with complete decision path and metadata.
// Performance: O(tree_depth) for single prediction via tree traversal.
// Side effects: None, read-only tree traversal with path tracking.
func (dt *DecisionTree) predictWithPath(node Node, instance map[string]interface{}, path []string) *DetailedPredictionResult {
	if node == nil {
		return dt.createErrorPredictionResult(nil, path, "ERROR: nil node", "ERROR: nil node")
	}

	// Use type switch for safe node type handling
	switch n := node.(type) {
	case *LeafNode:
		// Handle leaf node
		prediction := n.GetMajorityClass()
		confidence := n.GetConfidence()
		classDistribution := n.GetClassDistribution()

		// Add leaf information to path
		leafDescription := fmt.Sprintf("LEAF[%v] (confidence: %.3f)", prediction, confidence)
		finalPath := append(path, leafDescription)

		// Create decision rule from path
		decisionRule := dt.formatDecisionRule(finalPath, prediction)

		return dt.createDetailedPredictionResult(
			prediction,
			confidence,
			finalPath,
			decisionRule,
			classDistribution,
			n,
		)

	case *DecisionNode:
		// Handle decision node - process the split logic
		decisionNode := n

		// Get feature value from instance
		featureValue, exists := instance[decisionNode.Feature.Name]
		if !exists {
			// Feature missing - use majority class from current node
			prediction := decisionNode.GetMajorityClass()
			missingMessage := fmt.Sprintf("MISSING[%s] -> %v", decisionNode.Feature.Name, prediction)
			return dt.createErrorPredictionResult(decisionNode, path, missingMessage, "")
		}

		// Determine which child to traverse to and create decision description
		var branchKey interface{}
		var decisionDescription string

		if decisionNode.Feature.IsNumerical() {
			// Numerical split: compare with threshold
			var numValue float64
			switch v := featureValue.(type) {
			case int:
				numValue = float64(v)
			case int64:
				numValue = float64(v)
			case float64:
				numValue = v
			case float32:
				numValue = float64(v)
			default:
				// Invalid type - use majority class
				errorMessage := fmt.Sprintf("ERROR: invalid type for %s: %T", decisionNode.Feature.Name, featureValue)
				return dt.createErrorPredictionResult(decisionNode, path, errorMessage, "")
			}

			threshold, ok := decisionNode.SplitValue.(float64)
			if !ok {
				// Invalid threshold - use majority class
				errorMessage := fmt.Sprintf("ERROR: invalid threshold for %s", decisionNode.Feature.Name)
				return dt.createErrorPredictionResult(decisionNode, path, errorMessage, "")
			}

			if numValue <= threshold {
				branchKey = LeftBranch
				decisionDescription = fmt.Sprintf("%s <= %.6f", decisionNode.Feature.Name, threshold)
			} else {
				branchKey = RightBranch
				decisionDescription = fmt.Sprintf("%s > %.6f", decisionNode.Feature.Name, threshold)
			}
		} else {
			// Categorical split: n-ary split using actual feature value as branch key
			branchKey = featureValue
			decisionDescription = fmt.Sprintf("%s = %v", decisionNode.Feature.Name, featureValue)
		}

		// Get child node and recurse
		child := decisionNode.GetChild(branchKey)
		if child == nil {
			// No child found - use majority class from current node
			prediction := decisionNode.GetMajorityClass()
			pathWithDecision := append(path, decisionDescription)
			noChildMessage := fmt.Sprintf("NO_CHILD -> %v", prediction)
			return dt.createErrorPredictionResult(decisionNode, pathWithDecision, noChildMessage, "")
		}

		// Add current decision to path and recurse to child
		newPath := append(path, decisionDescription)
		return dt.predictWithPath(child, instance, newPath)

	default:
		// Handle unknown node types
		errorMessage := fmt.Sprintf("ERROR: unknown node type %T", node)
		return dt.createErrorPredictionResult(node, path, errorMessage, "ERROR: unknown node type")
	}
}

// createDetailedPredictionResult creates a DetailedPredictionResult with common parameters.
//
// Args:
//   - prediction: The predicted class value
//   - confidence: Prediction confidence [0.0, 1.0]
//   - decisionPath: Sequence of decisions made during tree traversal
//   - decisionRule: Human-readable decision rule
//   - classDistribution: Class distribution at the prediction node
//   - leafNode: The node where prediction was made
//
// Returns new DetailedPredictionResult with provided parameters.
// Performance: O(1) struct creation.
// Side effects: None, pure constructor function.
func (dt *DecisionTree) createDetailedPredictionResult(
	prediction interface{},
	confidence float64,
	decisionPath []string,
	decisionRule string,
	classDistribution map[interface{}]int,
	leafNode Node,
) *DetailedPredictionResult {
	return &DetailedPredictionResult{
		Prediction:        prediction,
		Confidence:        confidence,
		DecisionPath:      decisionPath,
		DecisionRule:      decisionRule,
		ClassDistribution: classDistribution,
		LeafNode:          leafNode,
	}
}

// createErrorPredictionResult creates a DetailedPredictionResult for error cases.
//
// Args:
//   - node: The node where the error occurred (can be nil)
//   - path: Current decision path
//   - errorMessage: Error description to append to path
//   - decisionRule: Rule description for the error case
//
// Returns DetailedPredictionResult with error information and majority class fallback.
// Performance: O(1) for node statistics retrieval plus O(path_length) for path operations.
// Side effects: None, uses node's majority class as fallback prediction.
func (dt *DecisionTree) createErrorPredictionResult(
	node Node,
	path []string,
	errorMessage string,
	decisionRule string,
) *DetailedPredictionResult {
	var prediction interface{}
	var confidence float64
	var classDistribution map[interface{}]int

	if node != nil {
		prediction = node.GetMajorityClass()
		confidence = node.GetConfidence()
		classDistribution = node.GetClassDistribution()
	} else {
		prediction = nil
		confidence = 0.0
		classDistribution = make(map[interface{}]int)
	}

	errorPath := append(path, errorMessage)
	if decisionRule == "" {
		decisionRule = dt.formatDecisionRule(errorPath, prediction)
	}

	return dt.createDetailedPredictionResult(
		prediction,
		confidence,
		errorPath,
		decisionRule,
		classDistribution,
		node,
	)
}

// formatDecisionRule creates a human-readable decision rule from the decision path.
//
// Args:
//   - path: Sequence of decisions made during tree traversal
//   - prediction: Final prediction result
//
// Returns formatted decision rule string for human interpretation.
// Performance: O(path_length) for string concatenation.
// Side effects: None, pure formatting function.
func (dt *DecisionTree) formatDecisionRule(path []string, prediction interface{}) string {
	if len(path) == 0 {
		return fmt.Sprintf("EMPTY_PATH -> %v", prediction)
	}

	// Filter out leaf and error descriptions for the rule conditions
	conditions := []string{}
	for _, step := range path {
		// Skip leaf nodes and error messages in the rule conditions
		if !strings.HasPrefix(step, "LEAF[") &&
			!strings.HasPrefix(step, "ERROR:") &&
			!strings.HasPrefix(step, "MISSING[") &&
			!strings.HasPrefix(step, "NO_CHILD") {
			conditions = append(conditions, step)
		}
	}

	if len(conditions) == 0 {
		// No conditions, just return the final result
		return fmt.Sprintf("DEFAULT -> %v", prediction)
	}

	// Join conditions with " AND " and add the prediction
	rule := fmt.Sprintf("IF %s THEN %v", strings.Join(conditions, " AND "), prediction)

	return rule
}

// Validate checks complete tree consistency including root, features, and metadata.
//
// Returns true if tree is valid and ready for predictions, false otherwise.
// Performance: O(tree_size) for complete tree validation including all nodes.
// Relationships: Validates root node and all children recursively.
// Side effects: Logs validation errors for debugging, read-only validation.
func (dt *DecisionTree) Validate() bool {
	if dt.Root == nil {
		logger.Error("decision tree must have a root node")
		return false
	}
	if len(dt.Features) == 0 {
		logger.Error("decision tree must have feature definitions")
		return false
	}
	if len(dt.Classes) == 0 {
		logger.Error("decision tree must have target classes")
		return false
	}
	if dt.Metadata == nil {
		logger.Error("decision tree must have metadata")
		return false
	}

	// Validate root node and entire tree structure
	if !dt.Root.Validate() {
		logger.Error("root node validation failed")
		return false
	}

	return true
}

// ====================
// JSON Serialization Support
// ====================

// MarshalJSON implements custom JSON marshaling for DecisionTree.
//
// Converts root node to SerializableNode format and marshals complete tree structure.
// Preserves all tree information including node hierarchy and metadata.
// Performance: O(tree_size) for complete tree serialization.
// Relationships: Uses root node's ToSerializable() for node tree conversion.
// Side effects: Updates SerializableRoot field for JSON output.
func (dt *DecisionTree) MarshalJSON() ([]byte, error) {
	// Convert root to serializable format for JSON compatibility
	if dt.Root != nil {
		dt.SerializableRoot = dt.Root.ToSerializable()
	}

	// Create temporary struct for marshaling to avoid infinite recursion
	type TreeAlias DecisionTree
	return json.MarshalIndent((*TreeAlias)(dt), "", "  ")
}

// UnmarshalJSON implements custom JSON unmarshaling for DecisionTree.
//
// Reconstructs tree structure from JSON including root node conversion from SerializableNode.
// Restores complete tree functionality from persisted JSON representation.
// Performance: O(tree_size) for complete tree reconstruction.
// Relationships: Uses FromSerializable() to convert serialized nodes back to Node interface.
// Side effects: Reconstructs root Node from SerializableRoot, validates tree consistency.
func (dt *DecisionTree) UnmarshalJSON(data []byte) error {
	// Create temporary struct for unmarshaling to avoid infinite recursion
	type TreeAlias DecisionTree
	aux := (*TreeAlias)(dt)

	if err := json.Unmarshal(data, aux); err != nil {
		return errors.LogErrorAndReturn("failed to unmarshal tree JSON", err)
	}

	// Convert serializable root back to Node interface
	if dt.SerializableRoot != nil {
		root, err := FromSerializable(dt.SerializableRoot)
		if err != nil {
			return errors.LogErrorAndReturn("failed to deserialize root node", err)
		}
		dt.Root = root
	}

	// Validate reconstructed tree
	if !dt.Validate() {
		logger.Error("deserialized tree validation failed")
		return fmt.Errorf("invalid tree structure in JSON")
	}

	return nil
}

// ReconstructFromSerialization reconstructs the tree's Root node from SerializableRoot.
//
// Converts the SerializableRoot back to a proper Node interface implementation.
// This method is used by the persistence package to complete tree reconstruction
// after JSON unmarshaling.
//
// Returns: Error if reconstruction fails, nil on success.
// Constraints: SerializableRoot must be valid and complete.
// Performance: O(tree_size) for complete tree reconstruction.
// Relationships: Used by persistence package, calls FromSerializable.
// Side effects: Sets Root field from SerializableRoot.
//
// Example: Internal method used during tree loading from persistence.
func (dt *DecisionTree) ReconstructFromSerialization() error {
	if dt.SerializableRoot == nil {
		return fmt.Errorf("no serializable root to reconstruct from")
	}

	// Convert serializable root back to Node interface
	root, err := FromSerializable(dt.SerializableRoot)
	if err != nil {
		return fmt.Errorf("failed to reconstruct root node: %w", err)
	}

	dt.Root = root

	// Validate reconstructed tree
	if !dt.Validate() {
		return fmt.Errorf("reconstructed tree validation failed")
	}

	return nil
}

// GetTreeSummary returns formatted string with tree statistics and metadata.
//
// Returns comprehensive tree information including structure statistics,
// training parameters, and model identification for debugging and analysis.
// Performance: O(tree_depth) for depth calculation plus O(1) for other statistics.
// Relationships: Uses tree statistics and metadata for summary generation.
// Side effects: None, read-only access to tree information.
//
// Example: summary := tree.GetTreeSummary()
func (dt *DecisionTree) GetTreeSummary() string {
	if dt.Root == nil {
		return "Empty Decision Tree"
	}

	return fmt.Sprintf(`Decision Tree Summary:
  Algorithm: %s
  Features: %d
  Classes: %d (%v)
  Total Nodes: %d
  Leaf Nodes: %d
  Tree Depth: %d
  Training Samples: %d
  Max Depth Setting: %d
  Min Samples Setting: %d
  Criterion: %s
  Created: %s`,
		dt.Metadata.Algorithm,
		len(dt.Features),
		len(dt.Classes), dt.Classes,
		dt.Metadata.TotalNodes,
		dt.Metadata.LeafNodes,
		dt.GetDepth(),
		dt.Metadata.TrainingSamples,
		dt.Metadata.MaxDepth,
		dt.Metadata.MinSamples,
		dt.Metadata.Criterion,
		dt.Metadata.CreatedAt.Format("2006-01-02 15:04:05"))
}

// ====================
// Feature and Class Management
// ====================

// GetFeature retrieves feature definition by name.
//
// Args:
//   - name: Feature name to lookup in tree's feature definitions
//
// Returns Feature definition or nil if feature name not found in tree.
// Performance: O(f) where f = number of features for linear search.
// Relationships: Searches tree's feature definitions for specified feature.
// Side effects: None, read-only search through feature list.
//
// Example: feature := tree.GetFeature("age")
func (dt *DecisionTree) GetFeature(name string) *Feature {
	for _, feature := range dt.Features {
		if feature.Name == name {
			return feature
		}
	}
	errors.LogErrorAndReturnSimple(fmt.Sprintf("feature %s not found in tree", name))
	return nil
}

// HasClass checks if target class exists in tree's class definitions.
//
// Args:
//   - class: Target class value to check for existence
//
// Returns true if class is valid target for this tree, false otherwise.
// Performance: O(c) where c = number of classes for linear search.
// Relationships: Checks against tree's defined target classes.
// Side effects: None, read-only search through class list.
//
// Example: isValid := tree.HasClass("approved")
func (dt *DecisionTree) HasClass(class string) bool {
	for _, c := range dt.Classes {
		if c == class {
			return true
		}
	}
	return false
}

// GetFeatureNames returns slice of all feature names used in tree.
//
// Returns feature names in same order as Features slice for consistent iteration.
// Performance: O(f) where f = number of features for slice creation.
// Relationships: Extracts names from tree's feature definitions.
// Side effects: Creates new slice, original Features slice unchanged.
func (dt *DecisionTree) GetFeatureNames() []string {
	names := make([]string, len(dt.Features))
	for i, feature := range dt.Features {
		names[i] = feature.Name
	}
	return names
}
